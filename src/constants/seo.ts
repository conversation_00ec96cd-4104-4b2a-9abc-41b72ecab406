export const LEAD_GEN_SEO_CITIES_LIST: Array<{
  city: string;
  description: string;
}> = [
  {
    city: 'Bangalore',
    description:
      'Bangalore, officially known as Bengaluru, is the capital city of the Indian state of Karnataka. It is known as the Silicon Valley of India due to its thriving IT industry and startup ecosystem.',
  },
  {
    city: 'Delhi',
    description:
      'Delhi, officially the National Capital Territory of Delhi, is a bustling metropolitan area and the capital city of India. It is known for its historic monuments, diverse culture, and vibrant markets.',
  },
  {
    city: 'Chennai',
    description:
      'Chennai, formerly known as Madras, is the capital city of the Indian state of Tamil Nadu. It is a major cultural, economic, and educational hub known for its classical music, dance, and architecture.',
  },
  {
    city: 'Hyderabad',
    description:
      'Hyderabad is the capital city of the Indian state of Telangana and de jure capital of Andhra Pradesh. It is known for its rich history, iconic landmarks like Charminar, and IT industry.',
  },
  {
    city: 'Mumbai',
    description:
      'Mumbai, formerly known as Bombay, is the financial and entertainment capital of India. It is a bustling metropolis with a vibrant nightlife, historic attractions, and the Bollywood film industry.',
  },
  {
    city: 'Pune',
    description:
      "Pune is a city in the Indian state of Maharashtra known for its educational institutions, IT industry, and pleasant climate. It is often referred to as the 'Oxford of the East.'",
  },
  {
    city: 'Kolkata',
    description:
      'Kolkata, formerly Calcutta, is the capital city of the Indian state of West Bengal. It is known for its colonial architecture, artistic heritage, and cultural festivals.',
  },
  {
    city: 'Ahmedabad',
    description:
      'Ahmedabad is the largest city in the Indian state of Gujarat and an important industrial hub. It is renowned for its textile industry, Sabarmati Ashram, and vibrant street food.',
  },
  {
    city: 'Amritsar',
    description:
      'Amritsar is a historic city in the state of Punjab, known for its Sikh heritage and the Golden Temple, a prominent spiritual and cultural site.',
  },
  {
    city: 'Bhopal',
    description:
      'Bhopal is the capital city of the Indian state of Madhya Pradesh. It is known for its picturesque lakes, rich history, and cultural heritage.',
  },
  {
    city: 'Bhubaneswar',
    description:
      'Bhubaneswar is the capital city of the Indian state of Odisha. It is known for its ancient temples, vibrant arts scene, and emerging IT industry.',
  },
  {
    city: 'Chandigarh',
    description:
      'Chandigarh is a union territory and capital of both Punjab and Haryana states. It is a planned city known for its modernist architecture and urban design.',
  },
  {
    city: 'Faridabad',
    description:
      'Faridabad is a major city in the Indian state of Haryana. It is an industrial hub known for its manufacturing sector and proximity to Delhi.',
  },
  {
    city: 'Ghaziabad',
    description:
      'Ghaziabad is a city in the Indian state of Uttar Pradesh, located near Delhi. It is known for its industrial clusters and growing residential areas.',
  },
  {
    city: 'Jamshedpur',
    description:
      'Jamshedpur is a planned industrial city in the Indian state of Jharkhand. It is known for its steel industry, educational institutions, and quality of life.',
  },
  {
    city: 'Jaipur',
    description:
      'Jaipur is the capital city of the Indian state of Rajasthan. It is famous for its historic forts, palaces, vibrant markets, and rich cultural heritage.',
  },
  {
    city: 'Kochi',
    description:
      'Kochi, also known as Cochin, is a major port city in the Indian state of Kerala. It is renowned for its backwaters, spice markets, and coastal beauty.',
  },
  {
    city: 'Lucknow',
    description:
      'Lucknow is the capital city of the Indian state of Uttar Pradesh. It is known for its Nawabi heritage, historic monuments, and rich culinary tradition.',
  },
  {
    city: 'Nagpur',
    description:
      "Nagpur is the third-largest city in the Indian state of Maharashtra. It is known as the 'Orange City' and is a major commercial and political center.",
  },
  {
    city: 'Patna',
    description:
      'Patna is the capital city of the Indian state of Bihar. It is an ancient city located on the banks of the Ganges River, known for its historical significance.',
  },
  {
    city: 'Raipur',
    description:
      'Raipur is the capital city of the Indian state of Chhattisgarh. It is a rapidly developing city with a growing industrial base and cultural diversity.',
  },
  {
    city: 'Surat',
    description:
      'Surat is a major city in the Indian state of Gujarat. It is known for its diamond and textile industries, bustling markets, and vibrant street food.',
  },
  {
    city: 'Visakhapatnam',
    description:
      'Visakhapatnam, also known as Vizag, is a coastal city in the Indian state of Andhra Pradesh. It is famous for its beaches, shipbuilding industry, and natural beauty.',
  },
  {
    city: 'Agra',
    description:
      'Agra is a city in the Indian state of Uttar Pradesh, famous for the iconic Taj Mahal, a UNESCO World Heritage site and one of the Seven Wonders of the World.',
  },
  {
    city: 'Ajmer',
    description:
      'Ajmer is a city in the Indian state of Rajasthan, known for the Dargah Sharif, a Sufi shrine that attracts pilgrims from around the world.',
  },
  {
    city: 'Kanpur',
    description:
      'Kanpur is a major industrial city in the Indian state of Uttar Pradesh. It is known for its leather and textile industries, educational institutions, and historical sites.',
  },
  {
    city: 'Mysuru',
    description:
      'Mysuru (Mysore) is a historic city in the Indian state of Karnataka, known for its royal heritage, palaces, and cultural festivals.',
  },
  {
    city: 'Srinagar',
    description:
      'Srinagar is the largest city and summer capital of the Indian union territory of Jammu and Kashmir. It is renowned for its scenic beauty, gardens, and houseboats.',
  },
];

export const LEAD_GEN_SEO_INDUSTRIES_LIST: Array<{
  industry: string;
  description: string;
  slug: string;
  related?: Array<{
    name: string;
    href: string;
  }>;
}> = [
  {
    industry: 'Healthcare',
    slug: 'healthcare',
    description:
      "The healthcare industry encompasses a wide range of services aimed at maintaining or improving people's health, including hospitals, clinics, pharmaceutical companies, medical devices, health insurance, and more. It involves various professionals such as doctors, nurses, pharmacists, and researchers working together to provide patient care, conduct medical research, and develop treatments. With a growing emphasis on technology and innovation, the healthcare industry is increasingly integrating AI to improve diagnostics, patient management, and operational efficiency.",
    related: [
      {
        href: 'https://groweasy.ai/blogs/comprehensive-guide-to-lead-generation-strategies-and-best-practices-for-2024',
        name: 'Lead generation strategies and best practices',
      },
    ],
  },
  {
    industry: 'Real Estate',
    slug: 'real-estate',
    description:
      'The real estate industry involves the buying, selling, and renting of properties, including residential, commercial, and industrial real estate. Real estate professionals, such as agents, brokers, and property managers, help clients find and secure properties that meet their needs and investment goals. The industry also includes real estate development, where companies build new properties or renovate existing ones. AI is being utilized to streamline processes, enhance market analysis, and improve customer experiences.',
    related: [
      {
        href: 'https://groweasy.ai/blogs/real-estate-lead-generation-strategies',
        name: 'Real estate lead generation strategies',
      },
      {
        href: 'https://groweasy.ai/blogs/understanding-lead-generation-costs-involved-in-real-estate',
        name: 'Lead generation costs involved in real estate',
      },
      {
        href: 'https://groweasy.ai/blogs/heres-how-to-generate-free-real-estate-leads',
        name: 'How to generate free real estate leads',
      },
    ],
  },
  {
    industry: 'Higher Education',
    slug: 'higher-education',
    description:
      'The higher education industry comprises colleges, universities, and other institutions that offer post-secondary education and training. This sector includes a variety of academic programs, ranging from undergraduate degrees to advanced research and professional certifications. Higher education institutions focus on teaching, research, and community service. The integration of AI in higher education is enhancing personalized learning, administrative efficiency, and research capabilities, making education more accessible and effective.',
    related: [
      {
        href: 'https://groweasy.ai/blogs/7-effective-lead-generation-strategies-for-higher-education-institutions',
        name: 'Lead generation strategies for higher education',
      },
    ],
  },
  {
    industry: 'Interior Design',
    slug: 'interior-design',
    description:
      'The interior design industry involves the planning and designing of interior spaces in residential, commercial, and public buildings. Interior designers work to create functional and aesthetically pleasing environments by selecting furnishings, color schemes, lighting, and materials. They often collaborate with architects, contractors, and clients to achieve the desired look and functionality. AI is revolutionizing interior design by providing tools for virtual staging, 3D modeling, and personalized design recommendations, thus streamlining the design process and enhancing creativity.',
    related: [
      {
        href: 'https://groweasy.ai/blogs/6-proven-lead-generation-strategies-in-digital-marketing-',
        name: 'Top Lead generation strategies in digital marketing',
      },
    ],
  },
];

export const WHY_GROWEASY_LEAD_GEN_VALUE_PROPS = [
  {
    title: 'Launch Campaigns in Minutes',
    description:
      'With GrowEasy, launch lead generation campaigns on Facebook and Instagram in just 5 minutes.',
  },
  {
    title: 'AI-Powered Creatives and Copies',
    description:
      'Leverage generative AI to create compelling ad copies, banners, and target optimized audiences specific to <CITY>.',
  },
  {
    title: 'Real-Time Lead Delivery',
    description:
      'Get started with as little as Rs 1000 and start receiving leads within minutes. Get real time leads notifications on Whatsapp and Email',
  },
  {
    title: 'Built-in CRM',
    description:
      'Utilize the built-in CRM to classify your leads, add notes, and follow up.',
  },
];

export const META_TAGS: {
  [path: string]: {
    title: string;
    description: string;
    keywords: string;
    ogImagePath?: string;
  };
} = {
  '/': {
    title:
      'AI-Powered Lead Generation & Ad Campaigns | Boost ROAS with GrowEasy',
    description:
      'Launch AI-driven ad campaigns on Facebook, Google, and Instagram in just 5 minutes with GrowEasy. Automate lead generation, optimize ads, and boost your return on ad spend effortlessly.',
    keywords:
      'AI marketing platform, lead generation, AI ad campaigns, ROAS, GrowEasy, Facebook ads, Google ads, Instagram ads, ad optimization, marketing automation, AI copywriting, AI graphic design, real-time analytics, 24/7 campaign optimization, automated budget allocation',
  },
  '/about-us': {
    title: 'About GrowEasy | AI Marketing Platform for High-ROAS Campaigns',
    description:
      'Learn more about GrowEasy, the AI-powered platform designed to help businesses and agencies launch high-conversion ad campaigns on Google, Facebook, and Instagram in just 5 minutes.',
    keywords:
      'About GrowEasy, AI marketing platform, AI ad campaigns, ROAS, digital marketing automation, lead generation, company information, AI for marketing',
  },
  '/faqs': {
    title: 'GrowEasy FAQs | Your Questions About AI Marketing Answered',
    description:
      "Have questions about GrowEasy's AI-driven platform? Find answers to commonly asked questions about launching campaigns, lead generation, and maximizing ROAS.",
    keywords:
      'GrowEasy FAQs, AI marketing FAQs, ad campaign automation, lead generation, digital marketing platform, AI for ads, campaign optimization',
  },
  '/login': {
    title: 'Login to GrowEasy | Manage AI-Powered Ad Campaigns',
    description:
      'Login to GrowEasy and start creating AI-powered ad campaigns across Facebook, Google, and Instagram. Automate your lead generation and boost ROAS effortlessly.',
    keywords:
      'GrowEasy login, AI marketing platform login, ad campaign management, AI ad campaigns, lead generation, digital marketing automation, ROAS',
  },
  '/blogs': {
    title: 'GrowEasy Blog | AI Marketing Insights & Strategies',
    description:
      'Explore the GrowEasy blog for the latest insights, tips, and strategies on using AI to drive lead generation, optimize ad campaigns, and maximize ROAS.',
    keywords:
      'GrowEasy blog, AI marketing insights, digital marketing strategies, lead generation tips, campaign optimization, ROAS improvement, AI ad campaigns',
  },
  '/privacy-policy': {
    title: 'Privacy Policy | GrowEasy',
    description:
      "Read GrowEasy's Privacy Policy to understand how we collect, use, and protect your personal information.",
    keywords:
      'privacy policy, data protection, personal information, GrowEasy, AI lead generation, Facebook ads, Instagram ads, digital advertising, online marketing, AI marketing',
  },
  '/terms-and-conditions': {
    title: 'Terms and Conditions | GrowEasy',
    description:
      "Review GrowEasy's Terms and Conditions governing the use of our AI-powered lead generation services on Facebook and Instagram.",
    keywords:
      'terms and conditions, legal terms, GrowEasy, AI lead generation, Facebook ads, Instagram ads, digital advertising, online marketing, AI marketing',
  },
  '/order-social-media-videos-ads': {
    title: 'Social Media Video Ad Generation | GrowEasy',
    description:
      'Generate high-converting video ad scripts and stunning video ads for your business or brand with professional actors and editors. Fast turnaround in just a few days.',
    keywords:
      'video ads, custom video ads, video marketing, social media advertising, professional video creation, high-conversion video ads, digital video marketing, personalized video ads, video ad optimization',
    ogImagePath: '/images/groweasy-videos/groweasy-video-og-image.png',
  },
  '/order-social-media-videos-ads/order': {
    title: 'Order Your Custom Social Media Video Ads | GrowEasy',
    description:
      'Place your order for custom video ads created by professional actors and editors, optimized for social media success and high conversions. Fast and efficient service.',
    keywords:
      'order video ads, custom video ads, video marketing, social media advertising, professional video ads, video ad creation, custom video marketing, order video ads, high-conversion video ads',
    ogImagePath: '/images/groweasy-videos/groweasy-video-og-image.png',
  },
  '/digital-marketing-master-class': {
    title: 'Digital Marketing Masterclass for Startup Founders & SMBs',
    description:
      'Join Tej Pandya’s Digital Marketing Masterclass. Learn proven marketing strategies tailored for startups, small businesses, and freelancers. Reserve your spot today for ₹999!',
    keywords:
      'Digital marketing masterclass, startup growth strategies, online marketing course for entrepreneurs, Tej Pandya digital marketing, growth marketing for startups, affordable marketing strategies, business growth masterclass, marketing fundamentals for startups, GrowEasy masterclass.',
    ogImagePath:
      '/images/digital-marketing-masterclass/groweasy-masterclass-og-image.png',
  },
};

export const FOOTER_NAVLINKS = [
  // {
  //   label: 'About Us',
  //   href: '/about-us',
  // },
  // {
  //   label: 'FAQs',
  //   href: '/faqs',
  // },
  {
    label: 'Privacy Policy',
    href: '/privacy-policy',
  },
  {
    label: 'Terms & Conditions',
    href: '/terms-and-conditions',
  },
  {
    label: 'Legacy Banner Maker',
    href: '/banner-maker',
  },
  {
    label: 'Legacy Video Maker',
    href: '/video-maker',
  },
  {
    label: 'GrowEasy',
    href: 'https://groweasy.ai/',
  },
  // {
  //   label: 'Cancellation & Refund Policy',
  //   href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/refund',
  // },
  // {
  //   label: 'Shipping & Delivery Policy',
  //   href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/shipping',
  // },
  // {
  //   label: 'Payment Privacy Policy',
  //   href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/privacy',
  // },
  // {
  //   label: 'Payment Terms & Conditions',
  //   href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/terms',
  // },
  // {
  //   label: 'Contact Us',
  //   href: 'https://merchant.razorpay.com/policy/NGypnJPIsknIYb/contact_us',
  // },
  {
    label: 'Linkedin',
    href: 'https://www.linkedin.com/company/groweasy-ai',
  },
  {
    label: 'Download Android app on PlayStore',
    href: 'https://play.google.com/store/apps/details?id=xyz.bannerbot',
  },
  // {
  //   label: 'Download iOS app on AppStore',
  //   href: 'https://apps.apple.com/us/app/groweasy-ai-lead-generation/id6701542430',
  // },
  // {
  //   label: 'GrowEasy Pricing',
  //   href: '/pricing',
  // },
  // {
  //   label: 'GrowEasy Blogs',
  //   href: '/blogs',
  // },
  // {
  //   label: 'Instagram',
  //   href: 'https://www.instagram.com/groweasy_ai/',
  // },
  // {
  //   label: 'Youtube',
  //   href: 'https://www.youtube.com/@groweasy-ai',
  // },
  // {
  //   label: 'BannerBot',
  //   href: 'https://groweasy.ai/blogs/how-to-generate-free-banners-and-videos-using-ai',
  // },
  // {
  //   label: 'Order Social Media Videos',
  //   href: '/order-social-media-videos-ads',
  // },
  // {
  //   label: 'Digital Marketing Masterclass',
  //   href: '/digital-marketing-master-class',
  // },
  // {
  //   label: 'Facebook Audience Builder',
  //   href: '/tools/facebook-ads-audience-builder',
  // },
  // {
  //   label: 'Google Keyword Ideas Generator',
  //   href: '/tools/google-keyword-ideas-generator',
  // },
];
