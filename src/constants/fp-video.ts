import { IFpVideoData, IFpVideoDataV1, IFpVideoDataV2 } from '@/remotion/types';
import { P10_TEMPLATE_EXAMPLE_SUBTITLE } from './subtitle';

// depricated
export const TEMPLATES_CONFIG: Record<
  IFpVideoData['template_id'],
  { fps: number; durationInSec: number }
> = {
  p1: {
    // temp
    durationInSec: 20,
    fps: 30,
  },
  p2: {
    // temp
    durationInSec: 20,
    fps: 30,
  },
  p3: {
    durationInSec: 21.1,
    fps: 60,
  },
  p4: {
    durationInSec: 18,
    fps: 30,
  },
  p5: {
    durationInSec: 21,
    fps: 30,
  },
  p6: {
    durationInSec: 15,
    fps: 30,
  },
  p7: {
    durationInSec: 21,
    fps: 30,
  },
  p8: {
    durationInSec: 19,
    fps: 30,
  },
  p9: {
    durationInSec: 20,
    fps: 30,
  },
  p10: {
    durationInSec: 20,
    fps: 30,
  },
  p11: {
    durationInSec: 20,
    fps: 30,
  },
};

export const P9_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption: 'Maximize Your Ad ROI with Targeted Campaigns!',
  fps: 30,
  template_id: 'p9',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'AdVantage',
    website: 'www.advantageads.com',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
  },
  base_audio: {
    url: 'https://groweasy-public.s3.ap-south-1.amazonaws.com/uploaded-assets/apt-bgm.mp3',
  },
  scenes: [
    {
      scene_id: 1,
      texts: [
        {
          value: 'Struggling to get the most out of your ad campaigns?',
        },
      ],
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/7660169/7660169-sd_360_640_25fps.mp4',
        },
      ],
      texts: [
        {
          value: 'Precision Targeting for Higher Conversions',
        },
        {
          value:
            'Reach your ideal customers with laser-focused targeting options. Stop wasting money on irrelevant clicks!',
        },
      ],
    },
    {
      scene_id: 3,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/5520073/5520073-sd_360_640_30fps.mp4',
        },
      ],
      texts: [
        {
          value: 'Data-Driven Optimization',
        },
        {
          value:
            'Real-time analytics and AI-powered insights to fine-tune your campaigns for maximum impact.',
        },
      ],
    },
    {
      scene_id: 4,
      texts: [
        {
          value: '+911234567890 / www.getawaystays.co.in',
        },
      ],
    },
  ],
};

export const P8_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p8',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
  },
  base_audio: {
    url: 'https://groweasy-public.s3.ap-south-1.amazonaws.com/uploaded-assets/apt-bgm.mp3',
  },
  scenes: [
    {
      scene_id: 1,

      texts: [
        {
          value:
            'Are you looking for simple and efficient ways to manage your leads?',
        },
      ],
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'image',
          url: 'https://assets.lummi.ai/assets/QmQJiATwtUkkSkVyEUaWD75tKHTxqeeHSFR6cvzaNAeoTK?auto=format&w=800&h=800',
        },
      ],
      texts: [
        {
          value: 'AI Powered lead generation',
        },
        {
          value:
            'GrowEasy is an AI-powered lead generation tool that helps you find and connect with potential customers effortlessly.',
        },
      ],
    },
    {
      scene_id: 3,
      assets: [
        {
          type: 'image',
          url: 'https://assets.lummi.ai/assets/QmewrwKjW6aF1L3wGRyYnwd8tX5XzF2wBMhVk5bn9DBCc4?auto=format&w=800&h=800',
        },
      ],
      texts: [
        {
          value: 'Quick and Easy Setup',
        },
        {
          value:
            'PSetup high conversion campaigns in minutes with our user-friendly interface and powerful tools.',
        },
      ],
    },
    {
      scene_id: 4,
      assets: [
        {
          type: 'image',
          url: 'https://assets.lummi.ai/assets/QmaeftvpWX3AXPanu3X57T3zgGeY2WA4QvFpg8nyfa8NHK?auto=format&w=800&h=800',
        },
      ],
      texts: [
        {
          value: 'Maximize Your ROI',
        },
        {
          value:
            'Optimize your ad spend and increase your return on investment with our advanced targeting and analytics tools.',
        },
      ],
    },
    {
      scene_id: 5,
      texts: [
        {
          value: '+911234567890 / www.getawaystays.co.in',
        },
      ],
    },
  ],
};

export const P7_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p7',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
  },
  base_audio: {
    url: 'https://ia800704.us.archive.org/19/items/bgm_20250331/bgm.mp3?cnt=0',
  },
  scenes: [
    {
      scene_id: 1,
      assets: [
        {
          type: 'image',
          url: 'https://image.lexica.art/full_webp/08872788-31fd-4361-91a2-75b718105903',
        },
      ],
      texts: [
        {
          value: 'Looking for the perfect luxury gateway?',
        },
      ],
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'image',
          url: 'https://image.lexica.art/full_webp/06d17799-b9f8-4b1d-ad01-7e04158e401d',
        },
      ],
      texts: [
        {
          value: 'Premium 4BR Pool Villa Near Mandwa Jetty',
        },
        {
          value:
            'Welcome to Hearthstone by Getaway Stays - an exclusive 4 bedroom luxury villa with a private pool.',
        },
      ],
    },
    {
      scene_id: 3,
      assets: [
        {
          type: 'image',
          url: 'https://image.lexica.art/full_webp/133afbce-8d06-4c12-b4b8-bd6c4aa015e2',
        },
      ],
      texts: [
        {
          value: 'Ideal for Families & Corporate Retreats!',
        },
        {
          value:
            'Perfect for familty vacation, corporate offistes, weekend gateways, and private pool parties!',
        },
      ],
    },
    {
      scene_id: 4,
      assets: [
        {
          type: 'image',
          url: 'https://image.lexica.art/full_webp/2da2fb0c-13fe-4826-b778-901b34c152f0',
        },
      ],
      texts: [
        {
          value: 'Book Your Stay Now!',
        },
        {
          value:
            'Enjoy world-class comfort, scenic surroundings, and a peaceful retreat just a short ride from Mumbai.',
        },
      ],
    },
    {
      scene_id: 5,
      assets: [
        {
          type: 'image',
          url: 'https://image.lexica.art/full_webp/44c2ed57-3401-4c99-940d-a8bfa87efd80',
        },
      ],
      texts: [
        {
          value: '+911234567890 / www.getawaystays.co.in',
        },
      ],
    },
  ],
};

export const P6_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p6',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
  },
  base_audio: {
    url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/bg-musics/upbeat-reel-commercial-advertising-music-253432.mp3',
  },
  base_assets: [
    {
      type: 'video',
      url: 'https://videos.pexels.com/video-files/8348725/8348725-hd_720_1280_25fps.mp4',
    },
  ],
  scenes: [
    {
      scene_id: 1,
      texts: [
        {
          value: 'Just Listed',
        },
        {
          value: 'Los Angeles, CA 90045',
        },
        {
          value: 'Call (123) 555-1234 to arrange a viewing today',
        },
      ],
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'image',
          url: 'https://images.pexels.com/photos/31103901/pexels-photo-31103901/free-photo-of-city-waterfront-view-with-iconic-bridge.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        },
      ],
      texts: [
        {
          value: 'My Brand Realtors',
        },
        {
          value: '(123) 555-1234',
        },
        {
          value: '<EMAIL>',
        },
      ],
    },
  ],
};

export const P5_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p5',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'Easemyexpo',
    website: 'www.groweasy.ai',
    // logo: {
    //   url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
    //   width: 100,
    //   height: 100,
    // },
  },
  base_audio: {
    url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/bg-musics/upbeat-reel-commercial-advertising-music-253432.mp3',
  },
  scenes: [
    {
      scene_id: 1,
      texts: [
        {
          value: 'Automate your data extraction',
        },
        {
          value: 'Save 100 hours monthly!',
        },
      ],
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'image',
          url: 'https://groweasy.ai/cdn-cgi/image/quality=100,fit=cover/https://img.b2bpic.net/premium-photo/business-people-computer-communication-collaboration-idea-office-teamwork-colleagues-happy-reading-information-website-creative-agency-support-proposal-project_590464-312663.jpg',
        },
      ],
    },
    {
      scene_id: 3,
      assets: [
        {
          type: 'image',
          url: 'https://groweasy.ai/cdn-cgi/image/quality=100,fit=cover/https://img.b2bpic.net/premium-photo/doctor-nurse-group-planning-tablet-healthcare-research-workflow-collaboration-re_612827-6052.jpg',
        },
      ],
    },
    {
      scene_id: 4,
      assets: [
        {
          type: 'image',
          url: 'https://groweasy.ai/cdn-cgi/image/quality=100,fit=cover/https://img.b2bpic.net/premium-photo/man-woman-are-looking-computer-screen-that-says-company_1314467-2081.jpg',
        },
      ],
    },
    {
      scene_id: 5,
      texts: [
        {
          value: 'Upload floor plans and enrich company details',
        },
      ],
    },
  ],
};

export const P4_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p4',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
  },
  base_audio: {
    url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/bg-musics/upbeat-reel-commercial-advertising-music-253432.mp3',
  },
  base_assets: [
    {
      type: 'video',
      url: 'https://videos.pexels.com/video-files/8348725/8348725-hd_720_1280_25fps.mp4',
      video_duration: 12,
    },
  ],
  scenes: [
    {
      scene_id: 1,
      texts: [
        {
          value: 'Looking for the perfect luxury gateway?',
        },
      ],
    },
    {
      scene_id: 2,
      texts: [
        {
          value: 'Premium 4BR Pool Villa Near Mandwa Jetty',
        },
        {
          value:
            'Welcome to Hearthstone by Getaway Stays - an exclusive 4 bedroom luxury villa with a private pool.',
        },
      ],
    },
    {
      scene_id: 3,
      texts: [
        {
          value: 'Ideal for Families & Corporate Retreats!',
        },
        {
          value:
            'Perfect for familty vacation, corporate offistes, weekend gateways, and private pool parties!',
        },
      ],
    },
    {
      scene_id: 4,
      texts: [
        {
          value: 'Book Your Stay Now!',
        },
        {
          value:
            'Enjoy world-class comfort, scenic surroundings, and a peaceful retreat just a short ride from Mumbai.',
        },
      ],
    },
    {
      scene_id: 5,
      texts: [
        {
          value: '+911234567890 / www.getawaystays.co.in',
        },
      ],
    },
  ],
};

export const P3_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV1 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 60,
  template_id: 'p3',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
  },
  base_audio: {
    url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/bg-musics/upbeat-reel-commercial-advertising-music-253432.mp3',
  },
  base_assets: [
    {
      type: 'video',
      url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/17955818-sd_540_960_30fps.mp4',
    },
  ],
  scenes: [
    {
      scene_id: 1,
      assets: [
        {
          type: 'image',
          url: 'https://images.pexels.com/photos/19090/pexels-photo.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        },
      ],
      texts: [
        {
          value: 'Efficient Keyword Planning',
        },
      ],
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'image',
          url: 'https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        },
      ],
      texts: [
        {
          value: 'Enhance Ad Targeting',
        },
      ],
    },
    {
      scene_id: 3,
      assets: [
        {
          type: 'image',
          url: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        },
      ],
      texts: [
        {
          value: 'Seamless Campaign Retrieval',
        },
      ],
    },
    {
      scene_id: 4,
      assets: [
        {
          type: 'image',
          url: 'https://images.pexels.com/photos/292999/pexels-photo-292999.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        },
      ],
      texts: [
        {
          value: 'Streamlined Management',
        },
      ],
    },
    {
      scene_id: 5,
      assets: [],
      texts: [
        {
          value: 'Start Optimizing Today',
        },
        {
          value: 'Visit: www.groweasy.ai',
        },
      ],
    },
    {
      scene_id: 6,
      assets: [
        {
          type: 'image',
          url: 'https://images.pexels.com/photos/1478442/pexels-photo-1478442.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
        },
      ],
      texts: [
        {
          value: 'Access Advanced Tools',
        },
      ],
    },
    {
      scene_id: 7,
      texts: [
        {
          value: 'AI Powered lead generation',
        },
        {
          value: 'https://groweasy.ai',
        },
      ],
    },
  ],
};

export const P10_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV2 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p10',
  size: 'portrait',
  width: 720,
  height: 1280,
  base_audio: {
    url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/audios/1751378525859-769eb4d6-3df3-4e3f-af2c-b660644c6ca3.mp3',
    subtitle: P10_TEMPLATE_EXAMPLE_SUBTITLE,
  },
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
    mobile: '',
  },
  scenes: [
    {
      scene_id: 1,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/7106859/7106859-hd_720_1280_30fps.mp4',
        },
      ],
      start: 0,
      end: 3.32,
    },
    {
      scene_id: 2,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/7579599/7579599-hd_1080_1920_25fps.mp4',
        },
      ],
      start: 3.32,
      end: 4.559,
    },
    {
      scene_id: 3,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/9057186/9057186-hd_720_1280_25fps.mp4',
        },
      ],
      start: 4.559,
      end: 6.879,
    },
    {
      scene_id: 4,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/4613102/4613102-hd_720_1366_50fps.mp4',
        },
      ],
      start: 6.879,
      end: 18.319,
    },
    {
      scene_id: 5,
      assets: [
        {
          type: 'video',
          url: 'https://videos.pexels.com/video-files/4883868/4883868-hd_1080_1920_30fps.mp4',
        },
      ],
      start: 18.319,
      end: 20.899,
    },
  ],
};

export const P11_TEMPLATE_EXAMPLE_VALUE: IFpVideoDataV2 = {
  video_caption:
    'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
  fps: 30,
  template_id: 'p11',
  size: 'portrait',
  width: 720,
  height: 1280,
  branding: {
    brand_name: 'GrowEasy',
    website: 'www.groweasy.ai',
    logo: {
      url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
      width: 100,
      height: 100,
    },
    mobile: '',
  },
  base_audio: {
    url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/audios/1751378525859-769eb4d6-3df3-4e3f-af2c-b660644c6ca3.mp3',
    subtitle: P10_TEMPLATE_EXAMPLE_SUBTITLE,
  },
  base_assets: [
    {
      type: 'video',
      url: 'https://videos.pexels.com/video-files/9943179/9943179-hd_720_1366_25fps.mp4',
    },
  ],
  scenes: [
    {
      scene_id: 1,
      texts: [
        {
          value: 'मनाली टूर पैकेज',
        },
        {
          value: 'हनीमून पैकेज',
        },
        {
          value: 'स्पिती घाटी यात्रा',
        },
        {
          value: 'परिवार का पैकेज',
        },
      ],
      start: 0,
      end: 20,
    },
  ],
};
