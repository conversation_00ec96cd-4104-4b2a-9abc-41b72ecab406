import React from 'react';
import BackgroundCircles from '../landing-page/BackgroundCircles';
import classNames from 'classnames';

interface IDashboardCards extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  backgroundCirlesSize?: number;
  backgroundCirlesColorClassName?: string;
  className?: string;
  innerClassName?: string;
  selectable?: boolean;
  selected?: boolean;
}

const DashboardCard = (props: IDashboardCards) => {
  const {
    children,
    backgroundCirlesColorClassName = 'bg-[#FC5185]/10',
    backgroundCirlesSize,
    className,
    innerClassName,
    selectable = false,
    selected = false,
    ...rest
  } = props;
  return (
    <div
      className={classNames([
        ' relative p-4 md:p-6 overflow-hidden bg-[#2F1C6A] rounded-lg',
        className,
        {
          'hover:ring-2 hover:shadow-xl hover:shadow-white/5 ring-white/40 transition-all duration-150 cursor-pointer':
            selectable,
        },
        {
          'ring-2 shadow-xl shadow-white/15 ring-white/80 cursor-pointer':
            selected,
        },
      ])}
      {...rest}
    >
      <BackgroundCircles
        size={backgroundCirlesSize}
        colorClassName={backgroundCirlesColorClassName}
        className=" left-0 top-0 -translate-x-1/2 -translate-y-1/2  "
      />
      <div className={` relative ${innerClassName ?? ''} `}>{children}</div>
    </div>
  );
};

export default DashboardCard;
