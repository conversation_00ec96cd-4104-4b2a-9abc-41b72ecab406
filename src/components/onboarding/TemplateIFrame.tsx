import React, { useEffect, useMemo, useRef, useState } from 'react';
import SpinnerLoader from '../lib/SpinnerLoader';
import { FaPlay } from 'react-icons/fa6';
import { IFpVideoData } from '@/remotion/types';

interface fpWindow extends Window {
  pageTimeline: {
    pause: () => void;
    play: () => void;
    duration: () => number;
    seek: (time: number) => void;
    // progress: (amount: number) => void;
    eventCallback: (event: string, callback: () => void) => void;
  };
  gsap: {
    globalTimeline: {
      pause: () => void;
    };
  };
  init: (data: string) => void;
}

interface ITemplateIFrameProps {
  isIFrameLoading: boolean;
  setIsIFrameLoading: React.Dispatch<React.SetStateAction<boolean>>;
  videoTemplateData: IFpVideoData;
}

const TemplateIFrame = (props: ITemplateIFrameProps) => {
  const { setIsIFrameLoading, isIFrameLoading, videoTemplateData } = props;
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [showVideoControls, setShowVideoControls] = useState(true);
  const [videoOptions, setVideoOptions] = useState({
    isPlaying: false,
  });
  const [iFrameWindow, setIFrameWindow] = useState<fpWindow>();

  const url = useMemo(
    () =>
      process.env.NODE_ENV !== 'production'
        ? `http://localhost:3000/fp-videos/${videoTemplateData.template_id}`
        : `https://designeasy.ai/fp-videos/${videoTemplateData.template_id}`,
    [videoTemplateData],
  );

  const play = () => {
    if (!iframeRef.current) return;
    // const window = iframeRef.current?.contentWindow as fpWindow;
    // window.pageTimeline.play();
    iFrameWindow?.pageTimeline.play();
    setVideoOptions((prev) => ({ ...prev, isPlaying: true }));
  };

  const pause = () => {
    if (!iframeRef.current) return;
    // const window = iframeRef.current?.contentWindow as fpWindow;
    // window.pageTimeline.pause();
    iFrameWindow?.pageTimeline.pause();
    setVideoOptions((prev) => ({ ...prev, isPlaying: false }));
  };

  const toggeleVideoPlayback = () => {
    if (videoOptions.isPlaying) {
      pause();
    } else {
      play();
    }
  };

  useEffect(() => {
    if (iframeRef.current) {
      iframeRef.current.onload = async () => {
        await new Promise((res) =>
          setTimeout(() => {
            res(1);
          }, 500),
        );

        setIsIFrameLoading(false);

        const window = iframeRef.current?.contentWindow as fpWindow;

        window.pageTimeline.pause();
        window.pageTimeline.seek(0);

        window.pageTimeline.eventCallback('onComplete', () => {
          window.pageTimeline.pause();
          setVideoOptions((prev) => ({ ...prev, isPlaying: false }));
          window.pageTimeline.seek(0);
        });

        setIFrameWindow(window);
      };
    }
  }, [videoTemplateData, setIsIFrameLoading]);

  useEffect(() => {
    if (iFrameWindow) {
      iFrameWindow.init(JSON.stringify(videoTemplateData));
      setVideoOptions((prev) => ({ ...prev, isPlaying: false }));
      // iFrameWindow.pageTimeline.pause();
    }
  }, [iFrameWindow, videoTemplateData]);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (showVideoControls) {
      timeoutId = setTimeout(() => {
        setShowVideoControls(false);
      }, 1000);
    }
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [showVideoControls]);

  return (
    <div className=" relative w-[300px] h-[534px] overflow-hidden rounded-xl ">
      {isIFrameLoading && (
        <div className=" absolute z-10 inset-0 bg-white/20 flex items-center justify-center ">
          <SpinnerLoader />
        </div>
      )}
      {/* <div className=" absolute w-full h-[80px] bg-gradient-to-t from-40% from-black/95 to-transparent bottom-0 z-[5] ">
        <div></div>
        </div> */}
      <div
        className={` ${isIFrameLoading ? ' opacity-0 ' : ' cursor-pointer '} `}
        onClick={toggeleVideoPlayback}
      >
        {!videoOptions.isPlaying && (
          <div className=" absolute z-10 inset-0 flex items-center justify-center ">
            <div className=" p-6 rounded-full bg-black/70 ">
              <FaPlay size={28} />
            </div>
          </div>
        )}
        <iframe
          ref={iframeRef}
          src={url}
          width={720}
          height={1280}
          title="video template"
          className=" scale-[0.416] origin-top-left select-none pointer-events-none "
        />
      </div>
    </div>
  );
};

export default TemplateIFrame;
