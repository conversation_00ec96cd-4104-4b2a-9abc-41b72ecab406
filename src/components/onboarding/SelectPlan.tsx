import React from 'react';
import Button from '../lib/Button';
import { HiCheck } from 'react-icons/hi';
import TextV2 from '../lib/typography/TextV2';
import HeadingV2 from '../lib/typography/HeadingV2';
import DashBoardCards from './DashboardCard';

// Pricing data object to store all plan information
const PRICING_DATA = {
  starter: {
    name: 'Starter',
    originalPrice: 199,
    currentPrice: 99,
    buttonVariant: 'outline',
    featuresTitle: 'Include:',
    featuresHighlight: 'Essential',
    features: ['AI-Generated Design', 'Unlimited Downloads'],
  },
  pro: {
    name: 'Pro',
    originalPrice: 599,
    currentPrice: 299,
    buttonVariant: 'filled',
    featuresTitle: 'IN "STARTER", PLUS',
    featuresHighlight: 'Everything',
    features: ['Human Designer Add-On'],
  },
};

const PriceTag = ({
  originalPrice,
  currentPrice,
}: {
  originalPrice: number;
  currentPrice: number;
}) => (
  <div className="mt-6">
    <TextV2 level={2} className="text-gray-2">
      $ {originalPrice}
    </TextV2>
    <TextV2 level={2} className=" mt-2">
      <span>$</span>
      <span className="text-2xl md:text-4xl font-semibold">
        {currentPrice}.00
      </span>
      <span>/mo</span>
    </TextV2>
  </div>
);

// Component for displaying features
const PlanFeatures = ({
  title,
  highlight,
  features,
}: {
  title: string;
  highlight: string;
  features: Array<string>;
}) => (
  <div>
    <HeadingV2 level={3} className="uppercase font-semibold">
      {highlight} {title}
    </HeadingV2>
    <div className=" mt-5 flex flex-col gap-4 ">
      {features.map((feature, index) => (
        <div key={index} className="flex items-center gap-4 ">
          <HiCheck className="text-[32px] md:text-[42px] text-green-1" />
          <TextV2 level={4}>{feature}</TextV2>
        </div>
      ))}
    </div>
  </div>
);

interface IPricingCardProps {
  name: string;
  originalPrice: number;
  currentPrice: number;
  buttonVariant: string;
  featuresTitle: string;
  featuresHighlight: string;
  features: Array<string>;
}

const PricingCard = (props: IPricingCardProps) => {
  const {
    name,
    originalPrice,
    currentPrice,
    buttonVariant,
    featuresTitle,
    featuresHighlight,
    features,
  } = props;
  return (
    <DashBoardCards className=" p-8 ">
      <HeadingV2 level={2} className="font-semibold">
        {name}
      </HeadingV2>

      <PriceTag originalPrice={originalPrice} currentPrice={currentPrice} />

      <Button
        className={`w-full mt-6`}
        variant={buttonVariant === 'filled' ? 'default' : 'outline'}
      >
        Choose Plan
      </Button>

      <hr className="my-8 border-blue-2" />

      <PlanFeatures
        title={featuresTitle}
        highlight={featuresHighlight}
        features={features}
      />
    </DashBoardCards>
  );
};

const SelectPlan = () => {
  return (
    <div className=" max-w-5xl mx-auto ">
      <div className="">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 ">
          <PricingCard {...PRICING_DATA.starter} />
          <PricingCard {...PRICING_DATA.pro} />
        </div>
      </div>
    </div>
  );
};

export default SelectPlan;
