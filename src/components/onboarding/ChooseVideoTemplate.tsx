import React, { useEffect, useRef, useState } from 'react';
import DashboardCard from './DashboardCard';
import HeadingV2 from '../lib/typography/HeadingV2';
import Button from '../lib/Button';
import { FaArrowLeft, FaArrowRight } from 'react-icons/fa6';
import { useOnboarding } from '../context/OnboardingProvider';
import { OnboardingStepIds } from '@/types/onBoarding';
import SpinnerLoader from '../lib/SpinnerLoader';
import Image from 'next/image';

const templateIds = [
  'p3',
  'p9',
  'p4',
  'p11',
  'p10',
  'p5',
  'p6',
  'p7',
  'p8',
] as const;
const newTemplateIds = ['p10', 'p11'];

const ChooseVideoTemplate = () => {
  const { saveFpVideoDetails } = useOnboarding();
  const templateCarouselRef = useRef<HTMLDivElement>(null);
  const [centerTemplateItemIndex, setCenterTemplateItemIndex] = useState(
    Math.floor(templateIds.length / 2),
  );
  const videoRefs = useRef<Array<HTMLVideoElement | null>>([]);
  const isScrollingRef = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to scroll to a specific index
  const scrollToIndex = (
    index: number,
    props: { behavior: 'smooth' | 'instant' } = { behavior: 'smooth' },
  ) => {
    const { behavior } = props;
    const parent = templateCarouselRef.current;
    if (!parent) return;

    const childTemplate = parent.querySelectorAll('.template-card')[index];
    if (!childTemplate) return;

    isScrollingRef.current = true;

    const parentRect = parent.getBoundingClientRect();
    const childRect = childTemplate.getBoundingClientRect();

    // Calculate the scroll position needed to center the child
    const scrollLeft =
      parent.scrollLeft +
      (childRect.left - parentRect.left) -
      (parentRect.width / 2 - childRect.width / 2);

    parent.scrollTo({ left: scrollLeft, behavior });

    setCenterTemplateItemIndex(index);

    if (timeoutRef.current) window.clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      isScrollingRef.current = false;
    }, 500); // Slightly longer than CSS transition
  };

  const scrollLeft = () => {
    const newIndex = Math.max(0, centerTemplateItemIndex - 1);
    scrollToIndex(newIndex);
  };

  const scrollRight = () => {
    const childrenCount = templateIds.length;
    const newIndex = Math.min(childrenCount - 1, centerTemplateItemIndex + 1);
    scrollToIndex(newIndex);
  };

  // Handle manual scrolling to detect centered item
  useEffect(() => {
    const handleScroll = () => {
      if (isScrollingRef.current) return;

      const parent = templateCarouselRef.current;
      if (!parent) return;

      if (timeoutRef.current) clearTimeout(timeoutRef.current);

      timeoutRef.current = setTimeout(() => {
        const children = Array.from(parent.querySelectorAll('.template-card'));
        const parentMiddle = parent.getBoundingClientRect().width / 2;

        let closestIndex = 0;
        let closestDistance = Infinity;

        children.forEach((child, index) => {
          const rect = child.getBoundingClientRect();
          const childMiddle = rect.left + rect.width / 2;
          const distance = Math.abs(
            childMiddle - (parent.getBoundingClientRect().left + parentMiddle),
          );

          if (distance < closestDistance) {
            closestDistance = distance;
            closestIndex = index;
          }
        });

        if (closestIndex !== centerTemplateItemIndex) {
          setCenterTemplateItemIndex(closestIndex);
        }
      }, 0);
    };

    const parent = templateCarouselRef.current;
    if (parent) {
      parent.addEventListener('scroll', handleScroll, { passive: true });
    }

    return () => {
      if (parent) {
        parent.removeEventListener('scroll', handleScroll);
      }
    };
  }, [centerTemplateItemIndex]);

  useEffect(() => {
    videoRefs.current.forEach((video, index) => {
      if (video) {
        if (index === centerTemplateItemIndex) {
          video.play().catch(() => {});
        } else {
          video.pause();
        }
      }
    });
  }, [centerTemplateItemIndex]);

  useEffect(() => {
    scrollToIndex(Math.floor((templateIds.length - 1) / 2), {
      behavior: 'instant',
    });
  }, []);

  return (
    <div>
      <DashboardCard>
        <div className=" space-y-6 ">
          <HeadingV2 level={3}>Choose your templates</HeadingV2>
          <div className=" flex items-center gap-4 relative ">
            <div className=" z-10 absolute left-0 drop-shadow-md ">
              <button
                className=" outline-none p-4 bg-off-white text-black rounded-full cursor-pointer disabled:bg-off-white/50 "
                onClick={scrollLeft}
                disabled={centerTemplateItemIndex === 0}
              >
                <FaArrowLeft />
              </button>
            </div>
            <div className=" flex-1 flex justify-center items-center overflow-hidden ">
              <div
                ref={templateCarouselRef}
                className=" flex gap-36 snap-x snap-mandatory overflow-x-auto items-center px-[1000px] no-scrollbar py-20 "
              >
                {templateIds.map((templateId, index) => (
                  <div
                    key={index}
                    className={` relative snap-center shrink-0 transition-all rounded-lg w-[150px] h-[268px] ${centerTemplateItemIndex === index ? ' scale-125 duration-300 ease-out ' : 'duration-0'} `}
                  >
                    {newTemplateIds.includes(templateId) && (
                      <Image
                        src="/images/icons/new.svg"
                        width={44}
                        height={44}
                        alt="new icon"
                        className=" absolute -top-3 -left-3 z-50 "
                      />
                    )}
                    <div
                      className=" relative z-[1] template-card"
                      onClick={() => scrollToIndex(index)}
                    >
                      <video
                        ref={(el) => {
                          videoRefs.current[index] = el;
                        }}
                        width={150}
                        height={268}
                        muted
                        onLoadedMetadata={(e) => {
                          e.currentTarget.currentTime = 1;
                        }}
                        className=" rounded-lg "
                      >
                        <source
                          src={`/videos/fp-template/${templateId}.mp4`}
                          type="video/mp4"
                        />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                    <div className=" absolute top-0 left-0 w-full h-full flex items-center justify-center bg-gray-400 rounded-lg ">
                      <SpinnerLoader size={40} borderWidth={4} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className=" z-10 absolute right-0 drop-shadow-md ">
              <button
                className=" outline-none p-4 bg-off-white text-black rounded-full cursor-pointer disabled:bg-off-white/50 "
                onClick={scrollRight}
                disabled={centerTemplateItemIndex === templateIds.length - 1}
              >
                <FaArrowRight />
              </button>
            </div>
          </div>
          <div className=" flex justify-center ">
            <Button
              onClick={() =>
                saveFpVideoDetails(
                  {
                    templateId: templateIds[
                      centerTemplateItemIndex
                    ] as (typeof templateIds)[number],
                  },
                  OnboardingStepIds.CHOOSE_VIDEO_TEMPLATE,
                )
              }
              size={'responsive'}
            >
              Continue
            </Button>
          </div>
        </div>
      </DashboardCard>
    </div>
  );
};

export default ChooseVideoTemplate;
