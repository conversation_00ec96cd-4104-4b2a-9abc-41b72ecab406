import React, { useState, useEffect, useRef } from 'react';
import { Player } from '@remotion/player';
import { TEMPLATES_CONFIG } from '@/constants/fp-video';
import { IFpVideoData } from '@/remotion/types';

interface IRemotionPlayerProps {
  videoData: IFpVideoData;
}

const RemotionPlayer = (props: IRemotionPlayerProps) => {
  const { videoData } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({
    width: 0,
    height: 0,
  });
  const [Composition, setComposition] =
    useState<React.FC<{ videoData: IFpVideoData }>>();

  // Calculate dimensions based on container size
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        // Maintain the aspect ratio of the video
        const aspectRatio = videoData.width / videoData.height;
        const height = containerWidth / aspectRatio;

        setDimensions({
          width: containerWidth,
          height: height,
        });
      }
    };

    // Set initial dimensions
    updateDimensions();

    // Update dimensions when window is resized
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, [videoData.width, videoData.height]);

  useEffect(() => {
    switch (videoData.template_id) {
      case 'p3':
        import('@/remotion/templates/p3').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p4':
        import('@/remotion/templates/p4').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p5':
        import('@/remotion/templates/p5').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p6':
        import('@/remotion/templates/p6').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p7':
        import('@/remotion/templates/p7').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p8':
        import('@/remotion/templates/p8').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p9':
        import('@/remotion/templates/p9').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p10':
        import('@/remotion/templates/p10').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
      case 'p11':
        import('@/remotion/templates/p11').then((module) => {
          setComposition(
            () => module.default as React.FC<{ videoData: IFpVideoData }>,
          );
        });
        break;
    }
  }, [videoData]);

  return (
    <div
      className="flex-1 flex flex-col self-stretch relative min-w-[300px] max-w-[300px] lg:max-w-[400px] "
      ref={containerRef}
    >
      {dimensions.width > 0 && Composition && (
        <Player
          component={Composition}
          inputProps={{
            videoData: videoData,
          }}
          durationInFrames={Math.ceil(
            Math.ceil(
              (videoData.duration_in_sec ??
                TEMPLATES_CONFIG[videoData.template_id].durationInSec) *
                TEMPLATES_CONFIG[videoData.template_id].fps,
            ),
          )}
          compositionHeight={videoData.height}
          compositionWidth={videoData.width}
          fps={TEMPLATES_CONFIG[videoData.template_id].fps}
          controls
          clickToPlay={false}
          style={{
            width: Math.round(dimensions.width),
            height: Math.round(dimensions.height),
          }}
        />
      )}
    </div>
  );
};

export default RemotionPlayer;
