'use client';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import DashBoardCards from './DashboardCard';
import Button from '../lib/Button';
import { useMutation } from 'react-query';
import { getAiKeyBenefits, uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import {
  converNormalUrlToCdnUrl,
  logApiErrorAndShowToastMessage,
} from '@/utils';
import FullScreenLoader from '../lib/FullScreenLoader';
import SpinnerLoader from '../lib/SpinnerLoader';
import { useOnboarding } from '../context/OnboardingProvider';
import { OnboardingStepIds } from '@/types/onBoarding';
import { FaChevronRight } from 'react-icons/fa6';
import AddProductImageInput from './addproductdetails/AddProductImageInput';
import EnterProductUrlInput from './addproductdetails/EnterProductUrlInput';
import ProductDetailInput from './addproductdetails/ProductDetailInput';

const AddProductDetails = () => {
  const { user } = useUser();
  const {
    projectDetails,
    saveProjectDetails,
    isOnboardingCompleted,
    isSavingProject,
  } = useOnboarding();

  const [tempProjectDetails, setTempProjectDetails] =
    useState<IBannerbotBusinessDetails>(
      JSON.parse(JSON.stringify(projectDetails.details?.business_details)),
    );

  const [callSaveProjectDetails, setCallSaveProjectDetails] = useState(false);

  useEffect(() => {
    setTempProjectDetails(
      JSON.parse(JSON.stringify(projectDetails.details?.business_details)),
    );
  }, [projectDetails.details?.business_details]);

  const [productImageFiles, setProductImageFile] = useState<
    {
      preview: string;
      name: string;
      file: File;
      width: number;
      height: number;
      sizeInKb: number;
    }[]
  >();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    url: '',
  });

  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);
  const getAiKeyBenefitsMutation = useMutation(getAiKeyBenefits);

  const uploadAllFilesAndGetUrl = useCallback(async () => {
    if ((productImageFiles ?? []).length === 0) {
      return Promise.resolve();
    }
    // upload to BE and get url
    setIsLoading(true);
    const uploadPromises = (productImageFiles ?? []).map((productImageFile) =>
      uploadLogoImageMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file: productImageFile.file,
          },
          queryParams: {},
        })
        .then((response) => {
          return {
            url: converNormalUrlToCdnUrl(response.data.s3_url, {
              width: productImageFile.width / 2,
              height: productImageFile.height / 2,
              q: 100,
              fit: 'crop',
            }),
            width: productImageFile.width,
            height: productImageFile.height,
          };
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error as Error,
            'AddProductDetails.uploadAllFilesAndGetUrl',
          );
          // reset in case of error
          setProductImageFile(undefined);
        }),
    );

    return Promise.all(uploadPromises).then((response) => {
      return response.filter((item) => !!item);
    });
  }, [productImageFiles, uploadLogoImageMutation, user]);

  const getAiKeysAndSave = useCallback(
    (
      productUrls: {
        url: string;
        width: number;
        height: number;
      }[],
    ) =>
      getAiKeyBenefitsMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            id: projectDetails.id ?? '',
            details: {
              business_details: tempProjectDetails,
            },
          },
        })
        .then((response) => {
          saveProjectDetails(
            {
              ...projectDetails,
              details: {
                ...(projectDetails.details ?? {}),
                business_details: {
                  ...tempProjectDetails,
                  product_images: productUrls,
                  key_benefits: response.data.key_benefits,
                },
                ad_details: {
                  types: projectDetails.details?.ad_details?.types ?? [],
                },
              },
            },
            OnboardingStepIds.ADD_PRODUCT_DETAILS,
          );
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error,
            'AddProductDetails.getAiKeysAndSave',
          );
        })
        .finally(() => {
          setIsLoading(false);
        }),
    [
      projectDetails,
      tempProjectDetails,
      getAiKeyBenefitsMutation,
      saveProjectDetails,
      user,
    ],
  );

  const previousUploads = useMemo(
    () => tempProjectDetails.product_images,
    [tempProjectDetails.product_images],
  );

  const handleFormSubmit = async () => {
    if (isOnboardingCompleted) {
      return saveProjectDetails(
        projectDetails,
        OnboardingStepIds.ADD_PRODUCT_DETAILS,
      );
    }

    setIsLoading(true);
    if (!projectDetails.id) {
      setCallSaveProjectDetails(true);
      saveProjectDetails(
        {
          ...projectDetails,
          title: projectDetails.title
            ? projectDetails.title
            : (tempProjectDetails.product_or_service_description
                ?.split(' ')
                .splice(0, 3)
                .join(' ') ?? ''),
          details: {
            business_details: tempProjectDetails,
          },
        },
        OnboardingStepIds.SELECT_PROJECT_TYPE,
      );
    } else {
      uploadFileAndSaveProject();
    }
  };

  const uploadFileAndSaveProject = useCallback(async (): Promise<void> => {
    const productUrls = await uploadAllFilesAndGetUrl();

    getAiKeysAndSave([...(productUrls ?? []), ...(previousUploads ?? [])]);
  }, [uploadAllFilesAndGetUrl, getAiKeysAndSave, previousUploads]);

  useEffect(() => {
    // console.log(projectDetails);
    if (projectDetails?.id && callSaveProjectDetails) {
      uploadFileAndSaveProject();
      setCallSaveProjectDetails(false);
    }
  }, [projectDetails, callSaveProjectDetails, uploadFileAndSaveProject]);

  return (
    <DashBoardCards className=" max-w-5xl mx-auto ">
      <form
        className=" flex flex-col gap-5 md:gap-6 "
        onSubmit={(e) => {
          e.preventDefault();
          handleFormSubmit();
        }}
      >
        <EnterProductUrlInput
          tempProjectDetails={tempProjectDetails}
          setTempProjectDetails={setTempProjectDetails}
          setShowFullPageLoader={setIsLoading}
          errors={errors}
          setErrors={setErrors}
        />
        <ProductDetailInput
          setTempProjectDetails={setTempProjectDetails}
          tempProjectDetails={tempProjectDetails}
        />
        <AddProductImageInput
          setProductImageFile={setProductImageFile}
          tempProjectDetails={tempProjectDetails}
          setTempProjectDetails={setTempProjectDetails}
          previousUploads={previousUploads}
          isOnboardingCompleted={isOnboardingCompleted}
          isLoading={isLoading}
          isSavingProject={isSavingProject}
          productImageFiles={productImageFiles}
        />

        <div className=" flex justify-end ">
          {/* <Button
            variant={'outline'}
            onClick={() => previousStep(OnboardingStepIds.ADD_PRODUCT_DETAILS)}
            disabled={isSavingProject || isLoading}
          >
            <FaChevronLeft size={16} /> Previous
          </Button> */}
          <Button
            type="submit"
            size={'responsive'}
            disabled={
              isSavingProject ||
              isLoading ||
              Object.values(errors).some((item) => item)
            }
          >
            <>
              Next
              {isSavingProject || isLoading ? (
                <SpinnerLoader size={20} borderWidth={2} />
              ) : (
                <FaChevronRight size={16} />
              )}
            </>
          </Button>
        </div>
      </form>
      {(isSavingProject || isLoading) && <FullScreenLoader />}
    </DashBoardCards>
  );
};

export default AddProductDetails;
