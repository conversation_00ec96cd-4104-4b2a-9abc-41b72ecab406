import React, { useEffect, useState } from 'react';
import DashBoardCards from './DashboardCard';
import HeadingV2 from '../lib/typography/HeadingV2';
import TextV2 from '../lib/typography/TextV2';
import Image from 'next/image';
import Button from '../lib/Button';
import { useOnboarding } from '../context/OnboardingProvider';
import SpinnerLoader from '../lib/SpinnerLoader';
import { BannerbotAdType } from '@/types/bannerbotProject';
import { OnboardingStepIds } from '@/types/onBoarding';
import { FaChevronRight } from 'react-icons/fa';
import { FaChevronLeft } from 'react-icons/fa6';
import FullScreenLoader from '../lib/FullScreenLoader';
// import { CiLock } from 'react-icons/ci';
// import Button from '../lib/Button';

const SelectTypeOfAd = () => {
  const { isSavingProject, projectDetails, saveProjectDetails, previousStep } =
    useOnboarding();
  const [showFullPageLoader, setShowFullPageLoader] = useState(false);

  const [selectedAdType, setSelectedAdType] = useState<BannerbotAdType>(
    JSON.parse(
      JSON.stringify(
        Array.isArray(projectDetails.details?.ad_details?.types) &&
          projectDetails.details.ad_details.types.length !== 0
          ? projectDetails.details.ad_details.types[0]
          : BannerbotAdType.SINGLE_IMAGE,
      ),
    ),
  );

  useEffect(() => {
    setSelectedAdType(
      JSON.parse(
        JSON.stringify(
          Array.isArray(projectDetails.details?.ad_details?.types) &&
            projectDetails.details.ad_details.types.length !== 0
            ? projectDetails.details.ad_details.types[0]
            : BannerbotAdType.SINGLE_IMAGE,
        ),
      ),
    );
  }, [projectDetails]);

  // const handleAdTypeSelect = (newAdType: BannerbotAdType) => {
  //   setSelectedAdType(newAdType);
  // };

  const handleNext = (adType?: BannerbotAdType) => {
    const updatedProjectDetails = { ...projectDetails };

    const selAdType = adType ? adType : selectedAdType;

    const allSelectedAdTypes = new Set([
      selAdType,
      ...(projectDetails.details?.ad_details?.types ?? []),
    ]);

    updatedProjectDetails.details = {
      ...projectDetails.details,
      business_details: {
        ...projectDetails.details?.business_details,
      },
      ad_details: {
        types: Array.from(allSelectedAdTypes),
      },
    };

    saveProjectDetails(
      updatedProjectDetails,
      OnboardingStepIds.SELECT_TYPE_OF_AD,
    ).then(() => setShowFullPageLoader(true));
  };

  return (
    <div className=" flex flex-col gap-6 md:gap-8 max-w-5xl mx-auto ">
      <HeadingV2 level={3}>Select type of ads you want</HeadingV2>
      <div className=" flex max-md:flex-col max-md:items-center justify-center gap-4 md:gap-8 ">
        <DashBoardCards
          backgroundCirlesSize={350}
          selectable
          className=" max-h-[220px] "
          onClick={() => {
            handleNext(BannerbotAdType.SINGLE_IMAGE);
          }}
          selected={selectedAdType === BannerbotAdType.SINGLE_IMAGE}
        >
          <TextV2 level={2} className=" uppercase ">
            static banner ads
          </TextV2>
          <Image
            src={'/images/ads/banner-ads.png'}
            width={218}
            height={203}
            alt="banner ads"
            className=" mt-2.5 "
          />
        </DashBoardCards>

        <DashBoardCards
          backgroundCirlesSize={350}
          selectable
          className=" max-h-[220px] "
          onClick={() => {
            handleNext(BannerbotAdType.VIDEO);
          }}
          selected={selectedAdType === BannerbotAdType.VIDEO}
        >
          <TextV2 level={2} className=" uppercase ">
            video banner ads
          </TextV2>
          <Image
            src={'/images/ads/video-ads.png'}
            width={218}
            height={203}
            alt="video ads"
            className=" mt-2.5 "
          />
        </DashBoardCards>

        <div className=" relative ">
          <ComingSoonV2 />
          <DashBoardCards
            backgroundCirlesSize={350}
            selectable
            className=" max-h-[220px] "
          >
            <TextV2 level={2} className=" uppercase ">
              UGC Ads
            </TextV2>
            <Image
              src={'/images/ads/carousel-ads.png'}
              width={218}
              height={203}
              alt="carousel ads"
              className=" mt-2.5 "
            />
          </DashBoardCards>
        </div>
      </div>
      {/* <div className=" flex justify-between items-center bg-[#594988] px-5 py-4 rounded-lg ">
        <div className=" flex items-center gap-4 ">
          <CiLock size={24} />
          <TextV2 level={2}>Select Multiple Ad Type in Single Project</TextV2>
        </div>
        <Button
          variant={'custom'}
          size={'pill'}
          className=" bg-red-1 hover:bg-red-1/90 "
        >
          Upgrade to Pro
        </Button>
      </div> */}
      <div className=" flex justify-between gap-4 ">
        <Button
          variant={'outline'}
          size={'responsive'}
          onClick={() => previousStep(OnboardingStepIds.SELECT_TYPE_OF_AD)}
          className=" max-sm:flex-1 max-sm:flex-shrink-0 "
        >
          <FaChevronLeft size={16} /> Previous
        </Button>
        <Button
          onClick={() => handleNext()}
          size={'responsive'}
          disabled={isSavingProject}
          className=" max-sm:flex-1 max-sm:flex-shrink-0 "
        >
          <>
            Next
            {isSavingProject ? (
              <SpinnerLoader size={20} borderWidth={2} />
            ) : (
              <FaChevronRight size={16} />
            )}
          </>
        </Button>
      </div>
      {(showFullPageLoader || isSavingProject) && <FullScreenLoader />}
    </div>
  );
};

function ComingSoonV2() {
  return (
    <div className=" bg-off-white text-gray-950 font-medium absolute z-[1] right-0 top-0 -translate-y-2 translate-x-2 px-4 py-1.5 leading-none w-fit rounded-full text-sm ">
      Coming Soon
    </div>
  );
}

export default SelectTypeOfAd;
