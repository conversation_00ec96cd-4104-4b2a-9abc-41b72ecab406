'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import Button from '../lib/Button';
import { FiFilter } from 'react-icons/fi';
import { FaAngleDown, FaCheck, FaPlay, FaPlus } from 'react-icons/fa6';
import DashBoardCards from './DashboardCard';
import { BsStars } from 'react-icons/bs';
import { FaRegUser } from 'react-icons/fa';
import { TbProgressCheck } from 'react-icons/tb';
import Image from 'next/image';
import HeadingV2 from '../lib/typography/HeadingV2';
import { HiOutlineDocumentPlus } from 'react-icons/hi2';
import TextV2 from '../lib/typography/TextV2';
import {
  BannerbotAdType,
  BannerbotProjectStatus,
} from '@/types/bannerbotProject';
import { useQuery } from 'react-query';
import { getProjects } from '@/actions/bannerbotV2';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import { useRouter } from 'next/navigation';
import { changeWidthHeightOfImageUrl, converNormalUrlToCdnUrl } from '@/utils';
import HorizontalLoader from '../lib/HorizontalLoader';
import Link from 'next/link';

enum FilterOptions {
  'ALL_PROJECT' = 'All Project',
  'COMPLETED' = 'Completed',
  'DRAFT' = 'Draft',
}

const DesignProjects = () => {
  const { user } = useUser();
  const [showFilterOptions, setShowFilterOptions] = useState(false);
  const [filterOn, setFilterOn] = useState<FilterOptions>(
    FilterOptions.ALL_PROJECT,
  );

  const filterOptionDivRef = useRef<HTMLDivElement>(null);

  const getProjectsQuery = useQuery('getProjects', () => {
    return getProjects({
      headers: getCommonHeaders(user),
    });
  });

  const { data } = getProjectsQuery;

  const router = useRouter();

  const projects = useMemo(
    () =>
      data?.data.filter((item) => {
        if (filterOn === FilterOptions.COMPLETED) {
          return item.status === 'COMPLETE';
        } else if (filterOn === FilterOptions.DRAFT) {
          return item.status === 'DRAFT';
        } else {
          return true;
        }
      }),
    [data, filterOn],
  );

  useEffect(() => {
    const abortController = new AbortController();

    if (showFilterOptions) {
      document.body.addEventListener(
        'click',
        (e) => {
          const target = e.target as HTMLElement;
          if (
            showFilterOptions &&
            !filterOptionDivRef.current?.contains(target)
          ) {
            setShowFilterOptions(false);
          }
        },
        {
          signal: abortController.signal,
        },
      );
    }

    return () => {
      abortController.abort();
    };
  }, [showFilterOptions]);

  const startNewProject = () => {
    router.push('/onboarding');
  };

  if (!data) {
    return (
      <div className=" fixed inset-0 bg-blue-1 flex flex-col gap-2 items-center justify-center ">
        <div className=" w-[200px] ">
          <HorizontalLoader />
        </div>
        <TextV2 level={4} className=" text-gray-300 ">
          Fetching all your Projects
        </TextV2>
      </div>
    );
  }

  return (
    <>
      <div className=" flex max-md:flex-col md:justify-end md:items-center ">
        {/* <HeadingV2 level={3}>Design Projects</HeadingV2> */}
        {(data?.data ?? []).length > 0 && (
          <div className=" flex max-sm:flex-col gap-4 ">
            <div className=" relative" ref={filterOptionDivRef}>
              <Button
                variant={'outline'}
                size={'responsive'}
                onClick={() => setShowFilterOptions((prev) => !prev)}
                className=" w-full "
              >
                <FiFilter size={24} /> {filterOn}
                <FaAngleDown size={24} />
              </Button>

              <div
                className={` ${showFilterOptions ? ' max-h-[400px] opacity-100 scale-100 ' : ' max-h-0 opacity-10 '} absolute z-10 top-full w-full mt-2 overflow-hidden transition-all duration-300 `}
              >
                <div className=" flex flex-col bg-white text-gray-900 border-2 border-gray-500/50 rounded-lg ">
                  <TextV2
                    level={3}
                    className=" h-11 flex items-center hover:bg-black/5 px-5 cursor-pointer "
                    onClick={() => {
                      setShowFilterOptions(false);
                      setFilterOn(FilterOptions.ALL_PROJECT);
                    }}
                  >
                    All Projects
                  </TextV2>
                  <TextV2
                    level={3}
                    className=" h-11 flex items-center hover:bg-black/5 px-5 cursor-pointer "
                    onClick={() => {
                      setShowFilterOptions(false);
                      setFilterOn(FilterOptions.COMPLETED);
                    }}
                  >
                    Completed
                  </TextV2>
                  <TextV2
                    level={3}
                    className=" h-11 flex items-center hover:bg-black/5 px-5 cursor-pointer "
                    onClick={() => {
                      setShowFilterOptions(false);
                      setFilterOn(FilterOptions.DRAFT);
                    }}
                  >
                    Draft
                  </TextV2>
                </div>
              </div>
            </div>
            <Button className=" " onClick={startNewProject} size={'responsive'}>
              <FaPlus size={24} /> Start New Project
            </Button>
          </div>
        )}
      </div>
      <div className=" mt-6 sm:mt-8 ">
        {(data?.data ?? []).length > 0 ? (
          (projects ?? []).length > 0 ? (
            <div className=" grid md:grid-cols-2 gap-5 ">
              {(projects ?? []).map((item, index) => (
                <ProjectCard
                  title={item.title ?? ''}
                  status={item.status}
                  isAiGenerated={item.type === 'AI_ONLY'}
                  images={
                    item.details?.saved_assets?.[BannerbotAdType.SINGLE_IMAGE]
                  }
                  videos={item.details?.saved_assets?.[BannerbotAdType.VIDEO]}
                  key={index}
                  redirectUrl={
                    item.status !== BannerbotProjectStatus.COMPLETE
                      ? `/onboarding?id=${item.id}`
                      : item.details.ad_details?.types[0] ===
                          BannerbotAdType.SINGLE_IMAGE
                        ? `/project-details?id=${item.id}&banner-type=SINGLE_IMAGE`
                        : `/project-details?id=${item.id}&banner-type=VIDEO`
                  }
                />
              ))}
            </div>
          ) : (
            <TextV2 className=" text-gray-400 text-center mt-10 ">
              No projects to show
            </TextV2>
          )
        ) : (
          <DashBoardCards className=" p-8 ">
            <div className=" max-w-[575px] w-full mx-auto text-center flex flex-col justify-center items-center ">
              <div className=" flex flex-col items-center justify-center ">
                <div className=" p-4 rounded-full bg-blue-1 w-fit ">
                  <HiOutlineDocumentPlus size={24} />
                </div>
                <div className=" mt-4 ">
                  <TextV2>No projects yet</TextV2>
                  <TextV2 level={2} className=" text-gray-2 mt-2 ">
                    Create your first project to start generating amazing
                    banners with AI. It only takes a few minutes to get started.
                  </TextV2>
                </div>
              </div>
              <Button className=" mt-8 " onClick={startNewProject}>
                <FaPlus size={24} />
                Create Your First Project
              </Button>
            </div>
          </DashBoardCards>
        )}
      </div>
      {/* {isLoading && <FullScreenLoader />} */}
    </>
  );
};

const ProjectCard = ({
  title,
  status,
  isAiGenerated,
  redirectUrl,
  images = [],
  videos,
}: {
  title: string;
  status: BannerbotProjectStatus;
  isAiGenerated: boolean;
  redirectUrl: string;
  images:
    | {
        url: string;
        width: number;
        height: number;
      }[]
    | undefined;
  videos: IBannerbotVideoAdDetails[] | undefined;
}) => {
  const showVideos = useMemo(
    () => (videos ?? []).slice(-3).reverse(),
    [videos],
  );

  const showImages = useMemo(() => {
    if (showVideos.length > 2) {
      return [];
    }

    const modifiedUrlImages = images
      .slice(0, 4)
      .map(({ url, width, height }) => {
        const targetHeight = 250;
        const scaledWidth = Math.round((width * targetHeight) / height);

        return {
          url: converNormalUrlToCdnUrl(url, {
            height: targetHeight,
            width: scaledWidth,
            q: 30,
          }),
          height,
          width,
        };
      });

    const imagesArr =
      modifiedUrlImages.length == 4
        ? modifiedUrlImages
        : [
            ...modifiedUrlImages,
            ...Array.from({ length: 4 - modifiedUrlImages.length }).map(() => ({
              url: '/images/banner-placeholder.png',
              width: 1600,
              height: 900,
            })),
          ];

    if (!showVideos || showVideos.length === 0 || showVideos.length === 1) {
      return imagesArr;
    } else {
      // for showVideos.length === 2
      return imagesArr.slice(0, 2);
    }
  }, [images, showVideos]);

  return (
    <Link href={redirectUrl}>
      <DashBoardCards
        selectable
        className=" w-full h-full min-h-[250px] md:min-h-[350px] "
        innerClassName=" w-full h-full flex flex-col "
      >
        <div className=" flex max-[400px]:flex-col gap-y-3 gap-5 sm:items-center ">
          <HeadingV2 level={3} className=" line-clamp-1 ">
            {title}
          </HeadingV2>
          <div className=" flex-1 flex justify-between items-center gap-2">
            <HumanOrAiPill isAiGenerated={isAiGenerated} />
            <ProgressStatusPill status={status} />
          </div>
        </div>

        <div
          className={` flex-1 grid ${showVideos && showVideos?.length !== 0 ? 'grid-cols-3' : ' grid-cols-2 '} grid-rows-2 gap-4 mt-5 w-full `}
        >
          {showVideos?.map((item, index) => (
            <div
              key={'video ' + index}
              className=" relative row-span-2 rounded-xl overflow-hidden "
            >
              {item.thumbnail_url && item.thumbnail_url !== '' ? (
                <>
                  <Image
                    src={changeWidthHeightOfImageUrl(item.thumbnail_url, {
                      width: 210,
                      height: (item.height / item.width) * 210,
                    })}
                    width={210}
                    height={370}
                    alt={item.caption + 'thumbnail image'}
                    className=" w-full h-full object-cover "
                  />
                  <div className=" absolute z-10 inset-0 flex items-center justify-center ">
                    <div className=" p-4 rounded-full bg-black/70 ">
                      <FaPlay size={20} />
                    </div>
                  </div>
                </>
              ) : item.url !== '' ? (
                <>
                  <video src={item.url} />
                  <div className=" absolute z-10 inset-0 flex items-center justify-center ">
                    <div className=" p-4 rounded-full bg-black/70 ">
                      <FaPlay size={20} />
                    </div>
                  </div>
                </>
              ) : (
                <div className=" bg-gray-500 h-full flex flex-col items-center justify-center text-center p-3 select-none gap-2 ">
                  <TextV2 level={4} className=" text-gray-50 ">
                    The video is being generated
                  </TextV2>
                  <p className=" !text-sm text-gray-200 ">
                    This might take around 2 minutes
                  </p>
                </div>
              )}
            </div>
          ))}
          {showImages.map(({ url, height, width }, index) => (
            <div key={'image ' + index} className=" relative pt-[56.8%] ">
              <Image
                src={url}
                width={width}
                height={height}
                alt="Banner Image"
                className=" absolute rounded-xl border-2 inset-0 border-white w-full h-full object-cover bg-gray-100 "
              />
            </div>
          ))}
        </div>
      </DashBoardCards>
    </Link>
  );
};

const HumanOrAiPill = ({ isAiGenerated }: { isAiGenerated: boolean }) => {
  return (
    <Button
      size={'responsivePill'}
      variant={'custom'}
      className=" bg-white/15 !rounded-full "
    >
      {isAiGenerated ? (
        <>
          <BsStars size={16} />
          AI
        </>
      ) : (
        <>
          <FaRegUser size={16} />
          Human
        </>
      )}
    </Button>
  );
};

const ProgressStatusPill = ({ status }: { status: BannerbotProjectStatus }) => {
  return (
    <Button
      size={'responsivePill'}
      variant={'custom'}
      className=" bg-blue-1 shadow-[inset_0_0_0_0.5px] shadow-white !rounded-full "
    >
      {status === BannerbotProjectStatus.COMPLETE ? (
        <>
          <FaCheck size={16} /> Completed
        </>
      ) : status === BannerbotProjectStatus.DRAFT ? (
        <>
          <TbProgressCheck size={16} />
          Draft
        </>
      ) : (
        <>
          <TbProgressCheck size={16} />
          Archieved
        </>
      )}
    </Button>
  );
};

export default DesignProjects;
