import { getCommonHeaders } from '@/actions';
import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { useUser } from '@/components/context/UserProvider';
import TextV2 from '@/components/lib/typography/TextV2';
import { showToastMessage } from '@/modules/toast';
import {
  changeWidthHeightOfImageUrl,
  logApiErrorAndShowToastMessage,
} from '@/utils';
import Image from 'next/image';
import React, { useCallback, useState } from 'react';
import { useMutation } from 'react-query';
import SearchMediaOrUploadDialog from './SearchMediaOrUploadDialog';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import Button from '@/components/lib/Button';
import { FaPlay } from 'react-icons/fa6';
import PreviewMediaDialog from './PreviewMediaDialog';
import {
  MAX_IMAGE_FILE_UPLOAD_SIZE_KB,
  MAX_VIDEO_UPLOAD_FILE_SIZE_KB,
} from '@/constants';
import { IFpVideoData } from '@/remotion/types';

interface ILoadSceneAssetsProps {
  assets: NonNullable<IFpVideoData['scenes']>[number]['assets'];
  updateSceneAssets: (
    changes: NonNullable<IFpVideoData['scenes']>[number]['assets'],
  ) => void;
}

const LoadSceneAssets = (props: ILoadSceneAssetsProps) => {
  const { assets, updateSceneAssets } = props;
  const { user } = useUser();
  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [previewMediaDetails, setPreviewMediaDetails] = useState<{
    type: 'video' | 'image' | 'audio';
    url: string;
  }>();
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [searchMediaDialogProps, setSearchMediaDialogProps] = useState<{
    type: 'video' | 'image';
    index: number;
  }>();

  const handleUpdateAssestFile = useCallback(
    (url: string, index: number, { type }: { type: 'image' | 'video' }) => {
      updateSceneAssets(
        assets
          ? assets.map((item, i) => (i === index ? { url, type } : item))
          : assets,
      );
    },
    [assets, updateSceneAssets],
  );

  const handleFileUpload = useCallback(
    (
      e: React.ChangeEvent<HTMLInputElement>,
      index: number,
      { type }: { type: 'image' | 'video' },
    ) => {
      const file = e.target.files?.[0];

      if (!file) {
        showToastMessage('something went wrong, please try again', 'error');
        return;
      }

      const fileSizeInKb = file.size / 1024;
      if (fileSizeInKb > MAX_VIDEO_UPLOAD_FILE_SIZE_KB) {
        showToastMessage(
          `File "${file.name}" exceeds the maximum size of ${MAX_VIDEO_UPLOAD_FILE_SIZE_KB} KB.`,
          'error',
        );
        return;
      }

      uploadLogoImageMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file,
          },
        })
        .then((response) => {
          handleUpdateAssestFile(response.data.s3_url, index, { type });
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'LoadSceneAssets.uploadLogoImageMutation',
          );
        })
        .finally(() => {
          e.target.files = null;
          e.target.value = '';
        });
    },
    [handleUpdateAssestFile, uploadLogoImageMutation, user],
  );

  const searchMediaFileUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!searchMediaDialogProps) return;

      handleFileUpload(e, searchMediaDialogProps.index, {
        type: searchMediaDialogProps.type,
      });
      setIsDialogOpen(false);
    },
    [searchMediaDialogProps, handleFileUpload],
  );

  const searchMediaUpdateFile = useCallback(
    (url: string) => {
      if (!searchMediaDialogProps) return;
      handleUpdateAssestFile(url, searchMediaDialogProps.index, {
        type: searchMediaDialogProps.type,
      });
      setIsDialogOpen(false);
    },
    [searchMediaDialogProps, handleUpdateAssestFile],
  );

  return (
    <div className=" space-y-4 ">
      {assets &&
        assets.map((asset, index) =>
          asset.type === 'image' ? (
            <div key={`assets-${index}`}>
              <TextV2 level={4} className=" font-medium ">
                Image {index + 1}
              </TextV2>
              <div
                className=" h-[100px] mt-2 flex gap-2 cursor-pointer "
                onClick={() => {
                  setSearchMediaDialogProps({ index, type: asset.type });
                  setIsDialogOpen(true);
                }}
              >
                <Image
                  src={changeWidthHeightOfImageUrl(asset.url, {
                    width: 100,
                    height: 100,
                    fit: 'cover',
                  })}
                  key={asset.url}
                  width={100}
                  height={100}
                  alt={`template image ${index + 1}`}
                  className=" rounded-lg "
                />
                <div className=" flex-1 flex justify-center items-center rounded-lg bg-blue-2/15 border-2 border-dashed border-gray-500 ">
                  <TextV2 level={4}>Change</TextV2>
                </div>
              </div>
            </div>
          ) : (
            <div key={asset.url}>
              <TextV2 level={4} className=" font-medium ">
                Video {index + 1}
              </TextV2>

              <div className=" mt-2 flex gap-2 ">
                <Button
                  variant={'custom'}
                  className=" bg-blue-2/15 border-2 border-blue-2/30 hover:bg-blue-2/10 "
                  onClick={() => {
                    setIsPreviewDialogOpen(true);
                    setPreviewMediaDetails(asset);
                  }}
                >
                  <FaPlay size={20} />
                  Preview
                </Button>

                <div
                  className=" flex-1 flex justify-center items-center rounded-lg bg-blue-2/15 border-2 border-dashed border-gray-500 min-h-12 cursor-pointer "
                  onClick={() => {
                    setSearchMediaDialogProps({ index, type: asset.type });
                    setIsDialogOpen(true);
                  }}
                >
                  <TextV2 level={4}>Change</TextV2>
                </div>
              </div>
            </div>
          ),
        )}

      {searchMediaDialogProps && (
        <SearchMediaOrUploadDialog
          isOpen={isDialogOpen}
          setIsOpen={setIsDialogOpen}
          title={`Change ${searchMediaDialogProps.type}`}
          showSection="search"
          type={searchMediaDialogProps.type}
          fileUpload={searchMediaFileUpload}
          updateFile={searchMediaUpdateFile}
          maxFileSizeInKB={
            searchMediaDialogProps.type === 'image'
              ? MAX_IMAGE_FILE_UPLOAD_SIZE_KB
              : MAX_VIDEO_UPLOAD_FILE_SIZE_KB
          }
          onClose={() => setSearchMediaDialogProps(undefined)}
        />
      )}

      {uploadLogoImageMutation.isLoading && <FullScreenLoader />}
      <PreviewMediaDialog
        isDialogOpen={isPreviewDialogOpen}
        setIsDialogOpen={setIsPreviewDialogOpen}
        previewMediaDetails={previewMediaDetails}
        setPreviewMediaDetails={setPreviewMediaDetails}
      />
    </div>
  );
};

export default LoadSceneAssets;
