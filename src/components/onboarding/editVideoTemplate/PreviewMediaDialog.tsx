import Dialog from '@/components/lib/Dialog';
import TextV2 from '@/components/lib/typography/TextV2';
import React, { useCallback } from 'react';
import { FaImage, FaVideo } from 'react-icons/fa6';
import { IoClose } from 'react-icons/io5';
import PreviewMedia from './PreviewMedia';

interface IMediaDetails {
  type: 'image' | 'video' | 'audio';
  url: string;
}

interface IPreviewMediaDialogProps {
  previewMediaDetails: IMediaDetails | undefined;
  setPreviewMediaDetails: React.Dispatch<
    React.SetStateAction<IMediaDetails | undefined>
  >;
  isDialogOpen: boolean;
  setIsDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const PreviewMediaDialog = (props: IPreviewMediaDialogProps) => {
  const {
    previewMediaDetails,
    setPreviewMediaDetails,
    isDialogOpen,
    setIsDialogOpen,
  } = props;
  return (
    <Dialog
      onClose={useCallback(
        () => setPreviewMediaDetails(undefined),
        [setPreviewMediaDetails],
      )}
      isOpen={isDialogOpen}
      setIsOpen={setIsDialogOpen}
    >
      <div className=" border-2 border-off-white/40 p-6 rounded-xl bg-blue-1 md:min-w-[600px] max-w-[90vw] ">
        <div className=" space-y-5 ">
          <div className=" flex justify-between items-center gap-4 ">
            <div className=" flex items-center gap-3 text-gray-400 ">
              {previewMediaDetails?.type === 'image' ? (
                <FaImage size={20} />
              ) : (
                <FaVideo size={20} />
              )}
              <TextV2 level={3} className=" capitalize ">
                {previewMediaDetails?.type} Preview
              </TextV2>
            </div>
            <button onClick={() => setIsDialogOpen(false)}>
              <IoClose size={24} className=" cursor-pointer " />
            </button>
          </div>
          <PreviewMedia
            type={previewMediaDetails?.type}
            url={previewMediaDetails?.url}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default PreviewMediaDialog;
