import Input from '@/components/lib/Input';
import TextV2 from '@/components/lib/typography/TextV2';
import { IFpVideoData } from '@/remotion/types';
import React, { ChangeEvent } from 'react';

interface ILoadSceneTextsProps {
  texts: NonNullable<IFpVideoData['scenes']>[number]['texts'];
  updateSceneTexts: (
    changes: NonNullable<IFpVideoData['scenes']>[number]['texts'],
  ) => void;
}

const LoadSceneTexts = (props: ILoadSceneTextsProps) => {
  const { texts, updateSceneTexts } = props;
  const handleSceneTextChange = (
    e: ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    updateSceneTexts(
      texts?.map((item, i) => (i === index ? { value: e.target.value } : item)),
    );
  };

  return (
    <>
      {texts?.map((text, index) => (
        <div key={`texts-${index}`}>
          <label>
            <TextV2 level={4} className=" font-medium ">
              Text {index + 1}
            </TextV2>
            <Input
              className=" !p-3 !bg-blue-2/15 focus:!ring-1 mt-2 "
              placeholder="eg. Book Now, Get Started"
              value={text.value}
              onChange={(e) => handleSceneTextChange(e, index)}
            />
          </label>
        </div>
      ))}
    </>
  );
};

export default LoadSceneTexts;
