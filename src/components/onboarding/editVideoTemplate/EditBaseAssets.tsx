import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import Button from '@/components/lib/Button';
import TextV2 from '@/components/lib/typography/TextV2';
import {
  changeWidthHeightOfImageUrl,
  logApiErrorAndShowToastMessage,
} from '@/utils';
import Image from 'next/image';
import React, { ChangeEvent, useCallback, useEffect, useState } from 'react';
import { FaPlay } from 'react-icons/fa6';
import { useMutation } from 'react-query';
import { getCommonHeaders } from '@/actions';
import { useUser } from '@/components/context/UserProvider';
import { showToastMessage } from '@/modules/toast';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import PreviewMediaDialog from './PreviewMediaDialog';
import SearchMediaOrUploadDialog from './SearchMediaOrUploadDialog';
import {
  MAX_IMAGE_FILE_UPLOAD_SIZE_KB,
  MAX_VIDEO_UPLOAD_FILE_SIZE_KB,
} from '@/constants';
import { IFpVideoData } from '@/remotion/types';

interface IEditBaseAssetsProps {
  baseAsset: IFpVideoData['base_assets'];
  updateBaseAssets: (baseMedia: IFpVideoData['base_assets']) => void;
}

const EditBaseAssets = (props: IEditBaseAssetsProps) => {
  const { baseAsset, updateBaseAssets } = props;
  const { user } = useUser();
  const [tempBaseAsset, setTempBaseAsset] = useState(baseAsset);
  const [previewMediaDetails, setPreviewMediaDetails] = useState<{
    type: 'video' | 'image' | 'audio';
    url: string;
  }>();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [searchMediaDialogProps, setSearchMediaDialogProps] = useState<{
    type: 'video' | 'image';
    index: number;
  }>();

  const uploadFileAndGetS3UrlMutation = useMutation(uploadFileAndGetS3Url);

  const handleUpdateFile = useCallback(
    (url: string, index: number, { type }: { type: 'image' | 'video' }) => {
      setTempBaseAsset((prev) =>
        prev?.map((item, i) => (i === index ? { url, type } : item)),
      );
    },
    [],
  );

  const handleFileUpload = useCallback(
    (
      e: ChangeEvent<HTMLInputElement>,
      index: number,
      { type }: { type: 'image' | 'video' },
    ) => {
      const file = e.target.files?.[0];

      if (!file) {
        showToastMessage('something went wrong, please try again', 'error');
        return;
      }

      uploadFileAndGetS3UrlMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file: file,
          },
        })
        .then((response) => {
          handleUpdateFile(response.data.s3_url, index, { type });
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'EditBaseAssets.uploadFileAndGetS3UrlMutation',
          );
        })
        .finally(() => {
          e.target.files = null;
          e.target.value = '';
        });
    },
    [handleUpdateFile, uploadFileAndGetS3UrlMutation, user],
  );

  const searchMediaFileUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!searchMediaDialogProps) return;

      handleFileUpload(e, searchMediaDialogProps.index, {
        type: searchMediaDialogProps.type,
      });
      setIsDialogOpen(false);
    },
    [searchMediaDialogProps, handleFileUpload],
  );

  const searchMediaUpdateFile = useCallback(
    (url: string) => {
      if (!searchMediaDialogProps) return;
      handleUpdateFile(url, searchMediaDialogProps.index, {
        type: searchMediaDialogProps.type,
      });
      setIsDialogOpen(false);
    },
    [searchMediaDialogProps, handleUpdateFile],
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateBaseAssets(tempBaseAsset);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [tempBaseAsset, updateBaseAssets]);

  const getVideoDuration = useCallback((src: string) => {
    return new Promise<number>((resolve, reject) => {
      const video = document.createElement('video');
      video.src = src;
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        resolve(video.duration);
      };

      video.onerror = (e) => {
        reject(e.toString());
      };
    });
  }, []);

  useEffect(() => {
    const hasMissingDurations = (tempBaseAsset ?? []).some(
      (asset) =>
        asset.type === 'video' &&
        (!asset.video_duration || asset.video_duration === 0),
    );

    if (!hasMissingDurations) return;

    Promise.all(
      (tempBaseAsset ?? []).map(async (asset) => {
        if (
          (!asset.video_duration || asset.video_duration === 0) &&
          asset.type === 'video'
        ) {
          const duration = await getVideoDuration(asset.url);
          asset.video_duration = duration;
        }
        return asset;
      }),
    ).then((updatedAsset) => {
      setTempBaseAsset(updatedAsset);
    });
  }, [getVideoDuration, tempBaseAsset]);

  return (
    <div>
      <div className=" pt-6 space-y-4 ">
        {tempBaseAsset &&
          tempBaseAsset.map((asset, index) =>
            asset.type === 'image' ? (
              <div key={`assets-${index}`}>
                <TextV2 level={4} className=" font-medium ">
                  Image {index + 1}
                </TextV2>
                <div
                  className=" h-[100px] mt-2 flex gap-2 cursor-pointer "
                  onClick={() => {
                    setSearchMediaDialogProps({ index, type: asset.type });
                    setIsDialogOpen(true);
                  }}
                >
                  <Image
                    src={changeWidthHeightOfImageUrl(asset.url, {
                      width: 100,
                      height: 100,
                      fit: 'cover',
                    })}
                    key={asset.url}
                    width={100}
                    height={100}
                    alt={`template image ${index + 1}`}
                    className=" rounded-lg "
                  />
                  <div className=" flex-1 flex justify-center items-center rounded-lg bg-blue-2/15 border-2 border-dashed border-gray-500 ">
                    <TextV2 level={4}>Change</TextV2>
                  </div>
                </div>
              </div>
            ) : (
              <div key={asset.url}>
                <TextV2 level={4} className=" font-medium ">
                  Video {index + 1}
                </TextV2>

                <div className=" mt-2 flex gap-2 ">
                  <Button
                    variant={'custom'}
                    className=" bg-blue-2/15 border-2 border-blue-2/30 hover:bg-blue-2/10 "
                    onClick={() => {
                      setIsPreviewDialogOpen(true);
                      setPreviewMediaDetails(asset);
                    }}
                  >
                    <FaPlay size={20} />
                    Preview
                  </Button>

                  <div
                    className=" flex-1 flex justify-center items-center rounded-lg bg-blue-2/15 border-2 border-dashed border-gray-500 min-h-12 cursor-pointer "
                    onClick={() => {
                      setSearchMediaDialogProps({ index, type: asset.type });
                      setIsDialogOpen(true);
                    }}
                  >
                    <TextV2 level={4}>Change</TextV2>
                  </div>
                </div>
              </div>
            ),
          )}
      </div>

      {searchMediaDialogProps && (
        <SearchMediaOrUploadDialog
          isOpen={isDialogOpen}
          setIsOpen={setIsDialogOpen}
          title={`Change ${searchMediaDialogProps.type}`}
          showSection="search"
          type={searchMediaDialogProps.type}
          fileUpload={searchMediaFileUpload}
          updateFile={searchMediaUpdateFile}
          maxFileSizeInKB={
            searchMediaDialogProps.type === 'image'
              ? MAX_IMAGE_FILE_UPLOAD_SIZE_KB
              : MAX_VIDEO_UPLOAD_FILE_SIZE_KB
          }
          onClose={() => setSearchMediaDialogProps(undefined)}
        />
      )}

      {uploadFileAndGetS3UrlMutation.isLoading && <FullScreenLoader />}
      <PreviewMediaDialog
        isDialogOpen={isPreviewDialogOpen}
        setIsDialogOpen={setIsPreviewDialogOpen}
        previewMediaDetails={previewMediaDetails}
        setPreviewMediaDetails={setPreviewMediaDetails}
      />
    </div>
  );
};

export default EditBaseAssets;
