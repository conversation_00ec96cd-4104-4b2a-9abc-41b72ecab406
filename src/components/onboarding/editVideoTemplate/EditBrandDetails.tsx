import { getCommonHeaders } from '@/actions';
import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { useUser } from '@/components/context/UserProvider';
import Input from '@/components/lib/Input';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import TextV2 from '@/components/lib/typography/TextV2';
import { showToastMessage } from '@/modules/toast';
import { IFpVideoData } from '@/remotion/types';
import {
  changeWidthHeightOfImageUrl,
  logApiErrorAndShowToastMessage,
} from '@/utils';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { useMutation } from 'react-query';

interface IEditBrandDetailsProps {
  brandDetails: NonNullable<IFpVideoData['branding']>;
  updateBrandDetails: (
    brandDetails: NonNullable<IFpVideoData['branding']>,
  ) => void;
}

const EditBrandDetails = (props: IEditBrandDetailsProps) => {
  const { brandDetails, updateBrandDetails } = props;
  const { user } = useUser();
  const [tempBrandDetails, setTempBrandDetails] = useState(brandDetails);

  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);

  const handleImageFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) {
      showToastMessage('something went wrong, please try again', 'error');
      return;
    }

    uploadLogoImageMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        data: {
          file,
        },
      })
      .then((response) => {
        setTempBrandDetails(
          (prev) =>
            ({
              ...prev,
              logo: { width: 100, height: 100, url: response.data.s3_url },
            }) as NonNullable<IFpVideoData['branding']>,
        );
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'EditBrandDetails.uploadLogoImageMutation',
        );
      })
      .finally(() => {
        e.target.files = null;
        e.target.value = '';
      });
  };

  const handleBrandNameChange = (brandName: string) => {
    setTempBrandDetails((prev) => ({ ...prev, brand_name: brandName }));
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateBrandDetails(tempBrandDetails);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [tempBrandDetails, updateBrandDetails]);

  return (
    <div className=" pt-6 space-y-4  ">
      <div>
        <TextV2 level={4} className=" font-medium ">
          Logo
        </TextV2>

        <label className=" mt-2 flex gap-2 cursor-pointer ">
          {tempBrandDetails?.logo?.url && (
            <Image
              src={changeWidthHeightOfImageUrl(tempBrandDetails.logo.url, {
                width: 100,
                height: 100,
                fit: 'cover',
              })}
              width={100}
              height={100}
              alt={`${tempBrandDetails?.brand_name} logo image`}
              className=" rounded-lg  "
            />
          )}
          <div className=" flex-1 flex justify-center items-center rounded-lg bg-blue-2/15 border-2 border-dashed border-gray-500 min-h-12 ">
            {uploadLogoImageMutation.isLoading ? (
              <SpinnerLoader size={32} borderWidth={4} />
            ) : (
              <TextV2 level={4}>Change</TextV2>
            )}

            <input
              type="file"
              className=" sr-only "
              accept="image/*"
              onChange={(e) => handleImageFileUpload(e)}
              disabled={uploadLogoImageMutation.isLoading}
            />
          </div>
        </label>
      </div>
      <div>
        <label>
          <TextV2 level={4} className=" font-medium ">
            Brand Name
          </TextV2>
          <Input
            className=" !p-3 !bg-blue-2/15 focus:!ring-1 mt-2 "
            placeholder="eg. Book Now, Get Started"
            value={tempBrandDetails?.brand_name}
            onChange={(e) => handleBrandNameChange(e.target.value)}
          />
        </label>
      </div>
    </div>
  );
};

export default EditBrandDetails;
