import SpinnerLoader from '@/components/lib/SpinnerLoader';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { IoMusicalNotes } from 'react-icons/io5';

interface IPreviewMediaProps {
  type?: 'video' | 'audio' | 'image';
  url?: string;
}

const PreviewMedia = (props: IPreviewMediaProps) => {
  const { type, url } = props;
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  useEffect(() => {
    if (type === 'image') {
      {
        setIsImageLoaded(false);
      }
    }
  }, [url, type]);

  if (!type || !url) return null;

  switch (type) {
    case 'image':
      return (
        <div className=" flex items-center justify-center w-full relative h-full min-h-[100px] ">
          <Image
            src={url}
            key={url}
            alt="Image preview"
            width={800}
            height={800}
            onLoad={() => setIsImageLoaded(true)}
            className=" relative z-10 max-h-[70vh] h-full w-auto object-contain rounded-md "
          />
          <div className=" absolute w-full h-full left-0 top-0 flex justify-center z-0 ">
            {!isImageLoaded && (
              <div className=" bg-gray-600 my-6 flex-1 flex items-center justify-center ">
                <SpinnerLoader size={40} borderWidth={4} />
              </div>
            )}
          </div>
        </div>
      );
    case 'video':
      return (
        <video
          src={url}
          controls
          className=" w-full h-full max-h-[70vh] rounded-md "
        />
      );
    case 'audio':
      return (
        <div className=" md:p-8 py-4 bg-primary/5 rounded-lg flex flex-col items-center ">
          <IoMusicalNotes size={64} className=" text-gray-500 mb-4" />
          <p className=" text-center text-lg font-medium mb-4">
            {url.split('/').pop()}
          </p>
          <audio src={url} controls className=" w-full " />
        </div>
      );
    default:
      return (
        <div className=" text-center md:p-8 py-4 ">
          <p>Unable to preview this media type</p>
          <p className=" text-center text-sm text-muted-foreground mt-2 ">
            {url}
          </p>
        </div>
      );
  }
};

export default PreviewMedia;
