import { getCommonHeaders } from '@/actions';
import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { useUser } from '@/components/context/UserProvider';
import Button from '@/components/lib/Button';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import TextV2 from '@/components/lib/typography/TextV2';
import { showToastMessage } from '@/modules/toast';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { FaPlay } from 'react-icons/fa';
import { useMutation } from 'react-query';
import PreviewMediaDialog from './PreviewMediaDialog';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { IFpVideoData } from '@/remotion/types';

interface IEditBackgroundMusicProps {
  backgroundMusic: IFpVideoData['base_audio'];
  updateBackgroundMusic: (backgroundMusic: IFpVideoData['base_audio']) => void;
}

const EditBackgroundMusic = (props: IEditBackgroundMusicProps) => {
  const { backgroundMusic, updateBackgroundMusic } = props;
  const { user } = useUser();
  const [tempBackgroundMusic, setTempBackgroundMusic] =
    useState(backgroundMusic);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [previewMediaDetails, setPreviewMediaDetails] = useState<{
    type: 'audio' | 'video' | 'image';
    url: string;
  }>();

  const uploadFileAndGetS3UrlMutation = useMutation(uploadFileAndGetS3Url);

  const handleFileUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) {
      showToastMessage('something went wrong, please try again', 'error');
      return;
    }

    uploadFileAndGetS3UrlMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        data: {
          file: file,
        },
      })
      .then((response) => {
        setTempBackgroundMusic((prev) => ({
          ...prev,
          url: response.data.s3_url,
        }));
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'EditBackgroundMusic.uploadFileAndGetS3UrlMutation',
        );
      })
      .finally(() => {
        e.target.files = null;
        e.target.value = '';
      });
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateBackgroundMusic(tempBackgroundMusic);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [tempBackgroundMusic, updateBackgroundMusic]);
  return (
    <div>
      <div className=" pt-6 space-y-4 ">
        <div>
          <div className=" mt-2 flex gap-2 ">
            <Button
              variant={'custom'}
              className=" bg-blue-2/15 border-2 border-blue-2/30 hover:bg-blue-2/10 "
              onClick={() => {
                setIsDialogOpen(true);
                setPreviewMediaDetails({
                  type: 'audio',
                  url: tempBackgroundMusic?.url ?? '',
                });
              }}
            >
              <FaPlay size={20} />
              Preview
            </Button>

            <label className=" flex-1 flex justify-center items-center rounded-lg bg-blue-2/15 border-2 border-dashed border-gray-500 min-h-12 cursor-pointer ">
              {uploadFileAndGetS3UrlMutation.isLoading ? (
                <SpinnerLoader size={32} borderWidth={4} />
              ) : (
                <TextV2 level={4}>Change</TextV2>
              )}

              <input
                type="file"
                className=" sr-only "
                accept="audio/*"
                onChange={(e) => handleFileUpload(e)}
                disabled={uploadFileAndGetS3UrlMutation.isLoading}
              />
            </label>
          </div>
        </div>
      </div>

      {uploadFileAndGetS3UrlMutation.isLoading && <FullScreenLoader />}
      <PreviewMediaDialog
        isDialogOpen={isDialogOpen}
        setIsDialogOpen={setIsDialogOpen}
        previewMediaDetails={previewMediaDetails}
        setPreviewMediaDetails={setPreviewMediaDetails}
      />
    </div>
  );
};

export default EditBackgroundMusic;
