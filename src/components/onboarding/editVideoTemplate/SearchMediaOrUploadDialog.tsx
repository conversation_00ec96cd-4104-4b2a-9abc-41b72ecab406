import { getCommonHeaders } from '@/actions';
import { searchImages, searchVideos } from '@/actions/fpVideo';
import { useUser } from '@/components/context/UserProvider';
import Button from '@/components/lib/Button';
import Dialog from '@/components/lib/Dialog';
import DragAndDrop from '@/components/lib/DragAndDrop';
import Input from '@/components/lib/Input';
import TextV2 from '@/components/lib/typography/TextV2';
import { useDebounce } from '@/hooks/useDebounce';
import { changeWidthHeightOfImageUrl } from '@/utils';
import Image from 'next/image';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { FiUpload } from 'react-icons/fi';
import { IoClose, IoSearch } from 'react-icons/io5';
import { useQuery } from 'react-query';
import PreviewMedia from './PreviewMedia';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { MAX_IMAGE_FILE_UPLOAD_SIZE_KB } from '@/constants';
import { showToastMessage } from '@/modules/toast';

interface ISearchMediaOrUploadDialogProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  showSection: 'search' | 'upload';
  type: 'image' | 'video';
  fileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  updateFile: (url: string) => void;
  maxFileSizeInKB?: number;
  onClose?: () => void;
}

const SearchMediaOrUploadDialog = (props: ISearchMediaOrUploadDialogProps) => {
  const {
    isOpen,
    setIsOpen,
    title,
    fileUpload,
    updateFile,
    type,
    maxFileSizeInKB = MAX_IMAGE_FILE_UPLOAD_SIZE_KB,
    onClose,
  } = props;
  const [showSection, setShowSection] = useState<'search' | 'upload'>(
    props.showSection,
  );

  return (
    <Dialog
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      onClose={onClose && onClose}
      className=" max-h-[80vh] flex flex-col space-y-6 border-2 border-off-white/40 p-6 rounded-xl bg-blue-1 w-full md:min-w-[600px] max-lg:max-w-[90vw] max-w-[70vw] overflow-auto "
    >
      <div className=" flex justify-between items-center gap-4 ">
        <div className=" flex items-center gap-3 text-gray-100 ">
          <TextV2 level={3}>{title}</TextV2>
        </div>
        <button onClick={() => setIsOpen(false)}>
          <IoClose size={24} className=" cursor-pointer " />
        </button>
      </div>

      <div className=" bg-blue-2/15 p-2 flex gap-2.5 rounded-xl max-sm:-mx-4 ">
        <Button
          className=" flex-1 max-sm:px-2 "
          variant={showSection === 'search' ? 'default' : 'ghost'}
          onClick={() => setShowSection('search')}
        >
          <IoSearch size={20} />
          <span>
            Search <span className=" max-sm:hidden ">{type}</span>
          </span>
        </Button>
        <Button
          className=" flex-1 max-sm:px-2 "
          variant={showSection === 'upload' ? 'default' : 'ghost'}
          onClick={() => setShowSection('upload')}
        >
          <FiUpload size={20} />
          <span>
            Upload <span className=" max-sm:hidden ">{type}</span>
          </span>
        </Button>
      </div>

      <div className=" flex-1 flex flex-col overflow-auto ">
        {showSection === 'search' ? (
          <SearchSection type={type} updateFile={updateFile} />
        ) : (
          <UploadSection
            type={type}
            fileUpload={fileUpload}
            maxFileSizeInKB={maxFileSizeInKB}
          />
        )}
      </div>
    </Dialog>
  );
};

const UploadSection = ({
  type,
  fileUpload,
  maxFileSizeInKB,
}: {
  type: 'image' | 'video';
  fileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxFileSizeInKB: number;
}) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleFileUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files).filter((file) => {
        const fileSizeInKb = file.size / 1024;
        if (fileSizeInKb > maxFileSizeInKB) {
          showToastMessage(
            `File "${file.name}" exceeds the maximum size of ${maxFileSizeInKB} KB.`,
            'error',
          );
          return false;
        }
        return true;
      });

      if (files.length > 0) {
        const dataTransfer = new DataTransfer();
        files.forEach((file) => dataTransfer.items.add(file));
        e.target.files = dataTransfer.files;
        fileUpload(e);
      }
    }
  };

  return (
    <div className=" space-y-4 ">
      <label>
        <DragAndDrop
          setIsDragging={setIsDragging}
          handleUpload={handleFileUpload}
          acceptedTypes={[`${type}/*`]}
          maxFileSizeInKB={maxFileSizeInKB}
          className={` flex flex-col items-center justify-center gap-4 h-[200px] border-2 border-dashed rounded-lg cursor-pointer ${isDragging ? ' border-gray-100 text-gray-50 ' : ' border-gray-500 text-gray-400 '} `}
        >
          <FiUpload size={40} />
          <TextV2 className=" text-center " level={4}>
            Drag and drop your images here, or click to select
          </TextV2>
        </DragAndDrop>
        <input
          type="file"
          className=" sr-only"
          onChange={handleFileUpload}
          accept={type === 'image' ? 'image/*' : 'video/*'}
        />
      </label>
    </div>
  );
};

const SearchSection = ({
  updateFile,
  type,
}: {
  updateFile: (url: string) => void;
  type: 'image' | 'video';
}) => {
  const { user } = useUser();
  const [searchText, setSearchText] = useState('');
  const [showPreviewDetails, setShowPreviewDetails] = useState<{
    type: 'image' | 'audio' | 'video';
    url: string;
  }>();
  const [searchImagesData, setSearchImagesData] = useState<IImageData[]>([]);
  const [searchVideosData, setSearchVideosData] = useState<IPexelsVideoData[]>(
    [],
  );

  const { debouncedValue } = useDebounce(searchText, 500);
  const searchImagesQuery = useQuery(
    ['searchImages', debouncedValue],
    () => {
      return searchImages({
        headers: getCommonHeaders(user),
        queryParams: {
          query: debouncedValue,
        },
      });
    },
    {
      enabled: !!(debouncedValue && type === 'image'),
    },
  );

  const searchVideosQuery = useQuery(
    ['searchVideos', debouncedValue],
    () => {
      return searchVideos({
        headers: getCommonHeaders(user),
        queryParams: {
          query: debouncedValue,
        },
      });
    },
    {
      enabled: !!(debouncedValue && type === 'video'),
    },
  );

  useEffect(() => {
    if ((searchImagesQuery.data?.data ?? []).length > 0) {
      setSearchImagesData(searchImagesQuery.data?.data ?? []);
    }
  }, [searchImagesQuery.data]);

  useEffect(() => {
    if ((searchVideosQuery.data?.data ?? []).length > 0)
      setSearchVideosData(searchVideosQuery.data?.data ?? []);
  }, [searchVideosQuery.data]);

  const getVideoUrlOfRequiredQuality = (
    videoFiles: IPexelsVideoData['video_files'],
    {
      quality: requiredQuality,
      width: requiredWidth,
    }: { quality: 'hd' | 'sd' | 'uhd'; width: number },
  ) => {
    const uhdVideos: Array<IPexelsVideoFile> = [];
    const hdVideos: Array<IPexelsVideoFile> = [];
    const sdVideos: Array<IPexelsVideoFile> = [];

    videoFiles.forEach((item) => {
      switch (item.quality) {
        case 'sd':
          sdVideos.push(item);
          break;
        case 'hd':
          hdVideos.push(item);
          break;
        case 'uhd':
          uhdVideos.push(item);
          break;
      }
    });

    const qualityToArr = {
      uhd: uhdVideos,
      hd: hdVideos,
      sd: sdVideos,
    };

    const arr = qualityToArr[requiredQuality];
    if (arr.length === 0) {
      const pixelVideoFile =
        hdVideos.find((item) => item.width === 720) ||
        hdVideos[0] ||
        videoFiles[0];
      return pixelVideoFile.link;
    }

    const pixelVideoFile =
      arr.find((item) => item.width === requiredWidth) || arr[0];
    return pixelVideoFile.link;
  };

  return (
    <div className=" flex-1 flex flex-col space-y-4 overflow-auto ">
      <div className=" flex items-center gap-3 p-0.5 ">
        <div className=" flex-1 relative flex items-center ">
          <Input
            className=" flex-1 !py-3 self-stretch !pr-9 "
            type="text"
            placeholder={` search for ${type}s...`}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
          {((type === 'image' && searchImagesQuery.isLoading) ||
            (type === 'video' && searchVideosQuery.isLoading)) && (
            <div className=" absolute right-2  ">
              <SpinnerLoader size={24} borderWidth={3} />
            </div>
          )}
        </div>
        <Button
          onClick={() =>
            debouncedValue !== '' &&
            (type === 'image'
              ? searchImagesQuery.refetch()
              : searchVideosQuery.refetch())
          }
        >
          Search
        </Button>
      </div>
      <div className=" flex-1 flex overflow-auto ">
        <div className=" flex-1 min-h-[125px] overflow-auto flex flex-wrap gap-4 justify-center ">
          {type === 'image' && searchImagesData.length > 0 ? (
            searchImagesData.map((item) => (
              <Image
                src={changeWidthHeightOfImageUrl(item.url, {
                  width: 200,
                  height: 200,
                })}
                width={200}
                height={200}
                alt={item.alt_description}
                className=" rounded-md border-2 border-gray-300 object-cover w-[120px] h-[120px] md:w-[200px] md:h-[200px] cursor-pointer "
                onClick={() => {
                  setShowPreviewDetails({
                    type,
                    url: item.url,
                  });
                }}
                key={item.id}
              />
            ))
          ) : type === 'video' && searchVideosData.length > 0 ? (
            searchVideosData.map((item) => (
              <Image
                width={200}
                height={200}
                alt="video"
                className=" rounded-md border-2 border-gray-300 object-cover w-[200px] h-[200px] cursor-pointer "
                src={changeWidthHeightOfImageUrl(item.image, {
                  width: 200,
                  height: 200,
                })}
                onClick={() => {
                  setShowPreviewDetails({
                    type,
                    url: getVideoUrlOfRequiredQuality(item.video_files, {
                      quality: 'hd',
                      width: 720,
                    }),
                  });
                }}
                key={item.id}
              />
            ))
          ) : (
            <TextV2 level={4} className=" text-gray-500 self-center ">
              Search to show {type}s
            </TextV2>
          )}
        </div>
        {showPreviewDetails && (
          <div className=" md:max-w-[400px] w-full px-4 md:ml-4 relative flex flex-col gap-y-2 items-center justify-center md:border-l-2 border-gray-500 ">
            <IoClose
              size={24}
              className=" self-end cursor-pointer "
              onClick={() => setShowPreviewDetails(undefined)}
            />
            <div className=" flex-1 flex self-stretch items-center justify-center overflow-auto ">
              <PreviewMedia {...showPreviewDetails} />
            </div>
            <div className=" ">
              <Button
                variant={'secondary'}
                onClick={() => updateFile(showPreviewDetails.url)}
              >
                Select
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchMediaOrUploadDialog;
