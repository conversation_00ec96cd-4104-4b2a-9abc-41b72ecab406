import TextV2 from '@/components/lib/typography/TextV2';
import React, { useEffect, useState } from 'react';
import LoadSceneAssets from './LoadSceneAssets';
import LoadSceneTexts from './LoadSceneTexts';
import { IFpVideoData } from '@/remotion/types';

interface IEditSceneProps {
  scenesData: NonNullable<IFpVideoData['scenes']>;
  updateScenes: (scenes: IFpVideoData['scenes']) => void;
}

const EditScene = (props: IEditSceneProps) => {
  const { scenesData, updateScenes } = props;
  const [tempScenesData, setTempScenesData] = useState<
    NonNullable<IFpVideoData['scenes']>
  >(JSON.parse(JSON.stringify(scenesData)));
  const [selectedSceneIndex, setSelectedSceneIndex] = useState(0);

  const changeScene = (index: number) => {
    setSelectedSceneIndex(index);
  };

  const handleSceneChange = (
    scene: NonNullable<IFpVideoData['scenes']>[number],
  ) => {
    setTempScenesData((prev) =>
      prev?.map((item, index) => (index === selectedSceneIndex ? scene : item)),
    );
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateScenes(tempScenesData);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [tempScenesData, updateScenes]);

  return (
    <div className=" pt-5 ">
      <div className=" bg-white/15 p-1.5 rounded-xl flex gap-2 border border-gray-500 overflow-auto no-scrollbar ">
        {tempScenesData.map((_, index) => {
          return (
            <div
              className={` flex-1 whitespace-nowrap text-center rounded-lg py-2 px-3 cursor-pointer ${selectedSceneIndex === index ? ' bg-red-1 ' : ' '} `}
              onClick={() => changeScene(index)}
              key={`scenes-${index}`}
            >
              <TextV2 level={4}>Scene {index + 1}</TextV2>
            </div>
          );
        })}
      </div>

      <div className=" pt-6 space-y-4 ">
        <LoadSceneTexts
          texts={tempScenesData[selectedSceneIndex].texts}
          updateSceneTexts={(
            changes: NonNullable<IFpVideoData['scenes']>[number]['texts'],
          ) =>
            handleSceneChange({
              ...tempScenesData[selectedSceneIndex],
              texts: changes,
            })
          }
        />
        <LoadSceneAssets
          assets={tempScenesData[selectedSceneIndex].assets}
          updateSceneAssets={(
            changes: NonNullable<IFpVideoData['scenes']>[number]['assets'],
          ) => {
            handleSceneChange({
              ...tempScenesData[selectedSceneIndex],
              assets: changes,
            });
          }}
        />
      </div>
    </div>
  );
};

export default EditScene;
