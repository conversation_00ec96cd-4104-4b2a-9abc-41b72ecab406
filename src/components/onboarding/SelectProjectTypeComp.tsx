'use client';
import React, { useEffect, useState } from 'react';
import HeadingV2 from '../lib/typography/HeadingV2';
// import DashBoardCards from './DashBoardCards';
// import { BsStars } from 'react-icons/bs';
// import { FaRegUser } from 'react-icons/fa';
// import TextV2 from '../lib/typography/TextV2';
import Button from '../lib/Button';
import { useOnboarding } from '../context/OnboardingProvider';
import Input from '../lib/Input';
import SpinnerLoader from '../lib/SpinnerLoader';
import { OnboardingStepIds } from '@/types/onBoarding';
import { FaChevronRight } from 'react-icons/fa6';
import FullScreenLoader from '../lib/FullScreenLoader';

const SelectProjectTypeComp = () => {
  const {
    projectDetails,
    isSavingProject,
    saveProjectDetails,
    isInitialLoading: showFullPageLoader,
  } = useOnboarding();

  const [tempProjectDetails, setTempProjectDetails] = useState<
    Partial<IBannerbotProject>
  >(JSON.parse(JSON.stringify(projectDetails)));

  useEffect(() => {
    // console.log(projectDetails);
    // console.log(tempProjectDetails);

    setTempProjectDetails(JSON.parse(JSON.stringify(projectDetails)));
  }, [projectDetails]);

  // const [error, setError] = useState({
  //   title: '',
  //   type: '',
  // });

  const handleNextStepClick = () => {
    // if (!projectDetails.type) {
    //   setError((prev) => ({ ...prev, type: 'Please select a type' }));
    //   return;
    // }

    // setError({
    //   title: '',
    //   type: '',
    // });

    saveProjectDetails(
      tempProjectDetails,
      OnboardingStepIds.SELECT_PROJECT_TYPE,
    );
  };

  return (
    <>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleNextStepClick();
        }}
      >
        <div>
          <HeadingV2 level={2}>Give a title to your project</HeadingV2>
          <Input
            placeholder="Enter the title"
            className="mt-5 md:mt-6"
            required
            onChange={(e) =>
              setTempProjectDetails((prev) => ({
                ...prev,
                title: e.target.value,
              }))
            }
            value={tempProjectDetails.title}
          />
        </div>
        {/* <div className=" mt-8 ">
          <div className=" flex items-baseline gap-2 ">
            <HeadingV2 level={2}>
              Which type of project you want to start?
            </HeadingV2>
            {error.type && (
              <TextV2 level={4} className=" text-red-1 ">
                (*{error.type})
              </TextV2>
            )}
          </div>
          <div className=" flex max-md:flex-col gap-5 justify-center mt-6 ">
            <DashBoardCards
              selectable
              selected={tempProjectDetails?.type === 'AI_ONLY'}
              onClick={() =>
                setTempProjectDetails((prev) => ({ ...prev, type: 'AI_ONLY' }))
              }
            >
              <div className=" p-4 rounded-full w-fit bg-white/15 ">
                <BsStars size={24} />
              </div>
              <div className=" mt-6 ">
                <HeadingV2 level={3}>100% AI Lead Ad</HeadingV2>
                <TextV2 level={2} className=" mt-1.5">
                  Fully Automated ad creation powered by artificial intelligence
                </TextV2>
              </div>
            </DashBoardCards>
            <DashBoardCards
              selectable
              selected={tempProjectDetails.type === 'AI_HUMAN'}
              onClick={() =>
                setTempProjectDetails((prev) => ({ ...prev, type: 'AI_HUMAN' }))
              }
            >
              <div className=" flex justify-between items-center ">
                <div className=" p-4 rounded-full w-fit bg-white/15 ">
                  <FaRegUser size={24} />
                </div>
                <Button variant={'custom'} size={'pill'} className=" bg-red-1 ">
                  Pro Plan
                </Button>
              </div>
              <div className=" mt-6 ">
                <HeadingV2 level={3}>AI + Human Assisted Project</HeadingV2>
                <TextV2 level={2} className=" mt-1.5">
                  Combine AI efficiency with human expertise for optimal result
                </TextV2>
              </div>
            </DashBoardCards>
          </div>
        </div> */}
        <div className=" flex justify-end ">
          <Button className="mt-8" type="submit" disabled={isSavingProject}>
            <>
              Next
              {isSavingProject ? (
                <SpinnerLoader size={20} borderWidth={2} />
              ) : (
                <FaChevronRight size={16} />
              )}
            </>
          </Button>
        </div>
      </form>
      {showFullPageLoader && <FullScreenLoader />}
    </>
  );
};

export default SelectProjectTypeComp;
