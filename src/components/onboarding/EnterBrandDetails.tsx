import React, { useCallback, useMemo, useState } from 'react';
import DashboardCard from './DashboardCard';
import HeadingV2 from '../lib/typography/HeadingV2';
import Input from '../lib/Input';
import FileInputBox from './FileInputBox';
import { useOnboarding } from '../context/OnboardingProvider';
import Button from '../lib/Button';
import SpinnerLoader from '../lib/SpinnerLoader';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa6';
import { OnboardingStepIds } from '@/types/onBoarding';
import FullScreenLoader from '../lib/FullScreenLoader';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import { useMutation } from 'react-query';
import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import {
  converNormalUrlToCdnUrl,
  logApiErrorAndShowToastMessage,
} from '@/utils';

const EnterBrandDetails = () => {
  const { user } = useUser();
  const {
    projectDetails,
    isOnboardingCompleted,
    isSavingProject,
    previousStep,
    saveProjectDetails,
  } = useOnboarding();

  const [tempProjectDetails, setTempProjectDetails] =
    useState<IBannerbotBusinessDetails>(
      JSON.parse(JSON.stringify(projectDetails.details?.business_details)),
    );

  const [showFullPageLoader, setShowFullPageLoader] = useState(false);

  const [logoImage, setLogoImage] = useState<
    {
      preview: string;
      name: string;
      file: File;
      width: number;
      height: number;
      sizeInKb: number;
    }[]
  >();

  const previousUploads = useMemo(
    () => tempProjectDetails.business_logo?.square,
    [tempProjectDetails.business_logo],
  );

  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);

  const uploadAllFilesAndGetUrl = useCallback(async () => {
    if ((logoImage ?? []).length === 0) {
      return Promise.resolve();
    }

    // upload to BE and get url
    setShowFullPageLoader(true);
    const uploadPromises = (logoImage ?? []).map((image) =>
      uploadLogoImageMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file: image.file,
          },
          queryParams: {},
        })
        .then((response) => {
          return {
            url:
              converNormalUrlToCdnUrl(response.data.s3_url, {
                width: 120,
                height: 120,
                q: 100,
                fit: 'pad',
              }) + '?v=1', // added the query 'v' so that cdn doesn't give any problem (this will not fix the cors problem fully)
            width: 120,
            height: 120,
          };
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error as Error,
            'EnterBrandDetails.uploadAllFilesAndGetUrl',
          );
          // reset in case of error
          setLogoImage(undefined);
        }),
    );

    return Promise.all(uploadPromises)
      .then((response) => {
        return response.filter((item) => !!item);
      })
      .finally(() => {
        setShowFullPageLoader(false);
      });
  }, [logoImage, uploadLogoImageMutation, user]);

  const handleSubmit = useCallback(async () => {
    const imageUrls = await uploadAllFilesAndGetUrl();

    const image =
      imageUrls && imageUrls.length > 0 ? imageUrls[0] : previousUploads;

    /*if (!image) {
      showToastMessage('Please upload your brand logo', 'error');
      return;
    }*/

    saveProjectDetails(
      {
        ...projectDetails,
        details: {
          ...projectDetails.details,
          business_details: {
            ...tempProjectDetails,
            business_logo: image
              ? {
                  square: image,
                }
              : null,
          },
          ad_details: {
            types: projectDetails.details?.ad_details?.types ?? [],
          },
        },
      },
      OnboardingStepIds.ENTER_BRAND_DETAILS,
    );
  }, [
    uploadAllFilesAndGetUrl,
    projectDetails,
    tempProjectDetails,
    previousUploads,
    saveProjectDetails,
  ]);

  return (
    <DashboardCard className=" max-w-5xl mx-auto ">
      <form
        className="flex flex-col gap-5 md:gap-6"
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <div className=" flex flex-col  ">
          <HeadingV2 level={3}>Enter your brand name</HeadingV2>
          <Input
            placeholder="Your brand name"
            required
            className=" mt-4 md:mt-5 "
            value={tempProjectDetails.business_name}
            onChange={(e) => {
              setTempProjectDetails((prev) => ({
                ...prev,
                business_name: e.target.value,
              }));
            }}
          />
        </div>
        <div className=" flex flex-col space-y-5 md:space-y-6 ">
          <div className=" flex items-baseline gap-2 ">
            <HeadingV2 level={3}>Upload your logo (Optional)</HeadingV2>
          </div>

          <FileInputBox
            text="Drop your logo here"
            images={logoImage}
            previousUploads={previousUploads ? [previousUploads] : []}
            addImage={(data) => {
              setLogoImage([data]);
            }}
            removeImage={() => setLogoImage([])}
            removePreviousUpload={() => {
              setTempProjectDetails((prev) => ({
                ...prev,
                business_logo: undefined,
              }));
            }}
            multipleFileUpload={false}
            isInputDisabled={isOnboardingCompleted || isSavingProject}
          />
        </div>
        <div className=" flex justify-between ">
          <Button
            variant={'outline'}
            size={'responsive'}
            onClick={() => previousStep(OnboardingStepIds.ENTER_BRAND_DETAILS)}
            disabled={isSavingProject}
          >
            <FaChevronLeft size={16} /> Previous
          </Button>
          <Button type="submit" disabled={isSavingProject} size={'responsive'}>
            <>
              Next
              {isSavingProject ? (
                <SpinnerLoader size={20} borderWidth={2} />
              ) : (
                <FaChevronRight size={16} />
              )}
            </>
          </Button>
        </div>
      </form>
      {(showFullPageLoader || isSavingProject) && <FullScreenLoader />}
    </DashboardCard>
  );
};

export default EnterBrandDetails;
