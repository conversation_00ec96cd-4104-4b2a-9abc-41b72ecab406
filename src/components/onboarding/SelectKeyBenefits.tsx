import React, { useEffect, useState } from 'react';
import DashBoardCards from './DashboardCard';
import HeadingV2 from '../lib/typography/HeadingV2';
import Input from '../lib/Input';
import Button from '../lib/Button';
import { IoClose } from 'react-icons/io5';
import { useOnboarding } from '../context/OnboardingProvider';
import SpinnerLoader from '../lib/SpinnerLoader';
import { FaChevronLeft, FaChevronRight, FaPlus } from 'react-icons/fa6';
import { OnboardingStepIds } from '@/types/onBoarding';
import FullScreenLoader from '../lib/FullScreenLoader';

const SelectKeyBenefits = () => {
  const { projectDetails, saveProjectDetails, isSavingProject, previousStep } =
    useOnboarding();

  const [tempProjectDetails, setTempProjectDetails] = useState<{
    business_details?: Partial<IBannerbotBusinessDetails>;
    ai_key_benefits?: string[];
  }>(JSON.parse(JSON.stringify(projectDetails.details)));

  const handleDeleteKeyBenefit = (index: number) => {
    const keyBenefitsObj =
      tempProjectDetails.business_details?.key_benefits ?? [];
    keyBenefitsObj.splice(index, 1);

    if (keyBenefitsObj?.length === 0) {
      keyBenefitsObj.push('');
    }

    setTempProjectDetails((prev) => ({
      ...prev,
      business_details: {
        ...prev.business_details,
        key_benefits: keyBenefitsObj,
      },
    }));
  };

  const handleAddMore = () => {
    if (
      (tempProjectDetails.business_details?.key_benefits ?? []).some(
        (item) => item === '',
      )
    )
      return;

    setTempProjectDetails((prev) => ({
      ...prev,
      business_details: {
        ...prev.business_details,
        key_benefits: [...(prev.business_details?.key_benefits ?? []), ''],
      },
    }));
  };

  const handelEditKeyBenefit = (index: number, keyBenefit: string) => {
    const keyBenefitsObj =
      tempProjectDetails.business_details?.key_benefits ?? [];

    keyBenefitsObj[index] = keyBenefit;

    setTempProjectDetails((prev) => ({
      ...prev,
      business_details: {
        ...prev.business_details,
        key_benefits: keyBenefitsObj,
      },
    }));
  };

  const handleSubmit = () => {
    // here the use can send key benefit which can be [''], should i handle this case?
    saveProjectDetails(
      {
        ...projectDetails,
        details: {
          ...tempProjectDetails,
          business_details: {
            ...tempProjectDetails.business_details,
          },
          ad_details: {
            types: projectDetails.details?.ad_details?.types ?? [],
          },
        },
      },
      OnboardingStepIds.SELECT_KEY_BENEFITS,
    );
  };

  useEffect(() => {
    const tempObj = { ...projectDetails.details };

    if ((tempObj.business_details?.key_benefits?.length ?? []) === 0) {
      tempObj.business_details = {
        ...tempObj.business_details,
        key_benefits: tempObj.ai_key_benefits ?? [],
      };
    }

    setTempProjectDetails(JSON.parse(JSON.stringify(tempObj)));
  }, [projectDetails.details]);

  return (
    <DashBoardCards className=" max-w-5xl mx-auto ">
      <div className=" flex flex-col gap-5 md:gap-6">
        <div>
          <HeadingV2 level={3}>Enter key benefits for your product</HeadingV2>

          <div className=" mt-5 md:mt-6 space-y-4 ">
            {(tempProjectDetails.business_details?.key_benefits ?? []).map(
              (item, index) => (
                <KeyBenefitsContainer
                  keyBenefit={item}
                  editKeyBenefit={(keyBenefit: string) =>
                    handelEditKeyBenefit(index, keyBenefit)
                  }
                  deleteKeyBenefit={() => handleDeleteKeyBenefit(index)}
                  key={index}
                />
              ),
            )}
          </div>
        </div>
        <div>
          <div className=" sm:hidden mb-4 ">
            <Button
              variant="outline"
              size={'responsive'}
              onClick={handleAddMore}
              className=" w-full "
            >
              <FaPlus size={16} /> Add More
            </Button>
          </div>
          <div className=" flex justify-between gap-4 ">
            <Button
              variant={'outline'}
              size={'responsive'}
              onClick={() =>
                previousStep(OnboardingStepIds.SELECT_KEY_BENEFITS)
              }
              className=" max-sm:flex-1 max-sm:flex-shrink-0 "
            >
              <FaChevronLeft size={16} /> Previous
            </Button>
            <div className=" flex items-center justify-end gap-3 flex-1 ">
              <Button
                variant="outline"
                size={'responsive'}
                onClick={handleAddMore}
                className=" max-sm:hidden "
              >
                <FaPlus size={16} /> Add More
              </Button>
              <Button
                onClick={handleSubmit}
                size={'responsive'}
                disabled={isSavingProject}
                className=" max-sm:flex-1 max-sm:flex-shrink-0 "
              >
                Next
                {isSavingProject ? (
                  <SpinnerLoader size={20} borderWidth={2} />
                ) : (
                  <FaChevronRight size={16} />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
      {isSavingProject && <FullScreenLoader />}
    </DashBoardCards>
  );
};

const KeyBenefitsContainer = ({
  keyBenefit,
  editKeyBenefit,
  deleteKeyBenefit,
}: {
  keyBenefit: string;
  editKeyBenefit: (keyBenefit: string) => void;
  deleteKeyBenefit: () => void;
}) => {
  return (
    <div className=" flex items-center ">
      <Input
        placeholder="Type your product’s key benefits here..."
        value={keyBenefit}
        onChange={(e) => editKeyBenefit(e.target.value)}
      />
      <IoClose
        className=" flex-shrink-0 ml-3 max-md:w-8 cursor-pointer "
        size={36}
        onClick={deleteKeyBenefit}
      />
    </div>
  );
};

export default SelectKeyBenefits;
