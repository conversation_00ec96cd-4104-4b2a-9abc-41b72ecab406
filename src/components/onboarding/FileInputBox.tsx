import Image from 'next/image';
import React, { ChangeEventHandler, useState } from 'react';
import { FaImage } from 'react-icons/fa6';
import Button from '../lib/Button';
import { IoClose } from 'react-icons/io5';
import TextV2 from '../lib/typography/TextV2';
import { LuImageUp } from 'react-icons/lu';
import { getUpdatedCloudflareImageUrl } from '@/utils';
import DragAndDrop from '../lib/DragAndDrop';
import { showToastMessage } from '@/modules/toast';
import { MAX_IMAGE_FILE_UPLOAD_SIZE_KB } from '@/constants';

interface IFileInputBoxProps {
  previousUploads:
    | {
        url: string;
        width: number;
        height: number;
      }[]
    | undefined;
  removePreviousUpload: (index: number) => void;
  addImage: (data: {
    preview: string;
    name: string;
    file: File;
    width: number;
    height: number;
    sizeInKb: number;
  }) => void;
  isInputDisabled: boolean;
  multipleFileUpload: boolean;
  images:
    | {
        preview: string;
        name: string;
        file: File;
        width: number;
        height: number;
        sizeInKb: number;
      }[]
    | undefined;
  removeImage: (index: number) => void;
  text: string;
}

const FileInputBox = (props: IFileInputBoxProps) => {
  const {
    previousUploads,
    removePreviousUpload,
    addImage,
    isInputDisabled,
    multipleFileUpload,
    images,
    removeImage,
    text,
  } = props;

  const [isDragging, setIsDragging] = useState(false);

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const arrFiles = e.target.files ? Array.from(e.target?.files) : [];
    const files = multipleFileUpload ? arrFiles : [arrFiles[0]];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file) {
        const fileSizeInKb = file.size / 1024;

        if (fileSizeInKb > MAX_IMAGE_FILE_UPLOAD_SIZE_KB) {
          showToastMessage(
            `File "${file.name}" exceeds the maximum size of ${MAX_IMAGE_FILE_UPLOAD_SIZE_KB} KB.`,
            'error',
          );
          continue;
        }

        const fileName = file.name;
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new window.Image();
          img.onload = () => {
            const width = img.naturalWidth;
            const height = img.naturalHeight;
            addImage({
              preview: URL.createObjectURL(file),
              file,
              width,
              height,
              name: fileName,
              sizeInKb: fileSizeInKb,
            });
          };
          img.src = e.target?.result as string;
        };
        reader.readAsDataURL(file);
      }
    }

    // Reset the input's value to allow selecting the same file again
    e.target.value = '';
  };

  return (
    <div className="space-y-5 md:space-y-6">
      {(previousUploads ?? []).length > 0 && (
        <div className=" space-y-2 ">
          <div className=" text-gray-400 flex items-center gap-2  ">
            <FaImage size={20} /> <TextV2 level={3}>Previous uploads</TextV2>
          </div>

          <div className=" flex flex-wrap items-center justify-center gap-3 ">
            {previousUploads?.map((item, index) => (
              <div
                key={index}
                className="group relative aspect-video rounded-lg border border-white/10 bg-[#231849] overflow-hidden"
              >
                <div className=" w-[200px] ">
                  <Image
                    src={
                      getUpdatedCloudflareImageUrl(item.url, {
                        width: 200,
                        height: (item.height / item.width) * 200,
                        q: 50,
                      }) || '/placeholder.svg'
                    }
                    alt={item.url}
                    sizes="200"
                    fill
                    className=" object-contain "
                  />
                </div>
                <Button
                  variant="ghost"
                  type="button"
                  size="sm"
                  className=" lg:hidden absolute bg-white/30 top-2 right-2 h-6 w-6 text-white hover:bg-white/20"
                  onClick={() => removePreviousUpload(index)}
                >
                  <IoClose size={24} />
                </Button>
                <div className="absolute inset-0 bg-black/50 opacity-0 lg:group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    type="button"
                    size="sm"
                    className="absolute top-2 right-2 h-6 w-6 text-white hover:bg-white/20"
                    onClick={() => removePreviousUpload(index)}
                  >
                    <IoClose size={24} />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <DragAndDrop
        handleUpload={onUploadImageInputChange}
        setIsDragging={setIsDragging}
        acceptedTypes={['image/*']}
        className={` bg-blue-1/70 p-5 rounded-lg border-2 border-dashed border-white/20 ${isDragging && '!border-red-1/60 !bg-blue-1'} `}
      >
        <label
          className={`flex flex-col gap-2 items-center justify-center text-center cursor-pointer ${isInputDisabled && 'opacity-30'} `}
        >
          <div className=" p-4 rounded-full bg-blue-2/30 w-fit ">
            <LuImageUp size={24} />
          </div>
          <div>
            <TextV2 level={3}>{text}</TextV2>
            <TextV2 level={4} className=" text-gray-2 ">
              or click to upload
            </TextV2>
          </div>
          <input
            type="file"
            className=" sr-only "
            onChange={onUploadImageInputChange}
            disabled={isInputDisabled}
            accept="image/*"
            {...{ multiple: multipleFileUpload }}
          />
        </label>

        {(images ?? []).length > 0 && (
          <div className=" flex flex-col gap-2 items-center justify-center text-center mb-4 ">
            {images?.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center gap-4 p-3 rounded-lg bg-blue-1 border border-white/10 w-full"
              >
                {item.preview && (
                  <div className="relative h-12 w-12 rounded-md overflow-hidden">
                    <Image
                      src={item.preview || '/placeholder.svg'}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <div className="flex-1 text-left min-w-0">
                  <TextV2
                    level={4}
                    className="text-sm font-medium text-white truncate"
                  >
                    {item.name}
                  </TextV2>
                  <p className="text-xs text-zinc-400">
                    {Math.round(item.sizeInKb)} kb
                  </p>
                </div>

                <Button
                  variant="ghost"
                  type="button"
                  size="sm"
                  className="h-8 w-8 text-zinc-400 hover:text-white"
                  onClick={() => removeImage(index)}
                >
                  <IoClose size={24} />
                </Button>
              </div>
            ))}
          </div>
        )}
      </DragAndDrop>
    </div>
  );
};

export default FileInputBox;
