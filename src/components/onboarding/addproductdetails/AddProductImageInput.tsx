import React from 'react';
import TextV2 from '../../lib/typography/TextV2';
import HeadingV2 from '@/components/lib/typography/HeadingV2';
import FileInputBox from '../FileInputBox';

interface IAddProductImageInputProps {
  setProductImageFile: React.Dispatch<
    React.SetStateAction<
      | {
          preview: string;
          name: string;
          file: File;
          width: number;
          height: number;
          sizeInKb: number;
        }[]
      | undefined
    >
  >;
  tempProjectDetails: IBannerbotBusinessDetails;
  setTempProjectDetails: React.Dispatch<
    React.SetStateAction<IBannerbotBusinessDetails>
  >;
  previousUploads:
    | {
        url: string;
        width: number;
        height: number;
      }[]
    | undefined;
  isOnboardingCompleted: boolean;
  isLoading: boolean;
  isSavingProject: boolean;
  productImageFiles:
    | {
        preview: string;
        name: string;
        file: File;
        width: number;
        height: number;
        sizeInKb: number;
      }[]
    | undefined;
}

const AddProductImageInput = (props: IAddProductImageInputProps) => {
  const {
    setProductImageFile,
    tempProjectDetails,
    setTempProjectDetails,
    previousUploads,
    isOnboardingCompleted,
    isLoading,
    isSavingProject,
    productImageFiles,
  } = props;

  const handleProductImageDelete = (index: number) => {
    setProductImageFile((prev) => {
      const newPrev = [...(prev ?? [])];
      const updatedFiles = newPrev.filter((_, i) => i !== index);
      return updatedFiles;
    });
  };

  const removePreviousUpload = (index: number) => {
    const productImages = [...(tempProjectDetails.product_images ?? [])];
    productImages.splice(index, 1);
    setTempProjectDetails((prev) => ({
      ...prev,
      product_images: productImages,
    }));
  };

  return (
    <div className=" flex flex-col space-y-5 md:space-y-6 ">
      <div className=" flex items-baseline gap-2 ">
        <HeadingV2 level={3}>Add product image</HeadingV2>
        <TextV2 level={2}>(optional)</TextV2>
      </div>

      <FileInputBox
        text="Drop your image here"
        images={productImageFiles}
        previousUploads={previousUploads}
        addImage={(data) =>
          setProductImageFile((prev) => [...(prev ?? []), data])
        }
        removeImage={handleProductImageDelete}
        removePreviousUpload={removePreviousUpload}
        isInputDisabled={isLoading || isOnboardingCompleted || isSavingProject}
        multipleFileUpload
      />
    </div>
  );
};

export default AddProductImageInput;
