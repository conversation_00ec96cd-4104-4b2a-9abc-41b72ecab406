import Textarea from '@/components/lib/Textarea';
import HeadingV2 from '@/components/lib/typography/HeadingV2';
import React from 'react';

interface IProductDetailInputProps {
  tempProjectDetails: IBannerbotBusinessDetails;
  setTempProjectDetails: React.Dispatch<
    React.SetStateAction<IBannerbotBusinessDetails>
  >;
}

const ProductDetailInput = (props: IProductDetailInputProps) => {
  const { setTempProjectDetails, tempProjectDetails } = props;
  return (
    <div className=" flex flex-col ">
      <HeadingV2 level={3}>
        What product you want to promote via your ad?
      </HeadingV2>
      <Textarea
        rows={6}
        placeholder="Describe your product"
        className=" resize-none mt-5 md:mt-6 "
        required
        onChange={(e) =>
          setTempProjectDetails((prev) => ({
            ...prev,
            product_or_service_description: e.target.value,
          }))
        }
        value={tempProjectDetails.product_or_service_description}
      />
    </div>
  );
};

export default ProductDetailInput;
