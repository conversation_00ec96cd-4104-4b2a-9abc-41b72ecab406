import { getCommonHeaders } from '@/actions';
import {
  getBusinessDetailsFromWebsite,
  uploadFileAndGetS3Url,
} from '@/actions/bannerbotV2';
import { useUser } from '@/components/context/UserProvider';
import Button from '@/components/lib/Button';
import Input from '@/components/lib/Input';
import HeadingV2 from '@/components/lib/typography/HeadingV2';
import { showToastMessage } from '@/modules/toast';
import {
  converNormalUrlToCdnUrl,
  isValidURL,
  logApiErrorAndShowToastMessage,
} from '@/utils';
import React from 'react';
import { useMutation } from 'react-query';

interface IEnterProductUrlInputProps {
  tempProjectDetails: IBannerbotBusinessDetails;
  setTempProjectDetails: React.Dispatch<
    React.SetStateAction<IBannerbotBusinessDetails>
  >;
  setShowFullPageLoader: React.Dispatch<React.SetStateAction<boolean>>;
  errors: {
    url: string;
  };
  setErrors: React.Dispatch<
    React.SetStateAction<{
      url: string;
    }>
  >;
}

const EnterProductUrlInput = (props: IEnterProductUrlInputProps) => {
  const {
    setTempProjectDetails,
    tempProjectDetails,
    setShowFullPageLoader,
    errors,
    setErrors,
  } = props;
  const { user } = useUser();

  const getBusinessDetailsFromWebsiteMutation = useMutation(
    getBusinessDetailsFromWebsite,
  );

  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);

  const getBusinessDetail = (url: string) => {
    if (!isValidURL(url)) return;

    setShowFullPageLoader(true);

    getBusinessDetailsFromWebsiteMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        queryParams: { website: url },
      })
      .then(async (response) => {
        const data = response.data;
        const url = data.business_square_logo_url
          ? data.business_square_logo_url
          : data.business_logo_url;

        const mimeToExt: Record<string, string> = {
          'image/png': 'png',
          'image/jpeg': 'jpg',
          'image/webp': 'webp',
          'image/gif': 'gif',
        };

        let logoImageUrl: string | undefined;

        try {
          const imageRes = await fetch(url);
          const blob = await imageRes.blob();
          const extension = mimeToExt[blob.type];
          const file = new File(
            [blob],
            `${data.business_name.toLowerCase()}.${extension}`,
            { type: blob.type },
          );

          const mutationResponse = await uploadLogoImageMutation.mutateAsync({
            headers: getCommonHeaders(user),
            data: {
              file,
            },
          });
          logoImageUrl =
            converNormalUrlToCdnUrl(mutationResponse.data.s3_url, {
              width: 120,
              height: 120,
              q: 100,
              fit: 'pad',
            }) + '?v=1'; // added the query 'v' so that cdn doesn't give any problem (this will not fix the cors problem fully)
        } catch (e) {
          console.error(e);
          showToastMessage('error fetching the logo', 'error');
        }

        setTempProjectDetails((prev) => ({
          ...prev,
          product_or_service_description: data.business_description,
          business_name: data.business_name,
          ...(logoImageUrl // add the logo image only when it is avaiable
            ? {
                business_logo: {
                  square: {
                    url: logoImageUrl,
                    width: 120,
                    height: 120,
                  },
                },
              }
            : {}),
        }));
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'EnterProductUrlInput.getBusinessDetail',
        );
      })
      .finally(() => {
        setShowFullPageLoader(false);
      });
  };

  return (
    <div className=" flex flex-col  ">
      <HeadingV2 level={3}>Enter Product URL</HeadingV2>
      <div className=" flex flex-col mt-4 sm:mt-5 gap-2 ">
        <Input
          placeholder="http://"
          onChange={(e) => {
            if (!e.target.value || isValidURL(e.target.value))
              setErrors((prev) => ({ ...prev, url: '' }));
            else
              setErrors((prev) => ({
                ...prev,
                url: 'Please enter a valid URL or keep it empty',
              }));

            setTempProjectDetails((prev) => ({
              ...prev,
              website: e.target.value,
            }));
          }}
          // onBlur={(e) => getBusinessDetail(e.target.value)}
          value={tempProjectDetails.website}
        />
        <div>
          <Button
            variant={'outline'}
            size={'responsivePill'}
            type="button"
            disabled={!tempProjectDetails.website || errors.url !== ''}
            onClick={() => getBusinessDetail(tempProjectDetails.website ?? '')}
          >
            Autofill Product Details
          </Button>
        </div>
      </div>
      {errors.url && <p className=" text-red-400 text-sm ">*{errors.url}</p>}
    </div>
  );
};

export default EnterProductUrlInput;
