import { getFpVideoData } from '@/actions/fpVideo';
import React, { useCallback, useEffect, useState } from 'react';
import { useQuery } from 'react-query';
import { useUser } from '../context/UserProvider';
import { getCommonHeaders } from '@/actions';
import { useOnboarding } from '../context/OnboardingProvider';
import HeadingV2 from '../lib/typography/HeadingV2';
import DashboardCard from './DashboardCard';
import { HiOutlinePencilAlt } from 'react-icons/hi';
import TextV2 from '../lib/typography/TextV2';
import { FaBox, FaCheck, FaChevronDown } from 'react-icons/fa6';
import Button from '../lib/Button';
import EditScene from './editVideoTemplate/EditScene';
import FullScreenLoader from '../lib/FullScreenLoader';
import EditBrandDetails from './editVideoTemplate/EditBrandDetails';
import { IoImagesOutline } from 'react-icons/io5';
import EditBaseMedia from './editVideoTemplate/EditBaseAssets';
import EditBackgroundMusic from './editVideoTemplate/EditBackgroundMusic';
import { OnboardingStepIds } from '@/types/onBoarding';
import HorizontalLoader from '../lib/HorizontalLoader';
import RemotionPlayer from './RemotionPlayer';
import Dialog from '../lib/Dialog';
import { IoMdHome } from 'react-icons/io';
import { useRouter } from 'next/navigation';
import FetchError from '@/actions/FetchError';
import { IFpVideoData } from '@/remotion/types';
import { logApiErrorAndShowToastMessage } from '@/utils';

const EditVideoData = () => {
  const { user } = useUser();
  const {
    fpVideoData,
    projectDetails,
    saveFpVideoDetails,
    isSavingProject,
    stepId,
    previousStep,
  } = useOnboarding();
  const [showSection, setShowSection] = useState<
    'scene' | 'brand' | 'base-media' | 'background-music' | undefined
  >('scene');
  // const [isIFrameLoading, setIsIFrameLoading] = useState(true);
  const [videoTemplateData, setVideoTemplateData] =
    useState<IFpVideoData | null>(null);
  const [savedVideoTemplateData, setSavedVideoTemplateData] =
    useState<IFpVideoData | null>(null); // this is for iframefinalisGeneratingPopup, set
  const [showIsGeneratingPopup, setShowIsGeneratingPopup] = useState(false);

  const router = useRouter();

  const getFpVideoDataQuery = useQuery(
    ['getFpVideoData', fpVideoData?.templateId, projectDetails.id],
    () => {
      return getFpVideoData({
        headers: getCommonHeaders(user),
        queryParams: {
          template_id: fpVideoData?.templateId ?? '',
          project_id: projectDetails.id ?? '',
        },
      });
    },
    {
      cacheTime: 60_000, // Keep in cache for 1 minute
      staleTime: 0, // Mark data as stale immediately
      enabled: !!(projectDetails.id && fpVideoData?.templateId),
      refetchOnMount: true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'EditVideoData.getFpVideoDataQuery',
        );
        previousStep(stepId);
      },
    },
  );

  const { data, isLoading } = getFpVideoDataQuery;

  useEffect(() => {
    setVideoTemplateData((data?.data as IFpVideoData) ?? null);
    setSavedVideoTemplateData((data?.data as IFpVideoData) ?? null);
  }, [data]);

  const handleUpdateScenes = useCallback(
    (scenes: IFpVideoData['scenes']) => {
      setVideoTemplateData((prev) => {
        if (!prev) return prev;
        return { ...prev, scenes } as IFpVideoData;
      });
      setSavedVideoTemplateData(
        (prev) => ({ ...prev, scenes }) as IFpVideoData,
      );
    },
    [setVideoTemplateData],
  );

  const handleUpdateBrandDetails = useCallback(
    (brandDetails: IFpVideoData['branding']) => {
      setVideoTemplateData((prev) =>
        prev ? { ...prev, branding: brandDetails } : prev,
      );
      setSavedVideoTemplateData((prev) =>
        prev ? { ...prev, branding: brandDetails } : prev,
      );
    },
    [setVideoTemplateData],
  );

  const handleUpdateBaseAssets = useCallback(
    (baseAssets: IFpVideoData['base_assets']) => {
      setVideoTemplateData((prev) =>
        prev ? { ...prev, base_assets: baseAssets } : prev,
      );
      setSavedVideoTemplateData((prev) =>
        prev ? { ...prev, base_assets: baseAssets } : prev,
      );
    },
    [setVideoTemplateData],
  );

  const handleUpdateBackgroundMusic = useCallback(
    (backgroundMusic: IFpVideoData['base_audio']) => {
      setVideoTemplateData((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          base_audio: backgroundMusic,
        } as IFpVideoData;
      });
    },
    [setVideoTemplateData],
  );

  if (!fpVideoData?.templateId || !videoTemplateData || isLoading)
    return (
      <div className=" fixed inset-0 bg-blue-1 flex flex-col gap-2 items-center justify-center ">
        <div className=" w-[200px] ">
          <HorizontalLoader />
        </div>
        <TextV2 level={4} className=" text-gray-300 ">
          Generating video data
        </TextV2>
      </div>
    );

  return (
    <div className=" space-y-8 ">
      <HeadingV2 level={2}>{projectDetails.title}</HeadingV2>
      <DashboardCard>
        <div className=" flex max-md:flex-col gap-4 pb-[64px] md:pb-[48px] ">
          <div className=" relative flex-1 flex flex-col max-w-[600px] max-md:order-2 space-y-5 ">
            <div className=" space-y-5 ">
              {videoTemplateData.scenes && (
                <div className=" p-3 bg-blue-1 border-2 border-white/65 rounded-lg overflow-hidden ">
                  <div
                    className=" flex justify-between items-center cursor-pointer "
                    onClick={() =>
                      setShowSection((prev) =>
                        prev === 'scene' ? undefined : 'scene',
                      )
                    }
                  >
                    <div className=" flex items-center gap-3 ">
                      <HiOutlinePencilAlt size={24} />{' '}
                      <TextV2 level={3}>Elements</TextV2>
                    </div>
                    <div>
                      <FaChevronDown
                        size={20}
                        className={` ${showSection === 'scene' && '-rotate-180'} transition-all duration-300 `}
                      />
                    </div>
                  </div>

                  {videoTemplateData.scenes.length > 0 && (
                    <div
                      className={` max-h-0 ${showSection === 'scene' && '!max-h-[1000px] !duration-500 '} transition-all duration-150 `}
                    >
                      <EditScene
                        scenesData={videoTemplateData.scenes}
                        updateScenes={handleUpdateScenes}
                      />
                    </div>
                  )}
                </div>
              )}

              {videoTemplateData.branding && (
                <div className=" p-3 bg-blue-1 border-2 border-white/65 rounded-lg overflow-hidden  ">
                  <div
                    className=" flex justify-between items-center cursor-pointer "
                    onClick={() =>
                      setShowSection((prev) =>
                        prev === 'brand' ? undefined : 'brand',
                      )
                    }
                  >
                    <div className=" flex items-center gap-3 ">
                      <FaBox size={20} />{' '}
                      <TextV2 level={3}>Logo & Brand Name</TextV2>
                    </div>
                    <div>
                      <FaChevronDown
                        size={20}
                        className={` ${showSection === 'brand' && '-rotate-180'} transition-all duration-300 `}
                      />
                    </div>
                  </div>

                  {videoTemplateData.branding && (
                    <div
                      className={` max-h-0 ${showSection === 'brand' && '!max-h-[1000px] !duration-500 '} transition-all duration-150 `}
                    >
                      <EditBrandDetails
                        brandDetails={videoTemplateData.branding}
                        updateBrandDetails={handleUpdateBrandDetails}
                      />
                    </div>
                  )}
                </div>
              )}

              {videoTemplateData.base_assets && (
                <div className=" p-3 bg-blue-1 border-2 border-white/65 rounded-lg overflow-hidden ">
                  <div
                    className=" flex justify-between items-center cursor-pointer "
                    onClick={() =>
                      setShowSection((prev) =>
                        prev === 'base-media' ? undefined : 'base-media',
                      )
                    }
                  >
                    <div className=" flex items-center gap-3 ">
                      <IoImagesOutline size={24} />{' '}
                      <TextV2 level={3}>Base Assets</TextV2>
                    </div>
                    <div>
                      <FaChevronDown
                        size={20}
                        className={` ${showSection === 'base-media' && '-rotate-180'} transition-all duration-300 `}
                      />
                    </div>
                  </div>

                  <div
                    className={` max-h-0 ${showSection === 'base-media' && '!max-h-[1000px] !duration-500 '} transition-all duration-150 `}
                  >
                    <EditBaseMedia
                      baseAsset={videoTemplateData.base_assets}
                      updateBaseAssets={handleUpdateBaseAssets}
                    />
                  </div>
                </div>
              )}

              <div className=" p-3 bg-blue-1 border-2 border-white/65 rounded-lg overflow-hidden ">
                <div
                  className=" flex justify-between items-center cursor-pointer "
                  onClick={() =>
                    setShowSection((prev) =>
                      prev === 'background-music'
                        ? undefined
                        : 'background-music',
                    )
                  }
                >
                  <div className=" flex items-center gap-3 ">
                    <IoImagesOutline size={24} />{' '}
                    <TextV2 level={3}>Background Music</TextV2>
                  </div>
                  <div>
                    <FaChevronDown
                      size={20}
                      className={` ${showSection === 'background-music' && '-rotate-180'} transition-all duration-300 `}
                    />
                  </div>
                </div>

                <div
                  className={` max-h-0 ${showSection === 'background-music' && '!max-h-[1000px] !duration-500 '} transition-all duration-150 `}
                >
                  <EditBackgroundMusic
                    backgroundMusic={videoTemplateData.base_audio}
                    updateBackgroundMusic={handleUpdateBackgroundMusic}
                  />
                </div>
              </div>
            </div>
            <div className=" fixed bottom-0 left-0 w-full z-[1] bg-blue-1 ">
              <div className=" p-4 sm:p-5 md:px-12 max-w-[1440px] mx-auto ">
                <div className=" flex max-md:justify-center gap-4 overflow-auto ">
                  {/* <Button
                    className=" max-md:flex-1 md:min-w-40 "
                    variant={'secondary'}
                    size={'responsive'}
                    onClick={triggerSave}
                  >
                    Save
                  </Button> */}
                  <Button
                    className=" max-md:flex-1 md:min-w-40 "
                    size={'responsive'}
                    onClick={() => {
                      saveFpVideoDetails(
                        {
                          templateId: fpVideoData?.templateId,
                          templateData: videoTemplateData,
                        },
                        OnboardingStepIds.EDIT_VIDEO_DATA,
                      )
                        .then(() => setShowIsGeneratingPopup(true))
                        .catch((error) => {
                          if (error instanceof FetchError) {
                            const statusCode = error.statusCode;
                            if (statusCode === 403) {
                              // payment is required
                              router.push('/subscription');
                            }
                          }
                        });
                    }}
                  >
                    Continue
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className=" flex-1 shrink-0 flex items-start justify-center ">
            {savedVideoTemplateData ? (
              // <TemplateIFrame
              //   isIFrameLoading={isIFrameLoading}
              //   setIsIFrameLoading={setIsIFrameLoading}
              //   videoTemplateData={savedVideoTemplateData}
              // />
              <RemotionPlayer videoData={savedVideoTemplateData} />
            ) : (
              'not nice'
            )}
          </div>
        </div>
      </DashboardCard>
      {isSavingProject && <FullScreenLoader />}
      {showIsGeneratingPopup && (
        <Dialog
          isOpen={true}
          setIsOpen={() => {}}
          className=" max-h-[80vh] flex flex-col space-y-6 border-2 border-off-white/40 p-8 py-12 m-6 rounded-xl bg-blue-1 w-full max-w-[400px] overflow-auto "
        >
          <div className=" self-center w-fit p-5 rounded-full bg-gradient-to-r from-0% from-[#FC5185] to-100% to-[#673DE6] ">
            <FaCheck size={24} />
          </div>
          <p className=" text-center ">
            Your Video is getting generated <br /> This might take up to 2 mins.
          </p>
          <Button
            onClick={() => {
              document.body.style.overflow = 'auto';
              router.push('/dashboard');
            }}
          >
            <IoMdHome size={24} /> Go to Dashboard
          </Button>
        </Dialog>
      )}
    </div>
  );
};

export default EditVideoData;
