import React from 'react';

interface ILandingPageContainer {
  children?: React.ReactNode;
  rounded?: boolean;
  className?: string;
  innerClassName?: string;
}

const LandingPageContainer = (props: ILandingPageContainer) => {
  const { children, className, innerClassName } = props;
  return (
    // <div
    //   className={` relative pt-9 sm:pt-[66px] lg:pt-[100px] pb-[106px] sm:pb-[235px] lg:pb-[300px] px-6 sm:px-9 lg:px-14 ${
    //     rounded
    //       ? 'rounded-t-[70px] sm:rounded-t-[135px] lg:rounded-t-[200px] -mt-[70px] sm:-mt-[135px] lg:-mt-[200px]'
    //       : null
    //   } ${className}`}
    // >
    <div className={` ${className}`}>
      <section className={` max-w-[1280px] mx-auto ${innerClassName} `}>
        {children}
      </section>
    </div>
    // </div>
  );
};

export default LandingPageContainer;
