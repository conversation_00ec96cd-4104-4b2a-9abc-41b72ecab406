import { IUserSubscription } from '@/types/subscription';
import { useMutation, UseQueryResult } from 'react-query';
import { useUser } from './context/UserProvider';
import { useState } from 'react';
import { redeemCouponCode } from '@/actions/subscriptions';
import { getCommonHeaders } from '@/actions';
import { showToastMessage } from '@/modules/toast';
import { logApiErrorAndShowToastMessage } from '@/utils';
import Dialog from './lib/Dialog';
import TextV2 from './lib/typography/TextV2';
import Button from './lib/Button';
import Input from './lib/Input';
import FullScreenLoader from './lib/FullScreenLoader';
import { useRouter } from 'next/navigation';

const RedeemCouponDialog = (props: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  getUserSubscriptionQuery?: UseQueryResult<
    {
      data: IUserSubscription | undefined;
    },
    unknown
  >;
  handleSuccess?: () => void;
}) => {
  const { isOpen, setIsOpen, getUserSubscriptionQuery, handleSuccess } = props;
  const { user } = useUser();
  const [couponCode, setCouponCode] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const redeemCouponCodeMutation = useMutation(redeemCouponCode);

  const router = useRouter();

  const handleApplyCoupon = () => {
    redeemCouponCodeMutation
      .mutateAsync({
        data: { coupon_code: couponCode },
        headers: getCommonHeaders(user),
      })
      .then(() => {
        showToastMessage('Coupon Successfully Applied', 'success');
        getUserSubscriptionQuery?.refetch();
        setShowSuccessMessage(true);
        if (handleSuccess) handleSuccess();
      })
      .catch((error) => {
        logApiErrorAndShowToastMessage(error, 'profile.handleApplyCoupon');
      });
  };

  return (
    <>
      <Dialog
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        className=" max-h-[80vh] flex flex-col space-y-6 border-2 border-off-white/40 p-6 rounded-xl bg-blue-1 w-fit max-w-[700px] overflow-auto mx-4 "
      >
        {showSuccessMessage ? (
          <>
            <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <span className="text-2xl">🎉</span>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-center">
                Coupon Applied Successfully!
              </h3>
              <div className="text-gray-200 text-center space-y-2 mt-4 max-w-md">
                <p className="text-lg font-medium">
                  You&apos;ve unlocked a special deal — great choice!
                </p>
                <p className="text-gray-400">
                  Enjoy your exclusive discount and make the most of your
                  upgraded experience.
                </p>
              </div>
            </div>

            <div>
              <Button
                className=" capitalize w-full "
                size={'sm'}
                onClick={() => {
                  router.push('/dashboard');
                  setIsOpen(false);
                }}
              >
                Go to Dashboard
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className=" space-y-1 ">
              <TextV2 level={2}>Redeem Coupon Code</TextV2>
              <TextV2 level={4} className=" text-gray-400 ">
                Enter your coupon code below to apply discounts or unlock
                special offers.
              </TextV2>
            </div>
            <label className=" space-y-1">
              <TextV2 level={4} className=" font-medium ">
                Coupon Code
              </TextV2>

              <Input
                placeholder="Enter coupon code"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value)}
              />
            </label>
            <div className=" flex max-sm:flex-col gap-4 md:justify-end ">
              <div className=" max-md:flex-1 ">
                <Button
                  className=" capitalize max-md:w-full max-md:flex-1 "
                  variant={'outline'}
                  size={'sm'}
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </Button>
              </div>
              <div className=" max-md:flex-1 ">
                <Button
                  className=" capitalize max-md:w-full max-md:flex-1 "
                  size={'sm'}
                  onClick={() => {
                    handleApplyCoupon();
                  }}
                >
                  Apply Coupon
                </Button>
              </div>
            </div>
          </>
        )}
      </Dialog>

      {redeemCouponCodeMutation.isLoading && <FullScreenLoader />}
    </>
  );
};

export default RedeemCouponDialog;
