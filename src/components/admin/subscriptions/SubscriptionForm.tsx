'use client';

import { type ISubscriptionPlan } from '@/types/subscription';
import { useState } from 'react';
import { FaMinus, FaPlus } from 'react-icons/fa6';

interface SubscriptionFormProps {
  initialData?: ISubscriptionPlan;
  onSubmit: (subscription: ISubscriptionPlan) => void;
  isCreatingNew: boolean;
}

interface Feature {
  key: string;
  value: string;
}

export default function SubscriptionForm({
  initialData,
  onSubmit,
  isCreatingNew,
}: SubscriptionFormProps) {
  const initialFeatures = initialData
    ? Object.entries(initialData.features).map(([key, value]) => ({
        key,
        value: value,
      }))
    : [{ key: '', value: '' }];

  const [formData, setFormData] = useState<Partial<ISubscriptionPlan>>(
    initialData || {
      name: '',
      description: '',
      price_in_usd: 0,
      period: 'month',
      line_items: [],
      features: {},
      active: true,
    },
  );

  const [features, setFeatures] = useState<Feature[]>(initialFeatures);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const featuresObject = features.reduce(
      (acc, feature) => {
        if (feature.key.trim()) {
          acc[feature.key.trim()] = feature.value;
        }
        return acc;
      },
      {} as Record<string, string>,
    );

    onSubmit({
      ...formData,
      features: featuresObject,
      id: initialData?.id || String(Date.now()),
    } as ISubscriptionPlan);
  };

  const handleLineItemsChange = (value: string) => {
    setFormData({
      ...formData,
      line_items: value.split('\n').filter((item) => item.trim() !== ''),
    });
  };

  const addFeature = () => {
    setFeatures([...features, { key: '', value: '' }]);
  };

  const removeFeature = (index: number) => {
    if (features.length > 1) {
      setFeatures(features.filter((_, i) => i !== index));
    }
  };

  const updateFeature = (
    index: number,
    field: keyof Feature,
    value: string | number,
  ) => {
    const newFeatures = [...features];
    newFeatures[index] = {
      ...newFeatures[index],
      [field]: field === 'value' ? Number(value) : value,
    };
    setFeatures(newFeatures);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1 sm:mb-2">
            Plan Name
          </label>
          <input
            className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-60 "
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            disabled={!isCreatingNew}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1 sm:mb-2">
            Description
          </label>
          <textarea
            className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-60 "
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            required
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1 sm:mb-2">
              Price (USD)
            </label>
            <input
              type="number"
              className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-60"
              value={formData.price_in_usd}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  price_in_usd: Number(e.target.value),
                })
              }
              disabled={!isCreatingNew}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 sm:mb-2">
              Billing Period
            </label>
            <select
              className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-60"
              value={formData.period}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  period: e.target.value as 'month' | 'year',
                })
              }
              disabled={!isCreatingNew}
            >
              <option value="month">Monthly</option>
              <option value="year">Yearly</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 sm:mb-2">
            Features (one per line)
          </label>
          <textarea
            className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
            value={formData.line_items?.join('\n')}
            onChange={(e) => handleLineItemsChange(e.target.value)}
            rows={4}
          />
        </div>

        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium">Limits</label>
            <button
              type="button"
              onClick={addFeature}
              className="text-blue-500 hover:text-blue-400 transition-colors p-1 bg-gray-600 rounded-lg cursor-pointer"
            >
              <FaPlus className="h-5 w-5" />
            </button>
          </div>
          <div className="space-y-3">
            {features.map((feature, index) => (
              <div key={index} className="flex gap-2 items-start">
                <div className="flex-3/4 ">
                  <input
                    placeholder="Feature name"
                    className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                    value={feature.key}
                    onChange={(e) =>
                      updateFeature(index, 'key', e.target.value)
                    }
                    required
                  />
                </div>
                <div className="flex-1/4 ">
                  <input
                    placeholder="Value"
                    className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                    value={feature.value}
                    onChange={(e) =>
                      updateFeature(index, 'value', e.target.value)
                    }
                    required
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeFeature(index)}
                  className="text-red-500 hover:text-red-400 transition-colors self-stretch p-1 bg-gray-600 rounded-lg cursor-pointer disabled:opacity-60"
                  disabled={features.length === 1}
                >
                  <FaMinus className="h-5 w-5" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {!isCreatingNew && (
          <div>
            <label className="block text-sm font-medium mb-1 sm:mb-2">
              Stripe Product ID
            </label>
            <input
              className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-60"
              value={formData.stripe_product_id}
              disabled
              required
            />
          </div>
        )}

        {!isCreatingNew && (
          <div>
            <label className="block text-sm font-medium mb-1 sm:mb-2">
              Stripe Price ID
            </label>
            <input
              className="w-full px-3 sm:px-4 py-2 bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base disabled:opacity-60"
              value={formData.stripe_price_id}
              disabled
              required
            />
          </div>
        )}
      </div>

      <button
        type="submit"
        className="w-full py-2 sm:py-3 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors duration-300 text-sm sm:text-base font-medium"
      >
        {initialData ? 'Update Plan' : 'Create Plan'}
      </button>
    </form>
  );
}
