'use client';

import { useState } from 'react';
import { FiEdit2 } from 'react-icons/fi';
import { FaCheck, FaX } from 'react-icons/fa6';
import { type ISubscriptionPlan } from '@/types/subscription';
import SubscriptionForm from './SubscriptionForm';
import Dialog from '@/components/lib/Dialog';

interface SubscriptionCardProps {
  subscription: ISubscriptionPlan;
  onUpdate: (subscription: ISubscriptionPlan) => void;
}

export default function SubscriptionCard({
  subscription,
  onUpdate,
}: SubscriptionCardProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleStatusToggle = () => {
    onUpdate({
      ...subscription,
      active: !subscription.active,
    });
  };

  return (
    <>
      <div
        className={`bg-blue-950 rounded-2xl p-4 sm:p-6 transform transition-all duration-300 hover:scale-105 ${!subscription.active && 'opacity-80'}`}
      >
        <div className="flex justify-between items-start mb-4 sm:mb-6">
          <h3 className="text-xl sm:text-2xl font-bold">{subscription.name}</h3>
          <div className="flex items-center gap-2 sm:gap-3">
            <button
              onClick={handleStatusToggle}
              className={`w-10 sm:w-12 h-5 sm:h-6 rounded-full relative transition-colors duration-300 ${
                subscription.active ? 'bg-green-500' : 'bg-gray-600'
              }`}
            >
              <span
                className={`absolute w-4 sm:w-5 h-4 sm:h-5 bg-white rounded-full top-0.5 transition-transform duration-300 ${
                  subscription.active ? 'left-6 sm:left-7' : 'left-0.5'
                }`}
              />
            </button>
            <button
              onClick={() => setIsDialogOpen(true)}
              className="text-gray-400 hover:text-white transition-colors p-1"
            >
              <FiEdit2 className="h-4 sm:h-5 w-4 sm:w-5" />
            </button>
          </div>
        </div>
        <p className=" text-gray-400 ">{subscription.description}</p>

        <div className="space-y-4 sm:space-y-6 mt-4 ">
          <div className="flex items-baseline gap-1 sm:gap-2">
            <span className="text-3xl sm:text-4xl font-bold">
              ${subscription.price_in_usd}
            </span>
            <span className="text-sm sm:text-base text-gray-400">
              /{subscription.period}
            </span>
          </div>

          <div>
            <h4 className="text-base sm:text-lg font-semibold mb-2 sm:mb-3">
              Features
            </h4>
            <ul className="space-y-1.5 sm:space-y-2">
              {subscription.line_items.map((item, index) => (
                <li
                  key={index}
                  className="flex items-center gap-2 text-sm sm:text-base text-gray-300"
                >
                  <FaCheck className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-base sm:text-lg font-semibold mb-2">Limits</h4>
            <div className="text-sm sm:text-base text-gray-300">
              Max videos: {subscription.features.max_templatized_videos_per_day}
              /month
            </div>
          </div>

          <div className="pt-4 border-t border-gray-700">
            <div className="text-xs text-gray-500">
              Stripe Product ID: {subscription.stripe_product_id}
            </div>
            <div className="text-xs text-gray-500">
              Stripe Price ID: {subscription.stripe_price_id}
            </div>
          </div>
        </div>
      </div>

      {isDialogOpen && (
        <Dialog isOpen={isDialogOpen} setIsOpen={setIsDialogOpen}>
          <div className="bg-gray-800 rounded-2xl p-6 sm:p-8 w-full max-w-2xl relative max-h-[90vh] overflow-y-auto">
            <button
              onClick={() => setIsDialogOpen(false)}
              className="absolute right-4 top-4 text-gray-400 hover:text-white"
            >
              <FaX className="h-6 w-6" />
            </button>
            <h2 className="text-xl sm:text-2xl font-bold mb-6">
              Edit Subscription Plan
            </h2>
            <SubscriptionForm
              initialData={subscription}
              onSubmit={(updatedSubscription) => {
                onUpdate(updatedSubscription);
                setIsDialogOpen(false);
              }}
              isCreatingNew={false}
            />
          </div>
        </Dialog>
      )}
    </>
  );
}
