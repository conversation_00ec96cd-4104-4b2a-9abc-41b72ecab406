import { converNormalUrlToCdnUrl } from '@/utils';
import React, { useMemo } from 'react';
import {
  FaArrowLeft,
  FaArrowRight,
  FaRegClock,
  FaRegFileImage,
  FaRegFileVideo,
} from 'react-icons/fa6';
import Text from '../lib/typography/Text';
import Image from 'next/image';
import { BannerbotAdType } from '@/types/bannerbotProject';
import Link from 'next/link';
import TextV2 from '../lib/typography/TextV2';
import { IoSparklesOutline } from 'react-icons/io5';

interface IProjectCardProps {
  userDetail: {
    uid: string;
    name: string;
    email: string;
  };
  project: IBannerbotProject;
  handleBack: () => void;
  redirectUrl: string;
}

const ProjectCard = ({
  userDetail,
  project,
  handleBack,
  redirectUrl,
}: IProjectCardProps) => {
  const showImages = useMemo(() => {
    const modifiedUrlImages = (
      project?.details?.saved_assets?.[BannerbotAdType.SINGLE_IMAGE] ?? []
    )
      .slice(0, 4)
      .map(({ url, width, height }) => {
        const newHeight = (height * 250) / width;
        return {
          url: converNormalUrlToCdnUrl(url, {
            width: 250,
            height: newHeight,
            q: 30,
          }),
          height: newHeight,
          width: 250,
        };
      });

    return modifiedUrlImages;
  }, [project?.details?.saved_assets]);

  const showAiBanners = useMemo(() => {
    const modifiedUrlImages = (
      project?.details?.saved_assets?.[BannerbotAdType.AI_BANNER] ?? []
    )
      .slice(0, 4)
      .map(({ url, width, height }) => {
        const newHeight = (height * 250) / width;
        return {
          url: converNormalUrlToCdnUrl(url, {
            width: 250,
            height: newHeight,
            q: 30,
          }),
          height: newHeight,
          width: 250,
        };
      });

    return modifiedUrlImages;
  }, [project?.details?.saved_assets]);

  const showVideoThumbnails = useMemo(() => {
    const modifiedUrlImages = (
      project?.details?.saved_assets?.[BannerbotAdType.VIDEO]?.reverse() ?? []
    )
      .slice(0, 4)
      .map(({ width, height, thumbnail_url }) => {
        const newHeight = (height * 250) / width;
        return {
          url: thumbnail_url
            ? converNormalUrlToCdnUrl(thumbnail_url, {
                width: 250,
                height: newHeight,
                q: 30,
              })
            : '',
          height: newHeight,
          width: 250,
        };
      });

    return modifiedUrlImages;
  }, [project?.details?.saved_assets]);

  return (
    <div className=" p-6 shadow rounded-xl hover:shadow-md transition-all duration-150 bg-blue-1 text-gray-100 ">
      <div
        className=" text-gray-400 w-fit flex items-center gap-2 cursor-pointer "
        onClick={handleBack}
      >
        <FaArrowLeft size={14} />
        <Text level={3}>Back to projects</Text>
      </div>
      <div className=" flex items-center justify-between mt-4 ">
        <Link href={redirectUrl}>
          <div className=" group flex gap-2 items-center cursor-pointer relative after:absolute after:w-full after:h-0.5 after:z-10 after:bg-blue-1 after:-left-full after:bottom-0 hover:after:left-0 after:transition-all after:duration-300 overflow-hidden ">
            <Text level={1} className=" font-medium line-clamp-1 ">
              {project.title ?? '[[NO_TITLE]]'}
            </Text>
            <FaArrowRight
              size={16}
              className=" -rotate-45 group-hover:rotate-0 transition-all duration-300 "
            />
          </div>
        </Link>
        <Text
          level={3}
          className=" text-gray-400 flex gap-2 items-center flex-shrink-0 "
        >
          <FaRegClock size={16} />{' '}
          {new Date(project.updated_at._seconds * 1000).toLocaleDateString()}
        </Text>
      </div>
      <Text level={2} className=" mt-4 text-gray-400 ">
        {project.details?.business_details?.key_benefits ??
          '[[NO_DESCRIPTION]]'}
      </Text>
      {showImages.length > 0 && (
        <h2 className=" mt-6 mb-2 text-gray-medium ">Static Banners</h2>
      )}
      <div className=" flex flex-wrap items-center justify-center gap-3 ">
        {showImages.map(({ url, height, width }, index) => (
          <Image
            src={url}
            height={height}
            width={width}
            alt="Banner Image"
            className=" rounded-xl border-2 inset-0 border-gray-600 "
            key={`static-${index}`}
          />
        ))}
      </div>
      {showAiBanners.length > 0 && (
        <h2 className=" mt-4 mb-2 text-gray-medium ">AI Banners</h2>
      )}
      <div className=" flex flex-wrap items-center justify-center gap-3 ">
        {showAiBanners.map(({ url, height, width }, index) => (
          <Image
            src={url}
            height={height}
            width={width}
            alt="Banner Image"
            className=" rounded-xl border-2 inset-0 border-gray-600 "
            key={`static-${index}`}
          />
        ))}
      </div>
      {showVideoThumbnails.length > 0 && (
        <h2 className=" mt-4 mb-2 text-gray-medium ">Video Banners</h2>
      )}
      <div className=" flex flex-wrap items-center justify-center gap-3 ">
        {showVideoThumbnails.map(({ url, height, width }, index) => (
          <>
            {url ? (
              <Image
                src={url}
                height={height}
                width={width}
                alt="Banner Image"
                className=" rounded-xl border-2 inset-0 border-gray-600 "
                key={`thumbnail-${index}`}
              />
            ) : (
              <div
                className=" bg-gray-500 flex flex-col items-center justify-center text-center p-3 select-none gap-2 "
                style={{
                  height,
                  width,
                }}
                key={`thumbnail-${index}`}
              >
                <TextV2 level={4} className=" text-gray-50 ">
                  The video is being generated
                </TextV2>
                <p className=" !text-sm text-gray-200 ">
                  This might take around 2 minutes
                </p>
              </div>
            )}
          </>
        ))}
      </div>
      <hr className=" border-gray-300 my-6 " />
      <div className=" text-gray-400 flex justify-between items-center ">
        <div className=" flex gap-4 ">
          <div className=" flex items-center gap-2 ">
            <FaRegFileImage size={16} />
            <Text level={3}>
              {' '}
              {project.details?.saved_assets?.[BannerbotAdType.SINGLE_IMAGE]
                ?.length ?? 0}{' '}
              static{' '}
            </Text>
          </div>
          <div className=" flex items-center gap-2 ">
            <IoSparklesOutline size={16} />
            <Text level={3}>
              {' '}
              {project.details?.saved_assets?.[BannerbotAdType.AI_BANNER]
                ?.length ?? 0}{' '}
              AI{' '}
            </Text>
          </div>
          <div className=" flex items-center gap-2 ">
            <FaRegFileVideo size={16} />
            <Text level={3}>
              {' '}
              {project.details?.saved_assets?.[BannerbotAdType.VIDEO]?.length ??
                0}{' '}
              video{' '}
            </Text>
          </div>
        </div>
        <Text level={3}>{userDetail.email}</Text>
      </div>
    </div>
  );
};

export default ProjectCard;
