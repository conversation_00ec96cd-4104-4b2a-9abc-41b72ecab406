import React from 'react';
import Text from '../lib/typography/Text';
import { FaRegClock, FaRegFileImage, FaRegFileVideo } from 'react-icons/fa6';
import { BannerbotAdType } from '@/types/bannerbotProject';
import { IoSparklesOutline } from 'react-icons/io5';

interface IProjectListCardProps {
  userDetail: {
    uid: string;
    name: string;
    email: string;
  };
  project: IBannerbotProject;
  handleClick: () => void;
}

const ProjectListCard = ({
  userDetail,
  project,
  handleClick,
}: IProjectListCardProps) => {
  return (
    <div
      className=" p-6 shadow rounded-xl hover:shadow-md transition-all duration-150 cursor-pointer bg-blue-1 text-gray-100 "
      onClick={handleClick}
    >
      <div className=" flex items-center justify-between ">
        <Text level={1} className=" font-medium line-clamp-1 ">
          {project.title ?? '[[NO_TITLE]]'}
        </Text>
        <Text
          level={3}
          className=" text-gray-400 flex gap-2 items-center flex-shrink-0 "
        >
          <FaRegClock size={16} />{' '}
          {new Date(project.updated_at._seconds * 1000).toLocaleDateString()}
        </Text>
      </div>
      <Text level={2} className=" mt-2 text-gray-400 line-clamp-4 ">
        {project.details?.business_details?.key_benefits ??
          '[[NO_DESCRIPTION]]'}
      </Text>
      <div className=" text-gray-400 flex justify-between flex-wrap items-center mt-2 ">
        <div className=" flex gap-4 ">
          <div className=" flex items-center gap-2 ">
            <FaRegFileImage size={16} />
            <Text level={3}>
              {' '}
              {project.details?.saved_assets?.[BannerbotAdType.SINGLE_IMAGE]
                ?.length ?? 0}{' '}
              static{' '}
            </Text>
          </div>
          <div className=" flex items-center gap-2 ">
            <IoSparklesOutline size={16} />
            <Text level={3}>
              {' '}
              {project.details?.saved_assets?.[BannerbotAdType.AI_BANNER]
                ?.length ?? 0}{' '}
              AI{' '}
            </Text>
          </div>
          <div className=" flex items-center gap-2 ">
            <FaRegFileVideo size={16} />
            <Text level={3}>
              {' '}
              {project.details?.saved_assets?.[BannerbotAdType.VIDEO]?.length ??
                0}{' '}
              video{' '}
            </Text>
          </div>
        </div>
        <Text level={3}>{userDetail.email}</Text>
      </div>
    </div>
  );
};

export default ProjectListCard;
