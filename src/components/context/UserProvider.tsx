'use client';

import { getCommonHeaders } from '@/actions';
import { getUserSubscription } from '@/actions/subscriptions';
import { IUserSubscription } from '@/types/subscription';
import { User } from 'firebase/auth';
import { usePathname, useRouter } from 'next/navigation';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { createContext } from 'react';
import { useQuery } from 'react-query';

interface IUserContextType {
  user: IBannerbotUser | undefined;
  setUser: React.Dispatch<React.SetStateAction<IBannerbotUser | undefined>>;
  handleSignOut: () => void;
  isUserSubscriptionLoading: boolean;
  userSubscription:
    | {
        isActive: boolean;
        data: IUserSubscription;
      }
    | undefined;
}

const protectedRoutes = [
  '/dashboard',
  '/project-details',
  '/onboarding',
  '/profile',
  '/admin',
  '/admin/ai-banners',
  '/admin/subscription',
  '/subscription/success',
  '/subscription/cancel',
  '/subscription',
  '/redeem',
];

const UserContext = createContext<IUserContextType | null>(null);

const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<IBannerbotUser>();
  const pathname = usePathname();
  const router = useRouter();
  const [isInitialSetupDone, setIsInitialSetupDone] = useState(
    !protectedRoutes.includes(pathname),
  );

  const getUserSubscriptionQuery = useQuery(
    ['getUserSubscription'],
    () => {
      return getUserSubscription({
        headers: getCommonHeaders(user),
      });
    },
    {
      enabled: !!user,
      refetchOnWindowFocus: false,
    },
  );

  async function handleSignOut() {
    if (window.bridge) {
      window.bridge.postMessage('logout');
      setUser(undefined);
      return;
    }

    const { getFirebaseAuth } = await import('@/utils/firebase');

    const auth = getFirebaseAuth();

    if (auth) {
      auth.signOut();
      setUser(undefined);
      // router.push('/login');
    }
  }

  const setUserToSentry = useCallback(
    (userProps = user) => {
      if (userProps?.uid && window?.sentry) {
        window.sentry.setUserToSentry({
          email: userProps.email ?? '',
          id: userProps.uid,
        });
      }
    },
    [user],
  );

  // const handlePath = useCallback(
  //   (userProps?: IBannerbotUser) => {
  //     if (!userProps && protectedRoutes.includes(pathname)) {
  //       router.push('/login');
  //     }

  //     if (userProps && pathname == '/') router.push('/dashboard');

  //     setIsInitialSetupDone(true);
  //   },
  //   [pathname, router],
  // );

  const getTokenAndSetUser = useCallback((user: User) => {
    user
      .getIdTokenResult(true)
      .then((idTokenResult) => {
        const profile = idTokenResult.claims?.profile as {
          email: string;
          name: string;
        };
        const userProps: IBannerbotUser = {
          displayName: user.displayName ?? profile?.name,
          email: user.email ?? profile?.email,
          photoUrl: user.photoURL ?? '',
          uid: user.uid,
          authToken: idTokenResult.token,
          mobile: user.phoneNumber ?? '',
        };
        setUser(userProps);

        // schedule auto refresh
        const expirationTime = idTokenResult.expirationTime;

        // Calculate the time to set the next refresh
        const currentTime = Date.now();
        const expirationTimeMillis = new Date(expirationTime).getTime();
        const timeUntilRefresh =
          expirationTimeMillis - currentTime - 5 * 60 * 1000; // 5 minutes before expiry
        //console.log(`Token will be refreshed in ${timeUntilRefresh / 1000} secs`);

        // Set the timeout to refresh the token
        setTimeout(() => {
          getTokenAndSetUser(user);
        }, timeUntilRefresh);
      })
      .catch((error) => {
        throw new Error('Error in initializing firebase', error);
      })
      .finally(() => {
        setIsInitialSetupDone(true);
      });
  }, []);

  const userSubscription = useMemo(() => {
    if (getUserSubscriptionQuery.data?.data) {
      return {
        isActive: getUserSubscriptionQuery.data.data.status === 'active',
        data: getUserSubscriptionQuery.data.data,
      };
    }
  }, [getUserSubscriptionQuery.data]);

  useEffect(() => {
    if (isInitialSetupDone) {
      if (!user && protectedRoutes.includes(pathname)) {
        router.push('/login');
        return;
      }

      if (user && pathname == '/') router.push('/dashboard');
    }
  }, [pathname, router, user, isInitialSetupDone]);

  useEffect(() => {
    const initFirebase = async () => {
      const { initializeFirebase, getFirebaseAuth } = await import(
        '@/utils/firebase'
      );
      initializeFirebase();
      const auth = getFirebaseAuth();

      if (auth) {
        auth.onAuthStateChanged(function (user) {
          if (user) {
            getTokenAndSetUser(user);
          } else {
            setIsInitialSetupDone(true);
          }
        });
      } else {
        setIsInitialSetupDone(true);
      }
    };

    if ([...protectedRoutes, '/'].includes(pathname) && !user)
      void initFirebase();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // register the service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('./service-worker.js')
        .then((registration) => {
          console.info('service worker registered:', registration);
        })
        .catch((error) => {
          console.info('service worker registration failed:', error);
        });
    }

    import('../../modules/sentry')
      .then((sentry) => {
        window.sentry = sentry.default;
        window.sentry.initializeSentry();
      })
      .catch((error) => console.error(error));
  }, []);

  useEffect(() => {
    window.platform = 'web'; // will be overridden in flutter app

    // flutter webview will call these methods
    window.onInitViaBridge = (user?: IBannerbotUser) => {
      if (user && user.authToken && user.email && user.uid) {
        setUser(user);
        setUserToSentry(user);
      }
      setIsInitialSetupDone(true);
    };
  }, [setUserToSentry]);

  const shouldRenderChildren = useMemo(
    () =>
      isInitialSetupDone &&
      (user ? pathname !== '/' : !protectedRoutes.includes(pathname)),
    [isInitialSetupDone, pathname, user],
  );

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        handleSignOut,
        isUserSubscriptionLoading: getUserSubscriptionQuery.isLoading,
        userSubscription,
      }}
    >
      {!isInitialSetupDone ? (
        <div className="min-h-screen bg-blue-1 text-white flex justify-center items-center">
          <p className="text-2xl">loading...</p>
        </div>
      ) : shouldRenderChildren ? (
        children
      ) : (
        <div className="min-h-screen bg-blue-1 text-white flex justify-center items-center">
          <p className="text-2xl">Redirecting...</p>
        </div>
      )}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === null) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserProvider;
