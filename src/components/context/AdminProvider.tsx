import { createContext, useContext, useState } from 'react';

interface IAdminContext {
  isSidebarOpen: boolean;
  setIsSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const AdminContext = createContext<IAdminContext | null>(null);

const AdminProvider = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <AdminContext.Provider value={{ isSidebarOpen, setIsSidebarOpen }}>
      {children}
    </AdminContext.Provider>
  );
};

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (context === null) {
    throw new Error('useAdmin must be used within a AdminProvider');
  }
  return context;
};

export default AdminProvider;
