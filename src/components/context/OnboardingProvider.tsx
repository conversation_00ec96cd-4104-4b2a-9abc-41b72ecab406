'use client';

import { getCommonHeaders } from '@/actions';
import { getProject, updateProject } from '@/actions/bannerbotV2';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useMutation, useQuery } from 'react-query';
import { useUser } from './UserProvider';
import {
  BannerbotAdType,
  BannerbotProjectStatus,
} from '@/types/bannerbotProject';
import { OnboardingStepIds } from '@/types/onBoarding';
import { useRouter, useSearchParams } from 'next/navigation';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { showToastMessage } from '@/modules/toast';
import { generateFpVideo } from '@/actions/fpVideo';
import HorizontalLoader from '../lib/HorizontalLoader';
import TextV2 from '../lib/typography/TextV2';
import { IFpVideoData } from '@/remotion/types';

interface IOnboardingContext {
  stepId: OnboardingStepIds;
  projectDetails: Partial<IBannerbotProject>;
  saveProjectDetails: (
    data: Partial<IBannerbotProject> & {
      details?: Partial<IBannerbotProject['details']> & {
        business_details?: Partial<IBannerbotBusinessDetails>;
        ad_details?: IBannerbotAdDetails;
      };
    },
    currentStepId: OnboardingStepIds,
  ) => Promise<void>;
  isSavingProject: boolean;
  isOnboardingCompleted: boolean;
  previousStep: (currentStepId: OnboardingStepIds) => void;
  isInitialLoading: boolean;
  saveFpVideoDetails: (
    fpData: {
      templateId: IFpVideoData['template_id'];
      templateData?: IFpVideoData;
    },
    currentStepId: OnboardingStepIds,
  ) => Promise<void>;
  fpVideoData:
    | {
        templateId: IFpVideoData['template_id'];
        data?: IFpVideoData;
      }
    | undefined;
  createNewVideoBanner: () => void;
  updateProjectDetails: (projectDetails: IBannerbotProject) => void;
}

export const OnboardingContext = createContext<IOnboardingContext | null>(null);

const OnboardingProvider = ({ children }: { children: React.ReactNode }) => {
  const { user } = useUser();
  const searchParams = useSearchParams();
  const router = useRouter();

  const projectId = searchParams.get('id');

  const [stepId, setStepId] = useState(OnboardingStepIds.ADD_PRODUCT_DETAILS);
  const [isSavingProject, setIsSavingProject] = useState(false);

  const [projectDetails, setProjectDetails] = useState<
    Partial<IBannerbotProject>
  >({
    id: '',
    title: '',
    status: BannerbotProjectStatus.DRAFT,
    type: 'AI_ONLY',
    uid: '',
    details: {
      business_details: {
        product_or_service_description: '',
        product_images: [],
        website: '',
        mobile: '',
        business_name: '',
        key_benefits: [],
      },
      ad_details: {
        types: [],
      },
      // ai_key_benefits: [],
    },
  });

  const [fpVideoData, setFpVideoData] = useState<{
    templateId: IFpVideoData['template_id'];
    templateData?: IFpVideoData;
  }>();

  const [isOnboardingCompleted, setIsOnboardingCompleted] = useState(
    projectDetails.status === BannerbotProjectStatus.COMPLETE,
  );

  useEffect(() => {
    setIsOnboardingCompleted(
      projectDetails.status === BannerbotProjectStatus.COMPLETE,
    );
  }, [projectDetails]);

  // const queryParams = useMemo(() => {
  //   const queries: Record<string, string> = {};
  //   searchParams.entries().forEach((item) => (queries[item[0]] = item[1]));
  //   return queries;
  // }, [searchParams]);

  // useEffect(() => {
  //   if (!searchParams.has('id')) {
  //     showToastMessage('Please create a project first', 'error');
  //     router.push('/dashboard');
  //   }
  // }, [searchParams]);

  const getProjectQuery = useQuery(
    ['getProject', projectId],
    () => {
      if (!projectId) return;
      return getProject({
        headers: getCommonHeaders(user),
        queryParams: { id: projectId },
      });
    },
    {
      enabled: !!projectId,
      onSuccess: (response) => {
        if (!response) return;
        if (response.data.length === 0) {
          showToastMessage('Something went wrong', 'error');
          router.push('/dashboard');
          return;
        }

        const project = response.data[0];

        setProjectDetails((prev) => ({
          ...prev,
          ...project,
          details: {
            ...prev.details,
            ...project.details,
            business_details: {
              ...prev.details?.business_details,
              ...project.details?.business_details,
            },
          },
        }));
      },
    },
  );

  const { isLoading: isInitialLoading } = getProjectQuery;

  const updateProjectMutation = useMutation(updateProject);
  const generateFpVideoMutation = useMutation(generateFpVideo);

  const resetStates = () => {
    setStepId(OnboardingStepIds.ADD_PRODUCT_DETAILS);
    setProjectDetails({
      id: '',
      title: '',
      status: BannerbotProjectStatus.DRAFT,
      type: 'AI_ONLY',
      uid: '',
      details: {
        business_details: {
          product_or_service_description: '',
          product_images: [],
          website: '',
          mobile: '',
          business_name: '',
          key_benefits: [],
        },
        ad_details: {
          types: [],
        },
        // ai_key_benefits: [],
      },
    });
  };

  const moveToNextStep = useCallback(
    (
      currentStepId: OnboardingStepIds,
      adtype?: { selectedAdType: BannerbotAdType | undefined },
    ) => {
      switch (currentStepId) {
        // case OnboardingStepIds.SELECT_PROJECT_TYPE:
        //   setStepId(OnboardingStepIds.ADD_PRODUCT_DETAILS);
        //   break;
        case OnboardingStepIds.ADD_PRODUCT_DETAILS:
          setStepId(OnboardingStepIds.ENTER_BRAND_DETAILS);
          break;
        case OnboardingStepIds.ENTER_BRAND_DETAILS:
          setStepId(OnboardingStepIds.SELECT_KEY_BENEFITS);
          break;
        case OnboardingStepIds.SELECT_KEY_BENEFITS:
          setStepId(OnboardingStepIds.SELECT_TYPE_OF_AD);
          break;
        case OnboardingStepIds.SELECT_TYPE_OF_AD:
          if (
            adtype?.selectedAdType &&
            adtype.selectedAdType === BannerbotAdType.VIDEO
          ) {
            setStepId(OnboardingStepIds.CHOOSE_VIDEO_TEMPLATE);
          } else {
            router.push(`/project-details?id=${projectDetails.id}`);
          }
          break;
        case OnboardingStepIds.CHOOSE_VIDEO_TEMPLATE:
          setStepId(OnboardingStepIds.EDIT_VIDEO_DATA);
          break;
        case OnboardingStepIds.EDIT_VIDEO_DATA:
          break;
        case OnboardingStepIds.PROJECT_BANNERS:
          break;
      }
    },
    [projectDetails.id, router],
  );

  const goBackPreviousStep = useCallback((currentStepId: OnboardingStepIds) => {
    switch (currentStepId) {
      // case OnboardingStepIds.ADD_PRODUCT_DETAILS:
      //   setStepId(OnboardingStepIds.SELECT_PROJECT_TYPE);
      //   break;
      case OnboardingStepIds.ENTER_BRAND_DETAILS:
        setStepId(OnboardingStepIds.ADD_PRODUCT_DETAILS);
        break;
      case OnboardingStepIds.SELECT_KEY_BENEFITS:
        setStepId(OnboardingStepIds.ENTER_BRAND_DETAILS);
        break;
      case OnboardingStepIds.SELECT_TYPE_OF_AD:
        setStepId(OnboardingStepIds.SELECT_KEY_BENEFITS);
        break;
      case OnboardingStepIds.EDIT_VIDEO_DATA:
        setStepId(OnboardingStepIds.CHOOSE_VIDEO_TEMPLATE);
    }
  }, []);

  /**
   * Saves the project details during the onboarding process.
   *
   * This function handles updating the project details by making an API call
   * and updating the local state with the response. It also manages navigation
   * between onboarding steps based on the current step and project status.
   *
   * @param data - Partial project data to be saved. Includes optional nested details
   * such as `business_details` and `ad_details`.
   * @param currentStepId - The ID of the current onboarding step.
   *
   * @returns A promise that resolves when the operation is complete.
   *
   * @throws Will log an error and show a toast message if the API call fails.
   *
   * @remarks
   * - If the project status is not `COMPLETE` or the current step is `PROJECT_BANNERS`,
   *   the function updates the project via an API call and updates the local state.
   * - If the current step is `ADD_PRODUCT_DETAILS`, the user is redirected to the
   *   onboarding page with the project ID.
   * - For other steps, the function moves to the next step, optionally passing
   *   additional data (e.g., selected ad type).
   * - If the project status is `COMPLETE` and the current step is not `PROJECT_BANNERS`,
   *   the function skips the API call and directly moves to the next step.
   * - The `isSavingProject` state is managed to indicate the saving process.
   * - pass the second argument as SELECT_PROJECT_TYPE or PROJECT_BANNERS if you dont want to redirect to step
   */
  const saveProjectDetails = useCallback(
    async (
      data: Partial<IBannerbotProject> & {
        details?: Partial<IBannerbotProject['details']> & {
          business_details?: Partial<IBannerbotBusinessDetails>;
          ad_details?: IBannerbotAdDetails;
        };
      },
      currentStepId: OnboardingStepIds,
    ): Promise<void> => {
      try {
        setIsSavingProject(true);

        if (
          data.status !== BannerbotProjectStatus.COMPLETE ||
          currentStepId === OnboardingStepIds.PROJECT_BANNERS
        ) {
          const response = await updateProjectMutation.mutateAsync({
            data: data,
            headers: getCommonHeaders(user),
          });

          const project = response.data;
          setProjectDetails((prev) => ({
            ...prev,
            ...project,
            details: {
              ...prev.details,
              ...project.details,
              business_details: {
                ...prev.details?.business_details,
                ...project.details?.business_details,
              },
            },
          }));

          if (currentStepId === OnboardingStepIds.ADD_PRODUCT_DETAILS)
            router.replace(`/onboarding?id=${project.id}`);

          // this is only temporaray a way around to save project and not go to next step
          if (currentStepId !== OnboardingStepIds.SELECT_PROJECT_TYPE)
            if (currentStepId === OnboardingStepIds.SELECT_TYPE_OF_AD) {
              moveToNextStep(currentStepId, {
                selectedAdType: project.details.ad_details?.types[0],
              });
            } else {
              moveToNextStep(currentStepId);
            }

          getProjectQuery.refetch();
        } else {
          moveToNextStep(currentStepId);
          setIsSavingProject(false);
        }
      } catch (error: unknown) {
        if (error instanceof Error)
          logApiErrorAndShowToastMessage(
            error,
            'OnboardingProvider.saveProjectDetails',
          );
      } finally {
        setIsSavingProject(false);
      }
    },
    [getProjectQuery, moveToNextStep, router, updateProjectMutation, user],
  );

  const updateProjectDetails = (projectDetails: IBannerbotProject) => {
    setProjectDetails(projectDetails);
  };

  const updateProjectStatusIfNeeded = useCallback(async () => {
    if (projectDetails.status === BannerbotProjectStatus.COMPLETE) return;

    try {
      await updateProjectMutation.mutateAsync({
        data: {
          ...projectDetails,
          status: BannerbotProjectStatus.COMPLETE,
        },
        headers: getCommonHeaders(user),
      });
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error instanceof Error ? error : new Error('Failed to update project'),
        'saveFpVideoDetails.updateProjectStatusIfNeeded',
      );
      throw error;
    }
  }, [projectDetails, updateProjectMutation, user]);

  const generateVideo = useCallback(
    async (templateData: IFpVideoData) => {
      if (!projectDetails.id) {
        throw new Error('missing projectDetails id');
      }
      try {
        await generateFpVideoMutation.mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            project_id: projectDetails.id,
            template_data: templateData,
          },
        });
      } catch (error) {
        logApiErrorAndShowToastMessage(
          error instanceof Error
            ? error
            : new Error('Failed to generate video'),
          'saveFpVideoDetails.generateVideo',
        );
        throw error;
      }
    },
    [generateFpVideoMutation, projectDetails, user],
  );

  const saveFpVideoDetails = useCallback(
    async (fpData: typeof fpVideoData, currentStepId: OnboardingStepIds) => {
      if (!projectDetails.id) return;
      setIsSavingProject(true);
      setFpVideoData(fpData);

      const isEditing = currentStepId === OnboardingStepIds.EDIT_VIDEO_DATA;

      try {
        if (isEditing) {
          if (!fpData?.templateData) {
            throw new Error('missing templateData');
          }

          await updateProjectStatusIfNeeded();
          await generateVideo(fpData.templateData);
        }
        moveToNextStep(currentStepId);
      } catch (error) {
        // Error already logged in helper functions
        throw error;
      } finally {
        setIsSavingProject(false);
      }
    },
    [
      generateVideo,
      moveToNextStep,
      projectDetails.id,
      updateProjectStatusIfNeeded,
    ],
  );

  useEffect(() => {
    if (!projectId) {
      resetStates();
    }
  }, [projectId]);

  const createNewVideoBanner = useCallback(() => {
    setStepId(OnboardingStepIds.CHOOSE_VIDEO_TEMPLATE);
    router.push(`/onboarding?id=${projectId}`);
  }, [projectId, router]);

  return (
    <OnboardingContext.Provider
      value={{
        stepId,
        projectDetails,
        saveProjectDetails,
        isSavingProject,
        isOnboardingCompleted,
        previousStep: goBackPreviousStep,
        isInitialLoading,
        saveFpVideoDetails,
        fpVideoData,
        createNewVideoBanner,
        updateProjectDetails,
      }}
    >
      {projectId && isInitialLoading ? (
        <div className=" fixed inset-0 bg-blue-1 flex flex-col gap-2 items-center justify-center ">
          <div className=" w-[200px] ">
            <HorizontalLoader />
          </div>
          <TextV2 level={4} className=" text-gray-300 ">
            Loading your data
          </TextV2>
        </div>
      ) : (
        children
      )}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === null) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default OnboardingProvider;
