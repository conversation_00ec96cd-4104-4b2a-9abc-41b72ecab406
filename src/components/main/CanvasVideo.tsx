import React from 'react';
import { useEffect, useRef, useState } from 'react';
import { recordAndDownloadVideo, renderVideo } from '@/utils/canvas_video';
import SpinnerLoader from '../lib/SpinnerLoader';
import { IVideoData } from '@/types/video';
import { BannerSize } from '@/types/banner_templates';
import classNames from 'classnames';
import Link from 'next/link';
import ButtonV2 from '../lib/ButtonV2';

interface ICanvasVideoProps {
  data: IVideoData;
  size: BannerSize;
}

const CanvasVideo = (props: ICanvasVideoProps) => {
  const { data, size } = props;

  const [containerHeight, setContainerHeight] = useState(0);

  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const [videoGenInProgress, setVideoGenInProgress] = useState(false);
  const [audioPaused, setAudioPaused] = useState(false);

  const videoWidth = size === BannerSize.landscape ? 1600 : 1080;
  const videoHeight =
    size === BannerSize.landscape
      ? 900
      : size === BannerSize.portrait
        ? 1920
        : 1080;

  const initRendering = (resetVideoState: boolean) => {
    const canvas = canvasRef.current;
    if (canvas && containerHeight) {
      const canvasWidth = videoWidth;
      const canvasHeight = videoHeight;

      canvas.width = canvasWidth;
      canvas.height = canvasHeight;
      canvas.style.transform = `scale(${containerHeight / canvasHeight})`;
      canvas.style.transformOrigin = `top left`;

      const ctx = canvas.getContext('2d');

      if (!ctx) throw new Error('error in ctx');

      // clear the canvas (in case of blobs change, old one has to be cleared)
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      renderVideo({
        canvas,
        data,
        stopped: audioPaused,
        videoWidth,
        videoHeight,
        resetVideoState,
      });
    }
  };

  useEffect(() => {
    initRendering(true);
    if (audioRef.current) {
      audioRef.current.currentTime = 0; // restart audio
      void audioRef.current.play();
    }
  }, [containerRef.current, data, containerHeight]);

  useEffect(() => {
    initRendering(false);
  }, [audioPaused]);

  useEffect(() => {
    const width = containerRef.current?.clientWidth ?? 0;
    const height = width * (videoHeight / videoWidth);
    setContainerHeight(height);
  }, [size, videoHeight, videoWidth]);

  useEffect(() => {
    if (!audioRef.current) {
      return;
    }
    audioRef.current.onplay = () => {
      setAudioPaused(false);
    };

    audioRef.current.onpause = () => {
      setAudioPaused(true);
    };

    void audioRef.current?.play(); // autoplay
  }, []);

  const onDownloadVideoClick = async () => {
    if (!audioRef.current || !canvasRef.current) return;
    audioRef.current.currentTime = 0; // restart audio
    void audioRef.current.play();
    initRendering(true);
    setVideoGenInProgress(true);
    await recordAndDownloadVideo({
      canvas: canvasRef.current,
      audio: audioRef.current,
      videoDuration: data.total_duration,
      videoCaption: data.video_caption,
    });
    setVideoGenInProgress(false);
  };

  return (
    <div
      className={classNames('w-full flex flex-col items-center mt-12', {
        'max-w-80': size === BannerSize.portrait,
        'max-w-96': size === BannerSize.square,
      })}
    >
      <div
        ref={containerRef}
        style={{ height: containerHeight }}
        className="relative w-full bg-black"
      >
        <canvas ref={canvasRef} />
      </div>
      <audio
        src={data.audio_url}
        ref={audioRef}
        className="mt-5"
        key={data.audio_url}
        crossOrigin="anonymous"
      ></audio>
      <div
        className="cursor-pointer mt-5 text-white"
        onClick={() => {
          // toggle
          if (audioRef?.current?.paused) {
            void audioRef.current.play();
          } else {
            void audioRef.current?.pause();
          }
        }}
      >
        <p className="text-4xl">{audioPaused ? '▶️' : '⏸️'}</p>
      </div>
      <ButtonV2
        className="!py-2 !px-4 !text-sm mt-5"
        onClick={() => void onDownloadVideoClick()}
        disabled={videoGenInProgress}
      >
        {videoGenInProgress ? (
          <SpinnerLoader size={24} borderWidth={3} />
        ) : (
          'Download Video'
        )}
      </ButtonV2>
      <div className="mt-3 text-white text-xs">
        Photos provided by{' '}
        <Link
          href="https://www.pexels.com"
          className="underline"
          target="_blank"
        >
          Pexels
        </Link>
      </div>

      {videoGenInProgress ? (
        <p className="mt-3 mb-12 text-xs text-white font-medium text-center">
          It will take few seconds. Do not stop the track...
        </p>
      ) : null}
    </div>
  );
};

export default CanvasVideo;
