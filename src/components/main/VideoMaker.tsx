'use client';
import React from 'react';
import { useState } from 'react';
import { useMutation } from 'react-query';
import { getVideoData } from '../../actions/main';
import { BannerSize } from '../../types/banner_templates';
import { logApiErrorAndShowToastMessage } from '../../utils';
import SpinnerLoader from '../lib/SpinnerLoader';
import Link from 'next/link';
import CanvasVideo from './CanvasVideo';
import ButtonV2 from '../lib/ButtonV2';

const VideoMaker = () => {
  const [prompt, setPrompt] = useState('');

  // const router = useRouter();

  const [selectedVideoSize, setSelectedVideoSize] = useState(BannerSize.square);

  const getVideoDataMutation = useMutation(getVideoData);

  const onGenerateVideoClick = () => {
    getVideoDataMutation
      .mutateAsync({
        queryParams: {} as Record<string, string>,
        headers: {
          'Content-Type': 'application/json',
        },
        query: prompt,
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'VideoMaker.onGenerateBannersClick',
        );
      });
  };

  const videoData = getVideoDataMutation?.data?.data;

  return (
    <div className="flex flex-col items-center w-full">
      <p className="text-sm text-white mt-8">Enter prompt to generate video</p>
      <textarea
        className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2 text-gray"
        rows={4}
        onChange={(event) => {
          setPrompt(event.target.value);
        }}
        placeholder="Generate a video for GrowEasy, an AI powered lead generation tool"
        value={prompt}
      ></textarea>
      <div className="mt-5 flex flex-col items-center">
        <ButtonV2
          className="!py-2 !px-4 !text-sm"
          onClick={onGenerateVideoClick}
          disabled={getVideoDataMutation.isLoading}
        >
          {getVideoDataMutation.isLoading ? (
            <SpinnerLoader size={24} borderWidth={3} />
          ) : (
            'Create Video'
          )}
        </ButtonV2>
        <Link href="/" className="mt-4 text-white font-medium">
          Looking to generate banners?
        </Link>
      </div>

      {videoData?.frames?.length ? (
        <div className="flex flex-col w-full items-center">
          <div className="flex flex-col mt-12 w-full">
            <p className="text-white text-xs">
              Video will be downloaded in webm format. To convert into any other
              format e.g. mp4, you can use online tools like{' '}
              <a
                href="https://cloudconvert.com/webm-to-mp4"
                target="_blank"
                rel="noreferrer"
                className="underline"
              >
                this.
              </a>{' '}
              Use Chrome for better experience.
            </p>
            <div className="flex justify-center items-center mt-4">
              <p className="text-white text-base mr-1">Select size:</p>
              <select
                className="py-1 px-2 rounded outline-none capitalize"
                value={selectedVideoSize}
                onChange={(event) =>
                  setSelectedVideoSize(event.target?.value as BannerSize)
                }
              >
                <option>{BannerSize.square}</option>
                <option>{BannerSize.portrait}</option>
                <option>{BannerSize.landscape}</option>
              </select>
            </div>
          </div>
          <CanvasVideo data={videoData} size={selectedVideoSize} />
        </div>
      ) : null}
    </div>
  );
};

export default VideoMaker;
