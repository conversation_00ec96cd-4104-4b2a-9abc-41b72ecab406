import React from 'react';
import { useEffect, useState, useRef, ChangeEventHandler } from 'react';
import BottomSheet from '@/components/lib/BottomSheet';
import {
  IBannerImage,
  IBannerTemplate,
  IImageElement,
} from '@/types/banner_templates';
import CrossIcon from '@/images/common/cross.svg';
import BannerImageComp from './BannerImageComp';
import Image from 'next/image';
import { showToastMessage } from '@/modules/toast';
import { triggerUnsplashImageDownload } from '@/actions/main';
import ButtonV2 from '../lib/ButtonV2';

interface IEditBannerTemplateBsProps {
  template: IBannerTemplate;
  onTemplateUpdate: (template: IBannerTemplate) => void;
  onClose: () => void;
  stockImages: IBannerImage[];
}

const EditBannerTemplateBs = (props: IEditBannerTemplateBsProps) => {
  const { onTemplateUpdate, onClose, stockImages } = props;

  const [template, setTemplate] = useState(props.template);
  const bannerImageContainerRef = useRef<HTMLDivElement>(null);
  const uploadImageInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    onTemplateUpdate(template);
  }, [template, onTemplateUpdate]);

  const onUploadIconPress = () => {
    uploadImageInputRef.current?.click();
  };

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const file = e.target?.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result;
        onImageUrlChange(imageUrl as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onImageUrlChange = (url: string) => {
    const updatedTemplate = JSON.parse(
      JSON.stringify(template),
    ) as IBannerTemplate;
    const imageElement = updatedTemplate.elements?.find(
      (item) =>
        item.type === 'image' &&
        item.imageProps?.variableName === 'CREATIVE_IMAGE',
    ) as IImageElement;
    if (url.includes('base64,')) {
      imageElement.imageProps.url = url;
    } else {
      const urlObj = new URL(url);
      urlObj.searchParams.set('crop', 'edges');
      urlObj.searchParams.set('fit', 'crop');
      urlObj.searchParams.set('w', imageElement.container?.width?.toString());
      urlObj.searchParams.set('h', imageElement.container?.height?.toString());
      imageElement.imageProps.url = urlObj.toString();
    }
    setTemplate(updatedTemplate);
  };

  const inputChange = (value: string, variableName: string) => {
    const updatedTemplate = JSON.parse(
      JSON.stringify(template),
    ) as IBannerTemplate;
    for (const element of updatedTemplate.elements) {
      if (element.type === 'textArr') {
        for (const node of element.texts ?? []) {
          if (node.variableName === variableName) {
            node.value = value;
            break;
          }
        }
      } else if (element.type === 'button') {
        if (element.textProps?.variableName === variableName) {
          element.textProps.value = value;
        }
      }
    }
    setTemplate(updatedTemplate);
  };

  let selectedStockImageNode: IBannerImage | null = null;

  const onDownloadClick = () => {
    //logEvent(EVENT_NAMES.download_banner_clicked);
    if (bannerImageContainerRef.current) {
      const imageCanvas =
        bannerImageContainerRef.current.getElementsByTagName('canvas')[0];
      if (imageCanvas) {
        const link = document.createElement('a');
        link.download = 'bannerbot-banner.png';
        link.href = imageCanvas.toDataURL();
        link.click();
        showToastMessage('Banner downloaded!', 'success');
      }

      if (selectedStockImageNode?.download_location) {
        void triggerUnsplashImageDownload({
          download_location: selectedStockImageNode?.download_location,
        });
      }
    }
  };

  let title = '';
  let description = '';
  let cta = '';

  for (const element of template.elements) {
    if (element.type === 'textArr') {
      for (const node of element.texts ?? []) {
        if (node.variableName === 'CREATIVE_TITLE_TEXT') {
          title = node.value ?? '';
        } else if (node.variableName === 'CALL_OUT_TEXT') {
          description = node.value ?? '';
        }
      }
    } else if (element.type === 'button') {
      cta = element.textProps?.value ?? '';
    } else if (
      element.type === 'image' &&
      element.imageProps?.variableName === 'CREATIVE_IMAGE'
    ) {
      const creativeImageUrl = element.imageProps?.url;
      // iterate over images to find selected one
      for (const stockImage of stockImages) {
        if (
          stockImage.url &&
          new URL(stockImage.url).pathname ===
            new URL(creativeImageUrl ?? '').pathname
        ) {
          selectedStockImageNode = stockImage;
          break;
        }
      }
    }
  }

  return (
    <BottomSheet
      onClose={onClose}
      className="h-4/5"
      contentClassName="h-full flex flex-col flex-1"
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <p className="text-base text-gray-dark flex-1"> Edit Banner </p>
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className="mt-2 flex-1 flex-col overflow-y-scroll no-scrollbar">
          <div className="flex justify-center mt-3">
            <div className="w-60" ref={bannerImageContainerRef}>
              <BannerImageComp template={template} />
            </div>
          </div>
          <p className="mt-5 text-xs text-primary font-medium">
            Image Attribution:{' '}
            <i>
              Photo by{' '}
              <a
                href={selectedStockImageNode?.author_profile_url}
                target="_blank"
                rel="noreferrer"
                className="underline"
              >
                {selectedStockImageNode?.author_name}
              </a>{' '}
              on{' '}
              <a
                href={selectedStockImageNode?.unsplash_url}
                target="_blank"
                rel="noreferrer"
                className="underline"
              >
                Unsplash
              </a>
            </i>
          </p>
          <p className="text-sm text-gray-dark flex-1 mt-5"> Images </p>
          <div className="mt-3 flex items-center overflow-x-scroll no-scrollbar">
            <div
              className="w-16 h-16 rounded-full shrink-0 overflow-hidden mr-2 cursor-pointer"
              onClick={onUploadIconPress}
            >
              <Image
                src="/images/common/upload-icon.png"
                width="64"
                height="64"
                alt=""
              />
              <input
                type="file"
                ref={uploadImageInputRef}
                onChange={onUploadImageInputChange}
                className="hidden"
                accept="image/*"
              />
            </div>
            {stockImages.map((item, index) => {
              return (
                <div
                  className="w-16 h-16 rounded-full shrink-0 overflow-hidden mr-2 cursor-pointer"
                  key={index}
                  onClick={() => onImageUrlChange(item.url)}
                >
                  <Image
                    src={item.thumbnail_url}
                    width="64"
                    height="64"
                    alt=""
                  />
                </div>
              );
            })}
          </div>
          <p className="text-sm text-gray-dark flex-1 mt-5"> Title </p>
          <input
            className="outline-none w-full border border-gray-light rounded p-2 text-sm mt-2"
            type="text"
            value={title}
            onChange={(element) =>
              inputChange(element.target.value, 'CREATIVE_TITLE_TEXT')
            }
          />

          <p className="text-sm text-gray-dark flex-1 mt-5"> Description </p>
          <input
            className="outline-none w-full border border-gray-light rounded p-2 text-sm mt-2"
            type="text"
            value={description}
            onChange={(element) =>
              inputChange(element.target.value, 'CALL_OUT_TEXT')
            }
          />
          <p className="text-sm text-gray-dark flex-1 mt-5"> Button Text </p>
          <input
            className="outline-none w-full border border-gray-light rounded p-2 text-sm mt-2"
            type="text"
            value={cta}
            onChange={(element) =>
              inputChange(element.target.value, 'CTA_TEXT')
            }
          />
        </div>
        <div className="mt-6 flex flex-col">
          <ButtonV2 onClick={onClose}>
            <p>Done</p>
          </ButtonV2>
          <p
            className="mt-2 text-xs text-hyperlink text-center cursor-pointer font-medium"
            onClick={onDownloadClick}
          >
            Download
          </p>
        </div>
      </div>
    </BottomSheet>
  );
};

export default EditBannerTemplateBs;
