'use client';
import React from 'react';
import classNames from 'classnames';
import { useState } from 'react';
import { useMutation } from 'react-query';
import { getBanners } from '../../actions/main';
import { BannerSize, IBannerTemplate } from '../../types/banner_templates';
import { logApiErrorAndShowToastMessage } from '../../utils';
import SpinnerLoader from '../lib/SpinnerLoader';
import EditIcon from '@/images/common/edit.svg';
import BannerImageComp from './BannerImageComp';
import EditBannerTemplateBs from './EditBannerTemplateBs';
import Link from 'next/link';
import ButtonV2 from '../lib/ButtonV2';

const BannerMaker = () => {
  const [prompt, setPrompt] = useState('');

  // const router = useRouter();

  const [templates, setTemplates] = useState<IBannerTemplate[]>([]);
  const [selectedTemplateForEditing, setSelectedTemplateForEditing] =
    useState<IBannerTemplate | null>(null);
  const [selectedBannerSize, setSelectedBannerSize] = useState(
    BannerSize.square,
  );

  const getBannersMutation = useMutation(getBanners);

  const onGenerateBannersClick = () => {
    getBannersMutation
      .mutateAsync({
        queryParams: {} as Record<string, string>,
        headers: {},
        query: prompt,
      })
      .then((response) => {
        setTemplates(response?.banners);
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'BannerMaker.onGenerateBannersClick',
        );
      });
  };

  const onTemplateUpdate = (template: IBannerTemplate) => {
    const updatedTemplates = templates.map((item) => {
      if (item.id === template.id) {
        return template;
      }
      return item;
    });
    setTemplates(updatedTemplates);
  };

  const stockImages = getBannersMutation?.data?.images ?? [];

  return (
    <div className="flex flex-col items-center w-full">
      <p className="text-sm text-white mt-8">
        Enter prompt to generate banners
      </p>
      <textarea
        className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2 text-gray"
        rows={4}
        onChange={(event) => {
          setPrompt(event.target.value);
        }}
        placeholder="Create banners for GrowEasy, an AI powered lead generation tool"
        value={prompt}
      ></textarea>
      <div className="mt-5 flex flex-col items-center">
        <ButtonV2
          className="!py-2 !px-4 !text-sm"
          onClick={onGenerateBannersClick}
          disabled={getBannersMutation.isLoading}
        >
          {getBannersMutation.isLoading ? (
            <SpinnerLoader size={24} borderWidth={3} />
          ) : (
            'Generate Banners'
          )}
        </ButtonV2>
        <Link href="/video-maker" className="mt-4 text-white font-medium">
          Looking to create video?
        </Link>
      </div>
      {templates.length ? (
        <div className="flex flex-col mt-12 w-full">
          <p className="text-white">
            Click on edit icon to edit and Download banners
          </p>
          <div className="flex justify-center items-center mt-4">
            <p className="text-white text-base mr-1">Select size:</p>
            <select
              className="py-1 px-2 rounded outline-none capitalize"
              value={selectedBannerSize}
              onChange={(event) =>
                setSelectedBannerSize(event.target?.value as BannerSize)
              }
            >
              <option>{BannerSize.square}</option>
              <option>{BannerSize.portrait}</option>
              <option>{BannerSize.landscape}</option>
            </select>
          </div>
          <div className="flex flex-row flex-wrap mt-2">
            {templates
              ?.filter(
                (item) =>
                  !['0s', '0p', '0l'].includes(item.id) &&
                  item.size === selectedBannerSize,
              )
              ?.map((item, index) => {
                return (
                  <div
                    key={index}
                    className="w-1/2 mt-2 odd:pr-1 even:pl-1 relative"
                  >
                    <div className="relative">
                      <BannerImageComp template={item} />
                    </div>
                    <div
                      className={classNames(
                        'absolute top-0 left-0 w-full h-full p-3',
                      )}
                      style={{
                        background:
                          'linear-gradient(to bottom, rgba(0, 0, 0, 0.5), transparent)',
                      }}
                    >
                      <div className="flex items-center">
                        <div className="flex-1" />
                        <EditIcon
                          className="text-white cursor-pointer"
                          onClick={() => setSelectedTemplateForEditing(item)}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      ) : null}
      {selectedTemplateForEditing ? (
        <EditBannerTemplateBs
          template={selectedTemplateForEditing}
          onTemplateUpdate={onTemplateUpdate}
          onClose={() => setSelectedTemplateForEditing(null)}
          stockImages={stockImages}
        />
      ) : null}
    </div>
  );
};

export default BannerMaker;
