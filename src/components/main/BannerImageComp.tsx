import React from 'react';
import { useEffect, useState, useRef } from 'react';
import { IBannerTemplate } from '../../types/banner_templates';
import SpinnerLoader from '../lib/SpinnerLoader';
import { renderTemplate } from '@/utils/banner_image';

interface IBannerImageCompProps {
  template: IBannerTemplate;
  onRenderingDone?: () => void;
}

const BannerImageComp = (props: IBannerImageCompProps) => {
  const { template, onRenderingDone } = props;

  const [containerWidth, setContainerWidth] = useState(0);
  const [renderingCompleted, setRenderingCompleted] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const initRendering = async () => {
    const canvas = canvasRef.current;
    if (canvas && containerWidth) {
      setRenderingCompleted(false);
      const canvasWidth = template.width;
      const canvasHeight = template.height;

      if (!canvasHeight || !canvasWidth) {
        throw new Error('height and width not defined');
      }

      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      canvas.style.transform = `scale(${containerWidth / canvasWidth} )`;
      canvas.style.transformOrigin = `top left`;

      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('error in ctx');

      // clear the canvas (in case of template change, old one has to be cleared)
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      await renderTemplate(ctx, template);
      setRenderingCompleted(true);
      if (onRenderingDone) {
        onRenderingDone();
      }
    }
  };

  useEffect(() => {
    setContainerWidth(containerRef.current?.clientWidth ?? 0);

    void initRendering();
  }, [containerRef.current]);

  useEffect(() => {
    // debounce
    const timeout = setTimeout(() => {
      void initRendering();
    }, 400);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [template]);

  return (
    <div
      ref={containerRef}
      style={{
        // height: (containerWidth / template.width) * template.height,
        paddingTop: `${(template.height / template.width) * 100}%`,
      }}
      className="relative overflow-hidden "
    >
      <div className=" absolute top-0 left-0 ">
        <canvas ref={canvasRef} id={template.id}></canvas>
      </div>
      {renderingCompleted ? null : (
        <div className="absolute top-0 left-0 w-full h-full z-[1] flex items-center justify-center">
          <SpinnerLoader size={40} borderWidth={2} />
        </div>
      )}
    </div>
  );
};

export default BannerImageComp;
