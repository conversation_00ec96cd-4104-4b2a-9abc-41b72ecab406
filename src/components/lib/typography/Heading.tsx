import React, { HTMLAttributes, JSX } from 'react';

interface IHeadingProps extends HTMLAttributes<HTMLHeadingElement> {
  children?: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

const Heading = (props: IHeadingProps) => {
  const { children, level = 1, className = '' } = props;

  const sizes = {
    1: ' text-[39px] sm:text-[69px] leading-[110%] font-semibold',
    2: ' text-[34px] sm:text-[55px] leading-[110%] font-bold',
    3: ' text-[30px] sm:text-[44px] leading-[110%] font-bold',
    4: ' text-[26px] sm:text-[35px] leading-[110%] font-bold',
    5: ' text-[23px] sm:text-[28px] leading-[110%] font-semibold',
    6: ' text-[20px] sm:text-[23px] leading-[110%] font-semibold',
  };

  const baseClasses = 'text-system-black';

  const classes = `${baseClasses} ${sizes[level]}`;
  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;

  return (
    <HeadingTag className={`${classes} ${className}`}>{children}</HeadingTag>
  );
};

export default Heading;
