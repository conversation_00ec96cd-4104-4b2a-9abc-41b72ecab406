import React, { HTMLAttributes } from 'react';

interface ITextV2Props extends HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4;
  className?: string;
}

// className=' text- '

const TextV2 = (props: ITextV2Props) => {
  const { children, level = 1, className = '', ...remaining } = props;

  const sizes = {
    1: 'text-xl sm:text-2xl',
    2: 'text-lg sm:text-xl',
    3: 'text-base sm:text-lg',
    4: 'text-sm sm:text-base',
  };

  return (
    <p className={`${sizes[level]} ${className}`} {...remaining}>
      {children}
    </p>
  );
};

export default TextV2;
