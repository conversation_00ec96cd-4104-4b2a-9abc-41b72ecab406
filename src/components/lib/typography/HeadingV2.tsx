import React, { HTMLAttributes, JSX } from 'react';

interface IHeadingV2Props extends HTMLAttributes<HTMLHeadingElement> {
  children?: React.ReactNode;
  level?: 1 | 2 | 3 | 4;
  className?: string;
}

const HeadingV2 = (props: IHeadingV2Props) => {
  const { children, level = 1, className = '' } = props;

  const sizes = {
    1: 'text-[32px] sm:text-[42px] lg:text-[52px] leading-[120%] font-bold',
    2: 'text-[20px] sm:text-[24px] lg:text-[28px] leading-[120%] font-bold',
    3: 'text-[18px] sm:text-[20px] lg:text-[24px] leading-[120%] font-bold',
    4: 'text-[16px] sm:text-[18px] lg:text-[20px] leading-[120%] font-bold',
  };

  const baseClasses = 'text-system-black';

  const classes = `${baseClasses} ${sizes[level]}`;
  const HeadingV2Tag = `h${level}` as keyof JSX.IntrinsicElements;

  return (
    <HeadingV2Tag className={`${classes} ${className}`}>
      {children}
    </HeadingV2Tag>
  );
};

export default HeadingV2;
