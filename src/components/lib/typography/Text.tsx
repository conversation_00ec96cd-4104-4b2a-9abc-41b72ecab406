import React, { HTMLAttributes } from 'react';

interface ITextProps extends HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4;
  className?: string;
}

const Text = (props: ITextProps) => {
  const { children, level = 1, className = '', ...remaining } = props;

  const sizes = {
    1: 'text-base sm:text-lg',
    2: 'text-sm sm:text-base',
    3: 'text-sm',
    4: 'text-xs',
  };

  return (
    <p className={`${sizes[level]} ${className}`} {...remaining}>
      {children}
    </p>
  );
};

export default Text;
