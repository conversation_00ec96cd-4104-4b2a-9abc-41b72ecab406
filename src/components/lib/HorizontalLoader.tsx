import React from 'react';
export interface IHorizontalLoaderProps {
  className?: string;
}

const HorizontalLoader = (props: IHorizontalLoaderProps) => {
  const { className } = props;

  return (
    <div
      className={`w-full rounded-sm animate-preLoader h-1 bg-[length:20%] bg-[-25% 0] bg-repeat-y bg-gradient-to-r from-primary to-primary bg-[#ccc] ${className}`}
    />
  );
};

export default HorizontalLoader;
