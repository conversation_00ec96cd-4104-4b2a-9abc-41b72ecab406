import React, { useEffect, useState } from 'react';
import { IoClose } from 'react-icons/io5';
import DashBoardCards from '../onboarding/DashboardCard';
import Portal from './Portal';

interface IPopupProps {
  onClose: () => void;
  children: React.ReactNode;
}

const Popup = (props: IPopupProps) => {
  const { onClose, children } = props;

  const [showPopup, setShowPopup] = useState(true);

  const handleClose = () => {
    setShowPopup(false);

    setTimeout(() => {
      onClose();
    }, 300);
  };

  useEffect(() => {
    if (showPopup) {
      document.body.style.overflow = 'hidden';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [showPopup]);

  return (
    <Portal>
      <div
        data-showpopup={showPopup}
        className=" fixed inset-0 backdrop-blur-sm bg-black/35 z-10 flex items-center justify-center animate-in fade-in-0 data-[showpopup=false]:animate-out data-[showpopup=false]:fade-out-0 data-[showpopup=false]:duration-300 duration-500 "
      >
        <DashBoardCards
          data-showpopup={showPopup}
          className=" absolute z-10 border-2 border-white/40 rounded-xl max-w-[1024px] w-full mx-5 animate-in fade-in-0 slide-in-from-bottom-8 data-[showpopup=false]:animate-out data-[showpopup=false]:fade-out-0 data-[showpopup=false]:slide-out-to-bottom-8 data-[showpopup=false]:duration-300 duration-500 "
        >
          <div className=" flex justify-end absolute right-0 top-0 -mr-3 -mt-3 z-10 cursor-pointer ">
            <IoClose size={24} onClick={handleClose} />
          </div>
          {children}
        </DashBoardCards>
        <div className=" absolute inset-0 z-[1] " onClick={handleClose} />
      </div>
    </Portal>
  );
};

export default Popup;
