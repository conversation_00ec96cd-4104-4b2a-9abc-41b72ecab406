import classNames from 'classnames';
import React from 'react';

interface IInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
}

const Input = (props: IInputProps) => {
  const { className, ...rest } = props;
  return (
    <input
      className={classNames([
        ' outline-none w-full bg-blue-1 p-3 sm:p-4 rounded-lg border border-white/50 focus:ring-2 focus:ring-white/80 ',
        className,
      ])}
      {...rest}
    />
  );
};

export default Input;
