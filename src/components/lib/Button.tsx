import React from 'react';
import classNames from 'classnames';
import { cva, VariantProps } from 'class-variance-authority';

// interface IButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
//   children: ReactNode
//   className?: string
// }

// className = ' rounded-lg '

const buttonVariants = cva(
  'inline-flex items-center justify-center cursor-pointer whitespace-nowrap rounded-lg transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-blue-1 focus-visible:ring-white disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 leading-[120%]',
  {
    variants: {
      variant: {
        default: 'bg-red-1 text-white hover:bg-red-1/90',
        // destructive:
        //   "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        secondary:
          'shadow-[inset_0_0_0_3px] shadow-red-1 bg-transparent hover:bg-white/5 ',
        ghost: ' bg-transparent ',
        custom: '',
        outline:
          'bg-off-white text-red-1 shadow-sm hover:bg-red-1 hover:text-white ',
        // ghost: "hover:bg-accent hover:text-accent-foreground",
        // link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: ' max-md:h-12 p-3.5 px-6 text-[18px] gap-4 font-medium',
        pill: 'h-8 py-1.5 px-5 text-[16px] gap-2 !rounded-full',
        sm: ' rounded-md px-5 h-11 gap-1.5 text-base font-medium',
        responsive:
          ' p-3 px-6 text-[18px] gap-4 font-medium max-sm:rounded-md max-sm:px-5 max-sm:h-11 max-sm:gap-1.5 max-sm:text-base max-sm:font-medium',
        responsivePill:
          'py-1.5 px-5 text-[16px] gap-2 !rounded-full max-sm:h-6 max-sm:py-1 max-sm:px-3 max-sm:text-sm max-sm:gap-1',

        // lg: "h-10 rounded-md px-8",
        // icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface IButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

const Button = (props: IButtonProps) => {
  const { variant, size, children, className = '', ...remainingProps } = props;

  return (
    <button
      className={classNames(buttonVariants({ variant, size, className }), {
        'opacity-60': !!remainingProps.disabled,
      })}
      {...remainingProps}
    >
      {children}
    </button>
  );
};

export default Button;
