import React from 'react';
interface ISpinnerLoader {
  size?: number;
  className?: string;
  borderWidth?: number;
}

const SpinnerLoader = (props: ISpinnerLoader) => {
  const { size = 50, className = '', borderWidth = 5 } = props;

  return (
    <div
      style={{
        width: `${size}px`,
        height: `${size}px`,
        borderWidth: `${borderWidth}px`,
      }}
      className={`h-8 w-8 loader animate-spin rounded-full border-x-primary border-t-white border-b-primary ${className}`}
    />
  );
};

export default SpinnerLoader;
