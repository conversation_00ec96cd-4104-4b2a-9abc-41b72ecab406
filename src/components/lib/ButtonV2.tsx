import React from 'react';
import classNames from 'classnames';
import { ButtonHTMLAttributes, ReactNode } from 'react';

interface IButtonV2Props extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  className?: string;
}

const ButtonV2 = (props: IButtonV2Props) => {
  const { children, className = '', ...remainingProps } = props;

  return (
    <button
      className={classNames(
        `bg-primary text-white text-base font-semibold py-3 px-6 rounded-lg ${className}`,
        {
          'opacity-60': !!remainingProps.disabled,
        },
      )}
      {...remainingProps}
    >
      {children}
    </button>
  );
};

export default ButtonV2;
