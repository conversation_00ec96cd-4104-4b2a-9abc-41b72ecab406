import { useCallback, useEffect, useState } from 'react';

export default function Dialog({
  children,
  onClose,
  isOpen,
  setIsOpen,
  className,
}: {
  children: React.ReactNode;
  onClose?: () => void;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  className?: string;
}) {
  const [isReallyOpen, setIsReallyOpen] = useState(isOpen);

  const closeDialog = useCallback(() => {
    setTimeout(() => {
      document.body.style.overflow = '';
      setIsReallyOpen(false);
      setIsOpen(false);
      if (onClose) onClose();
    }, 200);
  }, [setIsReallyOpen, setIsOpen, onClose]);

  useEffect(() => {
    if (isOpen) {
      setIsReallyOpen(true);
    } else {
      closeDialog();
    }
  }, [isOpen, setIsReallyOpen, closeDialog]);

  useEffect(() => {
    if (isReallyOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }, [isReallyOpen]);

  useEffect(() => {
    if (isOpen) {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') setIsOpen(false);
      };
      window.addEventListener('keydown', handleEscape);
      return () => window.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, setIsOpen]);

  if (!isReallyOpen) return null;

  return (
    <div className=" fixed z-50 inset-0  !m-0 flex items-center justify-center">
      <div
        className={` absolute inset-0 z-0 bg-black/60 ${isOpen ? ' animate-in fade-in ' : ' animate-out fade-out '} duration-300 backdrop-blur-sm `}
        onClick={() => setIsOpen(false)}
      />
      <div
        className={` relative z-10 ${isOpen ? ' animate-in slide-in-from-bottom-8 fade-in ' : ' animate-out slide-out-to-bottom-8 fade-out '}  duration-300 ${className}`}
      >
        {children}
      </div>
    </div>
  );
}
