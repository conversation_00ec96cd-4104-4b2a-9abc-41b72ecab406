import { MAX_IMAGE_FILE_UPLOAD_SIZE_KB } from '@/constants';
import { showToastMessage } from '@/modules/toast';
import React, { ChangeEventHandler } from 'react';

interface IDragAndDropProps {
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>;
  handleUpload: ChangeEventHandler<HTMLInputElement>;
  children: React.ReactNode;
  className?: string;
  acceptedTypes: string[];
  maxFileSizeInKB?: number;
}

const DragAndDrop = (props: IDragAndDropProps) => {
  const {
    setIsDragging,
    handleUpload,
    children,
    className,
    acceptedTypes,
    maxFileSizeInKB = MAX_IMAGE_FILE_UPLOAD_SIZE_KB,
  } = props;

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);

    const filteredFiles = droppedFiles.filter((file) =>
      acceptedTypes.some(
        (type) =>
          type === file.type || // Exact match like "image/png"
          (type.endsWith('/*') && file.type.startsWith(type.split('/')[0])), // wildcard like "image/*"
      ),
    );

    if (filteredFiles.length > 0) {
      const fileList = new DataTransfer();
      Array.from(filteredFiles).forEach((file) => {
        const fileSizeInKb = file.size / 1024;
        if (fileSizeInKb > maxFileSizeInKB) {
          showToastMessage(
            `File "${file.name}" exceeds the maximum size of ${maxFileSizeInKB} KB.`,
            'error',
          );
        } else {
          fileList.items.add(file);
        }
      });

      const inputEvent = {
        target: { files: fileList.files },
      } as React.ChangeEvent<HTMLInputElement>;

      handleUpload(inputEvent);
    } else if (droppedFiles.length > 0) {
      showToastMessage(
        'Invalid file type! Please upload a valid file.',
        'error',
      );
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  return (
    <div
      onDrop={handleDrop}
      className={className}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      {children}
    </div>
  );
};

export default DragAndDrop;
