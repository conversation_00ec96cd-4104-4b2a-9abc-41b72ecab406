'use client';

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface IPortalProps {
  children: React.ReactNode;
}

const Portal = ({ children }: IPortalProps) => {
  const [mounted, setMounted] = useState(false);
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    setMounted(true);
    let portalDiv = document.getElementById('portal-root');

    if (!portalDiv) {
      portalDiv = document.createElement('div');
      portalDiv.id = 'portal-root';
      document.body.appendChild(portalDiv);
    }

    setPortalElement(portalDiv);

    return () => {
      setMounted(false);
    };
  }, []);

  return mounted && portalElement
    ? createPortal(children, portalElement)
    : null;
};

export default Portal;
