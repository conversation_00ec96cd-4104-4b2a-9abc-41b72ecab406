import classNames from 'classnames';
import React from 'react';

interface ITextarea extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  className?: string;
}

const Textarea = (props: ITextarea) => {
  const { className, ...rest } = props;
  return (
    <textarea
      className={classNames([
        ' outline-none w-full bg-blue-1 p-3 md:p-4 rounded-lg border border-white/50 focus:ring-2 focus:ring-white/80 ',
        className,
      ])}
      {...rest}
    />
  );
};

export default Textarea;
