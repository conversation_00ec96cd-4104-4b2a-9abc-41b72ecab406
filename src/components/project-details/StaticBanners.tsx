import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import EditPopup from './EditPopup';
import BannerImageContainer from './BannerImageContainer';
import { IBannerTemplate } from '@/types/banner_templates';
import Button from '../lib/Button';
import classNames from 'classnames';
import { HiHome } from 'react-icons/hi';
import { useRouter } from 'next/navigation';
import { useOnboarding } from '../context/OnboardingProvider';
import { getBannersV2, uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { useMutation, useQuery } from 'react-query';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import { OnboardingStepIds } from '@/types/onBoarding';
import {
  BannerbotAdType,
  BannerbotProjectStatus,
} from '@/types/bannerbotProject';
import SpinnerLoader from '../lib/SpinnerLoader';
import TextV2 from '../lib/typography/TextV2';
import AiBanners from './AiBanners';
import HorizontalLoader from '../lib/HorizontalLoader';

interface IStaticBannersProps {
  selectedBannerSize: 'square' | 'landscape' | 'portrait' | undefined;
  setSelectedBannerSize: React.Dispatch<
    React.SetStateAction<'square' | 'landscape' | 'portrait' | undefined>
  >;
  isMaxAiBannerMade: boolean;
  setIsMaxAiBannerMade: React.Dispatch<React.SetStateAction<boolean>>;
}

const StaticBanners = (props: IStaticBannersProps) => {
  const {
    selectedBannerSize,
    setSelectedBannerSize,
    isMaxAiBannerMade,
    setIsMaxAiBannerMade,
  } = props;

  const { projectDetails, saveProjectDetails, isSavingProject } =
    useOnboarding();
  const { user } = useUser();
  const router = useRouter();

  const [tempProjectDetails, setTempProjectDetails] = useState(
    JSON.parse(JSON.stringify(projectDetails)),
  );
  const [showPopup, setShowPopup] = useState(false);
  const [editPopupId, setEditPopupId] = useState<string>();
  const [templates, setTemplates] = useState<IBannerTemplate[]>();

  const bannerImageContainerRef = useRef<HTMLDivElement>(null);

  const uploadFileAndGetS3UrlMutation = useMutation(uploadFileAndGetS3Url);

  // useEffect(() => {
  //   if (projectDetails.details?.generated_banners) {
  //     setTemplates(projectDetails.details.generated_banners.templates);
  //     return;
  //   }
  //   setTemplates(getBannersQuery.data?.data.templates);
  // }, [projectDetails.details?.generated_banners, getBannersQuery.data]);

  const [loadedBannerCount, setLoadedBannerCount] = useState(0);

  const handleShowPopup = (id: string) => {
    setEditPopupId(id);
    setShowPopup(true);
  };

  const dataURLtoFile = async (dataUrl: string, fileName: string) => {
    const res = await fetch(dataUrl);
    const blob = await res.blob();
    return new File([blob], fileName, { type: 'image/png' });
  };

  const getBannersQuery = useQuery(
    ['getBannersV2', projectDetails.id],
    () => {
      return getBannersV2({
        data: projectDetails,
        headers: getCommonHeaders(user),
      });
    },
    {
      enabled: !!(
        tempProjectDetails.id &&
        !tempProjectDetails.details?.generated_banners &&
        selectedBannerSize !== undefined
      ), // only get banners if they are not saved
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 5 * 60 * 1000,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'ProjectBanners.getBannersQuery');
      },
    },
  );

  const stockImages = useMemo(() => {
    if (!projectDetails.details?.generated_banners)
      return [...(getBannersQuery.data?.data.images ?? [])];
    // no need to add product images if there is generated banners
    return projectDetails.details.generated_banners.images;
  }, [
    getBannersQuery.data?.data.images,
    projectDetails.details?.generated_banners,
  ]);

  const filteredTemplates = useMemo(
    () => templates?.filter((item) => item.size === selectedBannerSize) ?? [],
    [templates, selectedBannerSize],
  );

  useEffect(() => {
    const templates =
      projectDetails.details?.generated_banners?.templates ||
      getBannersQuery.data?.data.templates ||
      [];
    setTemplates(templates);
  }, [
    getBannersQuery.data,
    projectDetails.details?.generated_banners?.templates,
  ]);

  const saveInitialBanners = useCallback(async () => {
    if (!filteredTemplates) return;
    if (bannerImageContainerRef.current) {
      const imageCanvasArr =
        bannerImageContainerRef.current.getElementsByTagName('canvas');

      if (imageCanvasArr.length > 4) {
        const uploadBannersPromises = Array.from(imageCanvasArr)
          .slice(0, 4)
          .map(async (imageCanvas, i) => {
            const imageData = imageCanvas.toDataURL('image/png');

            const file = await dataURLtoFile(
              imageData,
              `${filteredTemplates[i].id}_${new Date().toLocaleDateString('en-GB').replaceAll('/', '_')}.png`,
            );
            return await uploadFileAndGetS3UrlMutation.mutateAsync({
              headers: getCommonHeaders(user),
              data: {
                file,
              },
            });
          });

        Promise.all(uploadBannersPromises)
          .then((response) => {
            const bannerImages = response.map((item, i) => ({
              url: item.data.s3_url,
              width: filteredTemplates[i].width,
              height: filteredTemplates[i].height,
              created_at: Date.now(),
            }));

            const tempProjectDetails: IBannerbotProject = {
              ...(projectDetails as IBannerbotProject),
            };

            tempProjectDetails.details.saved_assets = {
              SINGLE_IMAGE: [
                ...((tempProjectDetails.details.saved_assets ?? {})[
                  BannerbotAdType.SINGLE_IMAGE
                ] ?? []),
                ...bannerImages,
              ],
            };

            saveProjectDetails(
              tempProjectDetails,
              OnboardingStepIds.PROJECT_BANNERS,
            );
          })
          .catch((error: Error) => {
            logApiErrorAndShowToastMessage(
              error,
              'BannerImageContainer.saveInitialBanners',
            );
          });
      }
    }
  }, [
    filteredTemplates,
    projectDetails,
    saveProjectDetails,
    uploadFileAndGetS3UrlMutation,
    user,
  ]);

  useEffect(() => {
    setTempProjectDetails(JSON.parse(JSON.stringify(projectDetails)));
  }, [projectDetails]);

  // useEffect(() => {
  //   if (
  //     (projectDetails.details?.generated_banners?.templates ?? []).length > 0
  //   ) {
  //     setSelectedBannerSize('landscape');
  //   }
  // }, [projectDetails]);

  useEffect(() => {
    // check if the saved_assests.single_image have images or not if not then add images
    if (
      templates &&
      loadedBannerCount == 4 &&
      (
        projectDetails.details?.saved_assets?.[BannerbotAdType.SINGLE_IMAGE] ??
        []
      )?.length < 4
    ) {
      setLoadedBannerCount(0);
      saveInitialBanners();
    }
  }, [
    templates,
    loadedBannerCount,
    projectDetails.details?.saved_assets,
    saveInitialBanners,
  ]);

  const saveTemplatesToProject = (): void => {
    if (!templates) return;
    // this will also set the status to complete of the project
    const tempProjectDetails = {
      ...projectDetails,
      status: BannerbotProjectStatus.COMPLETE,
      details: {
        ...projectDetails.details,
        generated_banners: {
          templates,
          images: stockImages,
        },
      },
    };

    saveProjectDetails(tempProjectDetails, OnboardingStepIds.PROJECT_BANNERS);
  };

  const handleTemplateUpdate = (template: IBannerTemplate) => {
    setTemplates((prev) =>
      prev?.map((item) =>
        item.id === editPopupId ? (template as IBannerTemplate) : item,
      ),
    );
    setShowPopup(false);
    setEditPopupId(undefined);
  };

  return (
    <div className=" flex-1 flex flex-col pb-[64px] md:pb-[84px] space-y-6 ">
      {!selectedBannerSize ? (
        <div className=" flex-1 flex flex-col items-center justify-center space-y-4 sm:space-y-6 ">
          <TextV2 className=" text-gray-200 ">Select banner size</TextV2>
          <div className="flex max-sm:flex-col gap-3 sm:gap-4">
            {[
              {
                size: 'landscape',
                label: 'Landscape',
                dimensions: '1200x628',
                aspectClass: 'aspect-video',
              },
              {
                size: 'square',
                label: 'Square',
                dimensions: '1080x1080',
                aspectClass: 'aspect-square',
              },
              {
                size: 'portrait',
                label: 'Portrait',
                dimensions: '1080x1920',
                aspectClass: 'h-[266px]',
              },
            ].map(({ size, label, dimensions, aspectClass }) => (
              <div
                key={size}
                className="space-y-2 hover:scale-105 transition-all duration-300 origin-bottom cursor-pointer"
                onClick={() =>
                  setSelectedBannerSize(
                    size as 'landscape' | 'square' | 'portrait',
                  )
                }
              >
                <div
                  className={`w-[150px] ${aspectClass} bg-blue-1/90 border-2 border-purple-900/90 rounded-lg flex items-center justify-center hover:border-red-1/70 transition-all duration-300`}
                >
                  <TextV2 level={3} className="font-medium">
                    {label}
                  </TextV2>
                </div>
                <TextV2 level={4} className="text-center text-gray-200">
                  {dimensions}
                </TextV2>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <>
          <AiBanners
            bannerSize={selectedBannerSize}
            projectDetails={tempProjectDetails}
            setProjectDetails={setTempProjectDetails}
            isMaxAiBannerMade={isMaxAiBannerMade}
            setIsMaxAiBannerMade={setIsMaxAiBannerMade}
          />
          <div className=" flex-1 flex flex-col min-h-56 ">
            {filteredTemplates.length > 0 && stockImages.length > 0 ? (
              <div className=" space-y-3 ">
                <TextV2 level={1}>Editable Banners Powered by AI</TextV2>
                <div
                  className={classNames([
                    ' grid items-center justify-center gap-6  ',
                    {
                      ' grid-cols-1 sm:grid-cols-2 lg:grid-cols-3':
                        selectedBannerSize === 'square',
                      ' grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4':
                        selectedBannerSize === 'portrait',
                      ' grid-cols-1 md:grid-cols-2':
                        selectedBannerSize === 'landscape',
                    },
                  ])}
                  ref={bannerImageContainerRef}
                >
                  {(filteredTemplates ?? []).length > 0 &&
                    filteredTemplates?.map((item, index) => (
                      <BannerImageContainer
                        key={item.id}
                        template={item}
                        isTemplatesLoading={false}
                        handleShowPopup={handleShowPopup}
                        onInitDone={
                          index < 4
                            ? () => setLoadedBannerCount((prev) => prev + 1)
                            : undefined
                        }
                      />
                    ))}
                </div>
              </div>
            ) : (
              <div className=" flex-1 flex flex-col gap-2 items-center justify-center ">
                <div className=" w-[200px] ">
                  <HorizontalLoader />
                </div>
                <TextV2 level={4} className=" text-gray-300 ">
                  Fetching Banners
                </TextV2>
              </div>
            )}
          </div>

          <EditPopup
            showPopup={showPopup}
            setShowPopup={setShowPopup}
            saveChanges={handleTemplateUpdate}
            template={
              templates?.find(
                (item) => item.id === editPopupId,
              ) as IBannerTemplate
            }
            stockImages={stockImages}
          />
        </>
      )}
      <div className=" fixed w-full bottom-0 bg-blue-1 left-0 z-10 p-4 sm:p-5 ">
        <div className="  flex justify-center items-stretch flex-wrap gap-3 sm:gap-4  ">
          <Button
            className="  max-sm:flex-1 "
            variant={'outline'}
            size={'responsive'}
            onClick={() => router.push('/dashboard')}
          >
            <HiHome size={20} className=" max-sm:w-4 " />{' '}
            <span className=" max-sm:hidden "> Go to dashboard</span>
          </Button>
          {!projectDetails.details?.generated_banners && (
            <Button
              className="  max-sm:flex-1 "
              onClick={saveTemplatesToProject}
              size={'responsive'}
              disabled={
                isSavingProject ||
                getBannersQuery.isLoading ||
                !selectedBannerSize
              }
            >
              Save
              {isSavingProject && <SpinnerLoader size={20} borderWidth={2} />}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default StaticBanners;
