import React, { Change<PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useMemo, useState } from 'react';
import {
  IBannerImage,
  IBannerTemplate,
  IImageElement,
} from '@/types/banner_templates';
import BannerImageComp from '../main/BannerImageComp';
import TextV2 from '../lib/typography/TextV2';
import Input from '../lib/Input';
import { MdTextFields } from 'react-icons/md';
import { FaChevronDown, FaImage } from 'react-icons/fa6';
import Textarea from '../lib/Textarea';
import Image from 'next/image';
import {
  converNormalUrlToCdnUrl,
  getImageTransformationParams,
  getUpdatedCloudflareImageUrl,
  getUpdatedUnsplashImageUrl,
  getUpdatedUrl,
  logApiErrorAndShowToastMessage,
} from '@/utils';
import { useMutation } from 'react-query';
import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import FullScreenLoader from '../lib/FullScreenLoader';
import Button from '../lib/Button';
import Dialog from '../lib/Dialog';
import DashboardCard from '../onboarding/DashboardCard';
import { IoClose } from 'react-icons/io5';

interface IEditPopupProps {
  showPopup: boolean;
  setShowPopup: React.Dispatch<React.SetStateAction<boolean>>;
  template: IBannerTemplate;
  stockImages: IBannerImage[];
  saveChanges: (template: IBannerTemplate) => void;
}

const EditPopup = (props: IEditPopupProps) => {
  const { showPopup, setShowPopup, stockImages, saveChanges } = props;

  const { user } = useUser();

  const [template, setTemplate] = useState<IBannerTemplate>();

  const [showSection, setShowSection] = useState<
    'text' | 'image' | 'logo' | undefined
  >('text');

  const uploadImageMutation = useMutation(uploadFileAndGetS3Url);

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const file = e.target?.files?.[0];
    if (file) {
      // const reader = new FileReader();
      // reader.onload = (event) => {
      //   const imageUrl = event.target!.result;
      //   onImageUrlChange(imageUrl as string);
      // };
      // reader.readAsDataURL(file);

      uploadImageMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file,
          },
        })
        .then((response) => {
          onImageUrlChange(
            converNormalUrlToCdnUrl(response.data.s3_url, {
              fit: 'cover',
              q: 80,
            }),
          );
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error as Error,
            'EditPopup.onUploadImageInputChange',
          );
        });
    }
  };

  const onImageUrlChange = (
    url: string,
    param?: { width: number; height: number },
  ) => {
    const updatedTemplate = JSON.parse(
      JSON.stringify(template),
    ) as IBannerTemplate;

    const imageElement = updatedTemplate.elements?.find(
      (item) =>
        item.type === 'image' &&
        item.imageProps?.variableName === 'CREATIVE_IMAGE',
    ) as IImageElement;

    if (url.includes('base64,')) {
      imageElement.imageProps.url = url;
    } else if (url.includes('groweasy.ai/cdn-cgi/image')) {
      if (param) {
        const targetSize = getImageTransformationParams({
          imageHeight: param.height,
          imageWidth: param.width,
          targetAspectRatio:
            imageElement.container.width / imageElement.container.height,
        });

        imageElement.imageProps.url = getUpdatedCloudflareImageUrl(url, {
          width: targetSize.targetWidth,
          height: targetSize.targetHeight,
        });
      } else {
        imageElement.imageProps.url = getUpdatedCloudflareImageUrl(url, {
          width: imageElement.container?.width,
          height: imageElement.container?.height,
        });
      }
    } else if (url.includes('bannerbot-public.s3.ap-south-1')) {
      const cdnUrl = converNormalUrlToCdnUrl(url, {
        width: imageElement.container?.width,
        height: imageElement.container?.height,
        fit: 'cover',
        q: 100,
      });
      imageElement.imageProps.url = cdnUrl;
    } else {
      imageElement.imageProps.url = getUpdatedUnsplashImageUrl(url, {
        width: imageElement.container?.width,
        height: imageElement.container?.height,
      });
    }

    setTemplate(updatedTemplate);
  };

  const inputChange = (value: string, variableName: string) => {
    const updatedTemplate = JSON.parse(
      JSON.stringify(template),
    ) as IBannerTemplate;
    for (const element of updatedTemplate.elements) {
      if (element.type === 'textArr') {
        for (const node of element.texts ?? []) {
          if (node.variableName === variableName) {
            node.value = value;
            break;
          }
        }
      } else if (element.type === 'button') {
        if (element.textProps?.variableName === variableName) {
          element.textProps.value = value;
        }
      }
    }
    setTemplate(updatedTemplate);
  };

  const textValues = useMemo(() => {
    let title = '';
    let description = '';
    let cta = '';

    if (!template) {
      return {
        title,
        description,
        cta,
      };
    }

    for (const element of template.elements) {
      if (element.type === 'textArr') {
        for (const node of element.texts ?? []) {
          if (node.variableName === 'CREATIVE_TITLE_TEXT') {
            title = node.value;
          } else if (node.variableName === 'CALL_OUT_TEXT') {
            description = node.value;
          }
        }
      } else if (element.type === 'button') {
        cta = element.textProps?.value;
      }
    }

    return {
      title,
      description,
      cta,
    };
  }, [template]);

  useEffect(() => {
    setTemplate(props.template);
  }, [props.template]);

  return (
    <Dialog isOpen={showPopup} setIsOpen={setShowPopup}>
      <DashboardCard className=" absolute z-10 border-2 border-white/40 rounded-xl max-w-[1024px]  mx-5 animate-in fade-in-0 slide-in-from-bottom-8 data-[showpopup=false]:animate-out data-[showpopup=false]:fade-out-0 data-[showpopup=false]:slide-out-to-bottom-8 data-[showpopup=false]:duration-300 duration-500 ">
        <div className=" flex justify-end absolute right-0 top-0 -mr-3 -mt-3 z-10 cursor-pointer ">
          <IoClose size={24} onClick={() => setShowPopup(false)} />
        </div>
        <div className=" flex flex-col gap-4 ">
          <div className=" flex-1 max-h-[75vh] !overflow-y-auto no-scrollbar overflow-auto grid grid-cols-1 max-md:pt-4 max-md:grid-rows-[auto,1fr] md:grid-cols-2 gap-6 md:gap-5 min-h-[600px] ">
            <div className=" max-md:order-2 space-y-5 ">
              <div className=" p-3 bg-blue-1 border-2 border-white/65 rounded-lg overflow-hidden ">
                <div
                  className=" flex justify-between items-center cursor-pointer "
                  onClick={() =>
                    setShowSection((prev) =>
                      prev === 'text' ? undefined : 'text',
                    )
                  }
                >
                  <div className=" flex items-center gap-3 ">
                    <MdTextFields size={20} />{' '}
                    <TextV2 level={3}>Edit Text</TextV2>
                  </div>
                  <div>
                    <FaChevronDown
                      size={20}
                      className={` ${showSection === 'text' && '-rotate-180'} transition-all duration-300 `}
                    />
                  </div>
                </div>

                <div
                  className={` max-h-0 ${showSection === 'text' && '!max-h-[1000px] !duration-500 '} transition-all duration-150 `}
                >
                  <div className=" pt-6  ">
                    <label>
                      <TextV2 level={4} className=" font-medium ">
                        Heading
                      </TextV2>
                      <Input
                        className=" !p-3 !bg-[#2F1C6A] focus:!ring-1 mt-2 "
                        placeholder="Your ad heading goes here..."
                        value={textValues.title}
                        onChange={(element) =>
                          inputChange(
                            element.target.value,
                            'CREATIVE_TITLE_TEXT',
                          )
                        }
                      />
                    </label>
                    <div className=" mt-4 ">
                      <label>
                        <TextV2 level={4} className=" font-medium ">
                          Description
                        </TextV2>
                        <Textarea
                          className=" !p-3 !bg-[#2F1C6A] focus:!ring-1 mt-2 "
                          placeholder="Your ad description goes here..."
                          rows={5}
                          value={textValues.description}
                          onChange={(element) =>
                            inputChange(element.target.value, 'CALL_OUT_TEXT')
                          }
                        />
                      </label>
                    </div>
                    <div className=" mt-4 ">
                      <label>
                        <TextV2 level={4} className=" font-medium ">
                          Button Text
                        </TextV2>
                        <Input
                          className=" !p-3 !bg-[#2F1C6A] focus:!ring-1 mt-2 "
                          placeholder="eg. Book Now, Get Started"
                          value={textValues.cta}
                          onChange={(element) =>
                            inputChange(element.target.value, 'CTA_TEXT')
                          }
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className=" p-3 bg-blue-1 border-2 border-white/65 rounded-lg overflow-hidden  ">
                <div
                  className=" flex justify-between items-center cursor-pointer "
                  onClick={() =>
                    setShowSection((prev) =>
                      prev === 'image' ? undefined : 'image',
                    )
                  }
                >
                  <div className=" flex items-center gap-3 ">
                    <FaImage size={20} />{' '}
                    <TextV2 level={3}>Change Image</TextV2>
                  </div>
                  <div>
                    <FaChevronDown
                      size={20}
                      className={` ${showSection === 'image' && '-rotate-180'} transition-all duration-300 `}
                    />
                  </div>
                </div>

                <div
                  className={` max-h-0 ${showSection === 'image' && '!max-h-[1000px] !duration-500 '} transition-all duration-150 `}
                >
                  <div className=" pt-6  ">
                    <label>
                      <TextV2 level={4} className=" font-medium ">
                        Upload Image
                      </TextV2>

                      <div className=" mt-2 ">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={onUploadImageInputChange}
                        />
                      </div>
                    </label>
                    <div className=" mt-4 ">
                      <TextV2 level={4} className=" font-medium ">
                        AI Suggested Banner Image
                      </TextV2>
                      <div className=" flex items-center overflow-auto no-scrollbar mt-2 ">
                        {stockImages.map((item, index) => {
                          return (
                            <div
                              className="w-[120px] h-[120px] rounded shrink-0 overflow-hidden mr-3 border-2 border-white/80 cursor-pointer"
                              key={index}
                              onClick={() => onImageUrlChange(item.url)}
                            >
                              <Image
                                src={getUpdatedUrl(item.thumbnail_url, {
                                  width: 200,
                                  height: 200,
                                })}
                                width="120"
                                height="120"
                                alt=""
                              />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className=" max-md:hidden flex max-md:justify-center ">
                <Button
                  className=" max-sm:flex-1 min-w-40 "
                  onClick={() => template && saveChanges(template)}
                >
                  Save
                </Button>
              </div>
            </div>
            <div className=" self-start md:self-center relative flex-shrink-0  ">
              <div className=" relative z-[1] ">
                {template && <BannerImageComp template={template} />}
              </div>
              <div className="absolute z-0 top-0 left-0 w-full h-full flex items-center justify-center">
                <div className="bg-gray-medium rounded-lg w-full h-full text-sm after:inset-0 after:flex after:justify-center after:h-full after:items-center" />
              </div>
            </div>
          </div>
          <div className=" md:hidden order-3 flex max-md:justify-center ">
            <Button
              className=" max-sm:flex-1 min-w-40 "
              onClick={() => template && saveChanges(template)}
            >
              Save
            </Button>
          </div>
        </div>
      </DashboardCard>
      {uploadImageMutation.isLoading && <FullScreenLoader />}
    </Dialog>
  );
};

export default EditPopup;
