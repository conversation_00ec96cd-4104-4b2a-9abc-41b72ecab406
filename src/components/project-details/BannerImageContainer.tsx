import { showToastMessage } from '@/modules/toast';
import { IBannerTemplate } from '@/types/banner_templates';
import React, { useRef } from 'react';
import TextV2 from '../lib/typography/TextV2';
import { MdOutlineFileDownload } from 'react-icons/md';
import BannerImageComp from '../main/BannerImageComp';
import Button from '../lib/Button';
import { FiSave } from 'react-icons/fi';
import { useOnboarding } from '../context/OnboardingProvider';
import { OnboardingStepIds } from '@/types/onBoarding';
import { useMutation } from 'react-query';
import { uploadFileAndGetS3Url } from '@/actions/bannerbotV2';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import { BannerbotAdType } from '@/types/bannerbotProject';
import { logApiErrorAndShowToastMessage } from '@/utils';

const BannerImageContainer = (props: {
  isTemplatesLoading: boolean;
  template: IBannerTemplate;
  handleShowPopup: (id: string) => void;
  onInitDone?: () => void;
}) => {
  const { isTemplatesLoading, template, handleShowPopup, onInitDone } = props;
  const { user } = useUser();
  const { saveProjectDetails, projectDetails } = useOnboarding();

  const bannerImageContainerRef = useRef<HTMLDivElement>(null);

  const uploadFileAndGetS3UrlMutation = useMutation(uploadFileAndGetS3Url);

  const dataURLtoFile = async (dataUrl: string, fileName: string) => {
    const res = await fetch(dataUrl);
    const blob = await res.blob();
    return new File([blob], fileName, { type: 'image/png' });
  };

  const handleDownloadClick = () => {
    if (bannerImageContainerRef.current) {
      const imageCanvas =
        bannerImageContainerRef.current.getElementsByTagName('canvas')[0];
      if (imageCanvas) {
        const link = document.createElement('a');
        link.download = 'groweasy-banner.png';
        const imageData = imageCanvas.toDataURL('image/png');

        dataURLtoFile(
          imageData,
          `${template.id}_${new Date().toLocaleDateString('en-GB').replaceAll('/', '_')}.png`,
        ).then((file) => {
          uploadFileAndGetS3UrlMutation
            .mutateAsync({
              headers: getCommonHeaders(user),
              data: {
                file,
              },
            })
            .then((response) => {
              const url = response.data.s3_url;

              const tempProjectDetails: IBannerbotProject = {
                ...(projectDetails as IBannerbotProject),
              };

              tempProjectDetails.details.saved_assets = {
                SINGLE_IMAGE: [
                  {
                    url,
                    width: template.width,
                    height: template.height,
                  },
                  ...((tempProjectDetails.details.saved_assets ?? {})[
                    BannerbotAdType.SINGLE_IMAGE
                  ] ?? []),
                ],
              };

              saveProjectDetails(
                tempProjectDetails,
                OnboardingStepIds.PROJECT_BANNERS,
              );
            })
            .catch((error: Error) => {
              logApiErrorAndShowToastMessage(
                error,
                'BannerImageContainer.handleDownloadClick',
              );
            });
        });

        link.href = imageData;
        link.click();
        showToastMessage('Banner downloaded!', 'success');
      }
    }
  };

  return (
    <div className=" relative ">
      <div
        className=" absolute top-2 right-2 z-[1] rounded-lg flex items-center bg-blue-1 font-semibold cursor-pointer border-2 border-white/40 "
        onClick={handleDownloadClick}
      >
        <TextV2 level={3} className=" p-1 px-2 flex items-center gap-3 flex-1 ">
          <MdOutlineFileDownload size={20} />{' '}
        </TextV2>{' '}
      </div>
      <div ref={bannerImageContainerRef}>
        {!isTemplatesLoading ? (
          <BannerImageComp template={template} onRenderingDone={onInitDone} />
        ) : (
          <div className=" pt-[56.25%] bg-gray-400/30 " />
        )}
      </div>

      <Button
        className=" !bg-blue-1 hover:!bg-blue-1/70 mt-4 w-full "
        onClick={() => handleShowPopup(template.id)}
      >
        <FiSave size={20} />
        <TextV2 level={3} className=" max-[400px]:text-sm ">
          Edit & Customize Banner
        </TextV2>
      </Button>
    </div>
  );
};

export default BannerImageContainer;
