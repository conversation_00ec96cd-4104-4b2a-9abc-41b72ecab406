import React, { useCallback, useMemo } from 'react';
import { useOnboarding } from '../context/OnboardingProvider';
import TextV2 from '../lib/typography/TextV2';
import { BannerbotAdType } from '@/types/bannerbotProject';
import Button from '../lib/Button';
import { HiHome } from 'react-icons/hi';
import { useRouter } from 'next/navigation';
import { OnboardingStepIds } from '@/types/onBoarding';

const VideoBanners = () => {
  const { projectDetails, createNewVideoBanner, saveProjectDetails } =
    useOnboarding();

  const videoBanners = useMemo(
    () =>
      (projectDetails.details?.saved_assets?.[BannerbotAdType.VIDEO] ?? [])
        .slice()
        .reverse(),
    [projectDetails],
  );

  const router = useRouter();

  const addVideoToAdTypes = useCallback(() => {
    const adTypes = projectDetails.details?.ad_details?.types;
    if (!adTypes?.includes(BannerbotAdType.VIDEO)) {
      const newAdTypes = new Set([BannerbotAdType.VIDEO, ...(adTypes ?? [])]);

      saveProjectDetails(
        {
          ...projectDetails,
          details: {
            ...projectDetails.details,
            ad_details: {
              types: Array.from(newAdTypes),
            },
          },
        },
        OnboardingStepIds.PROJECT_BANNERS,
      );
    }
  }, [projectDetails, saveProjectDetails]);

  return (
    <>
      <div className=" flex-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 max-md:justify-center pb-[144px] min-[420px]:pb-[80px] md:pb-[84px] ">
        {videoBanners.length > 0 ? (
          videoBanners.map((item) =>
            item.url !== '' ? (
              <video
                src={item.url}
                controls
                className=" rounded-lg "
                style={
                  {
                    // height: (item.height / item.width) * 300,
                  }
                }
                key={item.id}
              />
            ) : (
              <div
                className=" relative rounded-lg bg-gray-500 flex flex-col gap-1 items-center justify-center text-center select-none "
                style={{ paddingTop: `${(item.height / item.width) * 100}%` }}
                key={item.id}
              >
                <div className=" absolute inset-0 flex flex-col justify-center items-center p-4 ">
                  <TextV2 level={3} className=" text-gray-50 ">
                    The video is being generated
                  </TextV2>
                  <p className=" !text-sm text-gray-200 ">
                    This might take around 2 minutes
                  </p>
                </div>
              </div>
            ),
          )
        ) : (
          <div className=" flex-1 sm:col-span-2 md:col-span-3 lg:col-span-4 text-center self-center ">
            <TextV2 level={3} className=" text-gray-400 select-none ">
              Please create some video banners to continue
            </TextV2>
          </div>
        )}
      </div>

      <div className=" fixed w-full bottom-0 bg-blue-1 left-0 z-10 p-4 sm:p-5 ">
        <div className=" flex justify-center items-stretch flex-wrap gap-4  ">
          <Button
            className="  max-sm:flex-1 "
            variant={'outline'}
            size={'responsive'}
            onClick={() => router.push('/dashboard')}
          >
            <HiHome size={20} className=" max-sm:w-4 " />{' '}
            <span className=" max-sm:hidden "> Go to dashboard</span>
          </Button>
          <Button
            className="  max-sm:flex-1 !gap-1 "
            size={'responsive'}
            onClick={() => {
              addVideoToAdTypes();
              createNewVideoBanner();
            }}
            //   disabled={isSavingProject}
          >
            Create new{' '}
            <span className=" max-[350px]:hidden "> Video Banner</span>
            {/* {isSavingProject && <SpinnerLoader size={20} borderWidth={2} />} */}
          </Button>
        </div>
      </div>
    </>
  );
};

export default VideoBanners;
