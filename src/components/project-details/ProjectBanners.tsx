import React, { useEffect, useRef, useState } from 'react';
import HeadingV2 from '../lib/typography/HeadingV2';
import Button from '../lib/Button';
import { FaChevronDown } from 'react-icons/fa6';
import TextV2 from '../lib/typography/TextV2';
import DashBoardCards from '../onboarding/DashboardCard';
import StaticBanners from './StaticBanners';
import { BannerbotAdType } from '@/types/bannerbotProject';
import { useOnboarding } from '../context/OnboardingProvider';
import { OnboardingStepIds } from '@/types/onBoarding';
import { useRouter, useSearchParams } from 'next/navigation';
import VideoBanners from './VideoBanners';

const ProjectBanners = () => {
  const { projectDetails, isSavingProject, saveProjectDetails } =
    useOnboarding();

  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [tempTitle, setTempTitle] = useState('');

  const [selectedBannerSize, setSelectedBannerSize] = useState<
    'square' | 'landscape' | 'portrait'
  >();
  const [showOrientationOptions, setShowOrientationOptions] = useState(false);
  const [showBannerTypeOptions, setShowBannerTypeOptions] = useState(false);
  const [isMaxAiBannerMade, setIsMaxAiBannerMade] = useState(false);

  const orientationOptionDivRef = useRef<HTMLDivElement>(null);
  const bannerTypeOptionDivRef = useRef<HTMLDivElement>(null);

  const searchParams = useSearchParams();
  const router = useRouter();

  const showBannerType = searchParams.get('banner-type') as BannerbotAdType;

  const changeShowBannerType = (bannerType: BannerbotAdType) => {
    const params = new URLSearchParams(searchParams.toString());

    params.set('banner-type', bannerType);

    router.replace(`?${params.toString()}`, { scroll: false });
  };

  useEffect(() => {
    if (
      !showBannerType ||
      !Object.values(BannerbotAdType).includes(showBannerType)
    ) {
      const params = new URLSearchParams(searchParams.toString());

      params.set('banner-type', BannerbotAdType.SINGLE_IMAGE);

      router.replace(`?${params.toString()}`, { scroll: false });
    }
  }, [searchParams, router, showBannerType]);

  useEffect(() => {
    const abortController = new AbortController();

    if (showOrientationOptions || showBannerTypeOptions) {
      document.body.addEventListener(
        'click',
        (e) => {
          const target = e.target as HTMLElement;
          if (
            showOrientationOptions &&
            !orientationOptionDivRef.current?.contains(target)
          ) {
            setShowOrientationOptions(false);
          }
          if (
            showBannerTypeOptions &&
            !bannerTypeOptionDivRef.current?.contains(target)
          ) {
            setShowBannerTypeOptions(false);
          }
        },
        {
          signal: abortController.signal,
        },
      );
    }

    return () => {
      abortController.abort();
    };
  }, [showOrientationOptions, showBannerTypeOptions]);

  const saveTitle = (title: string) => {
    saveProjectDetails(
      { ...projectDetails, title },
      OnboardingStepIds.PROJECT_BANNERS,
    );
  };

  const bannerTypes = [
    { type: BannerbotAdType.SINGLE_IMAGE, label: 'Image Banners' },
    { type: BannerbotAdType.VIDEO, label: 'Video Banners' },
    { type: BannerbotAdType.CAROUSEL, label: 'UGC Videos' },
  ];

  return (
    <div className=" flex flex-col ">
      <div className=" flex flex-wrap gap-y-4 justify items-center sticky z-10 top-0 bg-blue-1 py-4 -mt-6 ">
        <div className=" flex items-center gap-2  ">
          {isEditingTitle ? (
            <input
              value={tempTitle}
              className=" text-lg w-full p-2 px-4 bg-transparent outline-none border rounded-lg border-gray-500 "
              onChange={(e) => setTempTitle(e.target.value)}
              placeholder="Project Title"
            />
          ) : (
            <>
              <HeadingV2 level={3} className="sm:hidden">
                {projectDetails.title?.substring(0, 5)}
                {(projectDetails.title ?? '').length > 5 ? '...' : ''}
              </HeadingV2>
              <HeadingV2 level={3} className="hidden sm:block">
                {projectDetails.title}
              </HeadingV2>
            </>
          )}

          <Button
            size="pill"
            type="button"
            disabled={isSavingProject}
            onClick={() => {
              if (isEditingTitle) {
                if (tempTitle !== projectDetails.title) saveTitle(tempTitle);
                setIsEditingTitle(false);
                setTempTitle('');
              } else {
                setTempTitle(projectDetails.title ?? '');
                setIsEditingTitle(true);
              }
            }}
          >
            {isEditingTitle ? 'Save' : 'Edit'}
          </Button>
        </div>
        {!isEditingTitle && (
          <div className=" flex gap-4 ml-auto ">
            {(showBannerType === BannerbotAdType.SINGLE_IMAGE ||
              showBannerType === BannerbotAdType.AI_BANNER) &&
              selectedBannerSize !== undefined && (
                <div
                  className=" custom-select relative "
                  ref={orientationOptionDivRef}
                >
                  <div
                    className=" rounded-xl flex items-center w-fit bg-blue-1 font-semibold cursor-pointer border-2 border-white/40 hover:bg-gray-400/5 "
                    onClick={() => setShowOrientationOptions((prev) => !prev)}
                  >
                    <TextV2
                      level={4}
                      className=" p-3 max-sm:py-2 pr-0 flex items-center gap-3 flex-1 capitalize "
                    >
                      {selectedBannerSize}
                    </TextV2>
                    <div
                      className={` p-3 ${showOrientationOptions ? 'rotate-180' : 'rotate-0'} transition-all duration-300 `}
                    >
                      <FaChevronDown size={16} />
                    </div>
                  </div>
                  <div
                    className={` ${showOrientationOptions ? ' max-h-[300px] opacity-100 ' : 'max-h-0 opacity-70 '} absolute top-full right-0 z-10 overflow-hidden transition-all duration-300 `}
                  >
                    <div className=" bg-blue-1 rounded-xl mt-2 cursor-pointer border-2 border-white/40 ">
                      {[
                        { size: 'landscape', label: 'Landscape(1200x628)' },
                        { size: 'square', label: 'Square(1080x1080)' },
                        { size: 'portrait', label: 'Portrait(1080x1920)' },
                      ].map(({ size, label }) => (
                        <div
                          key={size}
                          className="p-3 hover:bg-white/5 "
                          onClick={() => {
                            setShowOrientationOptions(false);
                            setSelectedBannerSize(
                              size as 'landscape' | 'square' | 'portrait',
                            );
                          }}
                        >
                          <TextV2 level={4}>{label}</TextV2>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            {/* <Button>
            <FaPlus size={24} /> Add Logo & Brand Name
          </Button> */}
          </div>
        )}
      </div>

      <div className=" flex-1 ">
        <DashBoardCards
          className=" mt-2 !p-6 lg:!p-8 overflow-auto "
          innerClassName=" flex flex-col gap-6 md:gap-8 min-h-[600px] "
        >
          <div className=" relative sm:hidden " ref={bannerTypeOptionDivRef}>
            <Button
              className=" w-full flex !justify-between overflow-hidden "
              size={'responsive'}
              onClick={() => setShowBannerTypeOptions((prev) => !prev)}
            >
              Select Banner Type
              <span className=" inline-block bg-[#c3236b] py-4 px-2.5 -mr-5 ">
                <FaChevronDown
                  size={20}
                  className={` ${showBannerTypeOptions === true && ' -rotate-180 '} transition-all duration-300 `}
                />
              </span>
            </Button>

            <div
              className={` ${showBannerTypeOptions ? ' max-h-[400px] opacity-100 scale-100 ' : ' max-h-0 opacity-10 '} absolute z-10 top-full w-full mt-2 overflow-hidden transition-all duration-300 `}
            >
              <div className=" flex flex-col bg-white text-gray-900 border-2 border-gray-500/50 rounded-lg ">
                {bannerTypes.map(({ type, label }) => (
                  <TextV2
                    key={type}
                    level={3}
                    className=" h-11 flex items-center hover:bg-black/5 px-5 cursor-pointer "
                    onClick={() => {
                      setShowBannerTypeOptions(false);
                      changeShowBannerType(type);
                    }}
                  >
                    {label}
                  </TextV2>
                ))}
              </div>
            </div>
          </div>

          <div className=" max-sm:hidden bg-blue-1 p-2 flex gap-2.5 rounded-xl ">
            {bannerTypes.map(({ type, label }) => (
              <Button
                key={type}
                className="flex-1"
                variant={showBannerType === type ? 'default' : 'ghost'}
                size={'sm'}
                onClick={() => changeShowBannerType(type)}
              >
                {label}
              </Button>
            ))}
          </div>

          <div className=" flex-1 flex flex-col ">
            {showBannerType === BannerbotAdType.SINGLE_IMAGE ? (
              <StaticBanners
                selectedBannerSize={selectedBannerSize}
                setSelectedBannerSize={setSelectedBannerSize}
                isMaxAiBannerMade={isMaxAiBannerMade}
                setIsMaxAiBannerMade={setIsMaxAiBannerMade}
              />
            ) : showBannerType === BannerbotAdType.VIDEO ? (
              <VideoBanners />
            ) : (
              <div className=" h-[500px] flex items-center justify-center ">
                <HeadingV2 className=" bg-white/5 p-8 px-0 max-sm:w-full sm:px-28 text-center rounded-xl ">
                  Coming soon
                </HeadingV2>
              </div>
            )}
          </div>
        </DashBoardCards>
      </div>
    </div>
  );
};

export default ProjectBanners;
