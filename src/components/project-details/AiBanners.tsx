import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { BannerbotAdType } from '@/types/bannerbotProject';
import Image from 'next/image';
import { MdOutlineFileDownload } from 'react-icons/md';
import SpinnerLoader from '../lib/SpinnerLoader';
import { useRouter } from 'next/navigation';
import classNames from 'classnames';
import { useMutation } from 'react-query';
import { generateAiAdBanner } from '@/actions/bannerbotV2';
import { getCommonHeaders } from '@/actions';
import { useUser } from '../context/UserProvider';
import FetchError from '@/actions/FetchError';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { FaLock } from 'react-icons/fa6';

interface IAiBannersProps {
  bannerSize: 'square' | 'landscape' | 'portrait';
  projectDetails: Partial<IBannerbotProject>;
  setProjectDetails: React.Dispatch<
    React.SetStateAction<Partial<IBannerbotProject>>
  >;
  isMaxAiBannerMade: boolean;
  setIsMaxAiBannerMade: React.Dispatch<React.SetStateAction<boolean>>;
}

const MAX_BANNERS = 3;

// if the saved_asests.ai_banners doesn't contain then generate else show them
// generate 3 banners for free 1 or 0 and for paid 3 or less and keep other locked
const AiBanners = (props: IAiBannersProps) => {
  const { bannerSize, projectDetails, setProjectDetails } = props; // the bannerSize is only used for generating new banners
  const { user, userSubscription } = useUser();
  const [isGeneratingCount, setIsGeneratingCount] = useState(0);
  const [isPlanExhausted, setIsPlanExhausted] = useState(false);
  const logErrorRef = useRef(true);
  const router = useRouter();

  const filteredAiBanners = useMemo(() => {
    const aiBanners =
      projectDetails.details?.saved_assets?.[BannerbotAdType.AI_BANNER] || [];
    return aiBanners.filter((banner) => {
      if (bannerSize === 'landscape') return banner.width > banner.height;
      if (bannerSize === 'square') return banner.width === banner.height;
      if (bannerSize === 'portrait') return banner.width < banner.height;
      return false;
    });
  }, [projectDetails.details?.saved_assets, bannerSize]);

  const lockedAiBannersCount = useMemo(
    () => MAX_BANNERS - isGeneratingCount - filteredAiBanners.length,
    [filteredAiBanners, isGeneratingCount],
  );

  const generateAiAdBannerMutation = useMutation(generateAiAdBanner);

  const handleGenerateBanner = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setIsGeneratingCount((prev) => Math.min(prev + 1));

      generateAiAdBannerMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            project_id: projectDetails.id ?? '',
            size: bannerSize,
          },
        })
        .then((data) => {
          setProjectDetails((prev) => {
            const newProjectDetails = JSON.parse(
              JSON.stringify(prev),
            ) as IBannerbotProject;
            newProjectDetails.details ??= {};
            newProjectDetails.details.saved_assets ??= {};
            newProjectDetails.details.saved_assets[
              BannerbotAdType.AI_BANNER
            ] ??= [];
            newProjectDetails.details.saved_assets[
              BannerbotAdType.AI_BANNER
            ].push({
              ...data.data,
              url: data.data.s3_url,
            });
            return newProjectDetails;
          });
          resolve();
        })
        .catch((error) => {
          if (logErrorRef.current) {
            logApiErrorAndShowToastMessage(
              error,
              'AiBanners.generateAiAdBannerMutation',
            );
            logErrorRef.current = false;
            setIsPlanExhausted(true);
          }
          if (error instanceof FetchError) {
            const statusCode = error.statusCode;
            if (statusCode === 403) {
              // payment is required
              // router.push('/subscription');
            }
          }
          reject(error);
        })
        .finally(() => {
          setIsGeneratingCount((prev) => Math.max(prev - 1));
        });
    });
  }, [user, projectDetails, bannerSize]);

  useEffect(() => {
    const generateInitialBanners = async () => {
      try {
        // Only generate if we don't have max banners yet (max = 3)
        const remainingBanners = Math.max(
          0,
          MAX_BANNERS - filteredAiBanners.length,
        );

        const generateBannersCount =
          remainingBanners === 0
            ? 0
            : userSubscription?.isActive
              ? remainingBanners
              : 1;

        if (generateBannersCount !== 0) {
          for (let i = 0; i < generateBannersCount; i++) {
            handleGenerateBanner();
          }
        } else {
          setIsGeneratingCount(0);
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        setIsGeneratingCount(0);
      }
    };

    generateInitialBanners();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bannerSize]);

  return (
    <div className=" flex flex-col ">
      <div
        className={classNames([
          ' grid items-center justify-center gap-6  ',
          {
            ' grid-cols-1 sm:grid-cols-2 lg:grid-cols-3':
              bannerSize === 'square',
            ' grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4':
              bannerSize === 'portrait',
            ' grid-cols-1 md:grid-cols-2': bannerSize === 'landscape',
          },
        ])}
      >
        {filteredAiBanners?.map((item, index) => (
          <AiBannerImage {...item} key={index} />
        ))}
        {Array.from({ length: isGeneratingCount }).map((_, index) => (
          <div
            className=" rounded-xl overflow-hidden border-2 border-white/30 w-full h-full bg-gray-500/60 flex items-center justify-center "
            key={index}
          >
            <IsGeneratingComp bannerSize={bannerSize} />
          </div>
        ))}
        {lockedAiBannersCount !== 0 &&
          Array.from({ length: lockedAiBannersCount }).map((_, index) => (
            <div
              className=" rounded-xl overflow-hidden border-2 border-white/30 w-full h-full bg-gray-500/60 flex items-center justify-center "
              key={index}
            >
              <IsLockedComp
                bannerSize={bannerSize}
                handleClick={() => router.push('/subscription')}
                showZeroCredit={isPlanExhausted && userSubscription?.isActive}
              />
            </div>
          ))}
      </div>

      {/* (
        <div className=" flex-1 flex flex-col items-center justify-center ">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500">
              Create Beautiful AI Banners
            </h2>
            <p className="text-gray-200 max-w-2xl mx-auto">
              Generate stunning banners with artificial intelligence. Perfect
              for social media, websites, and marketing materials.
            </p>
          </div>
          <Button
            variant={'custom'}
            onClick={handleGenerateBanner}
            className=" mx-auto w-fit bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
          >
            <BsStars className="mr-2 h-5 w-5 text-yellow-300" />
            Generate Banner
          </Button>
        </div>
      ) */}

      {/* <div className=" fixed w-full bottom-0 bg-blue-1 left-0 z-10 p-4 sm:p-5 ">
        <div className="  flex justify-center items-stretch flex-wrap gap-3 sm:gap-4  ">
          <Button
            className="  max-sm:flex-1 "
            variant={'outline'}
            size={'responsive'}
            onClick={() => router.push('/dashboard')}
          >
            <HiHome size={20} className=" max-sm:w-4 " />{' '}
            <span className=" max-sm:hidden ">Go to dashboard</span>
          </Button>

          {aiBanners && aiBanners.length > 0 && (
            <Button
              variant={'custom'}
              onClick={handleGenerateBanner}
              size={'responsive'}
              className=" max-sm:flex-1 sm:w-fit bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
            >
              <BsStars size={20} className=" max-sm:w-4 text-yellow-300" />
              Generate Banner
            </Button>
          )}
        </div>
      </div> */}
    </div>
  );
};

const AiBannerImage = (props: {
  url: string;
  height: number;
  width: number;
}) => {
  const { height, url, width } = props;
  const [isDownloading, setIsDownloading] = useState(false);
  return (
    <div className=" relative ">
      <div
        className=" absolute right-2 top-2 z-[1] bg-blue-600/70 hover:bg-blue-600 p-1 rounded cursor-pointer hover:scale-105 transition-all duration-300 "
        onClick={() => {
          setIsDownloading(true);

          fetch(url)
            .then((response) => response.blob())
            .then((blob) => {
              // Create a FileReader to convert blob to base64 data URL
              const reader = new FileReader();
              reader.onloadend = function () {
                // reader.result contains the data URL with base64 encoding
                const dataUrl = reader.result;

                // Create link with data URL instead of blob URL
                const link = document.createElement('a');
                if (typeof dataUrl === 'string') {
                  link.href = dataUrl; // Use data URL directly
                } else {
                  console.error('Failed to convert blob to data URL');
                  return;
                }
                link.download = url.split('/').pop() || 'download';
                link.click();
              };
              reader.readAsDataURL(blob);
            })
            .catch((error) =>
              console.error('Error downloading the file:', error),
            )
            .finally(() => {
              setIsDownloading(false);
            });
        }}
      >
        {isDownloading ? (
          <SpinnerLoader size={20} borderWidth={3} />
        ) : (
          <MdOutlineFileDownload size={20} />
        )}
      </div>
      <div className=" absolute -top-4 left-3 h-8 px-4 rounded-full flex items-center bg-linear-to-r from-[#673DE6] to-[#FC5185] ring-2 ring-[#c8c8c8] ">
        <p className=" text-xs sm:text-sm "> Designed by AI</p>
      </div>
      <Image
        src={url}
        height={height / 3}
        width={width / 3}
        alt="AI generate image"
        className=" w-full rounded-xl "
      />
    </div>
  );
};

const IsGeneratingComp = ({ bannerSize }: { bannerSize: string }) => {
  const ref = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, []);

  return (
    <div
      ref={ref}
      className={classNames([
        'bg-purple-900 bg-opacity-20 rounded-lg p-6 flex items-center justify-center w-full ',
        {
          ' aspect-[9/16] ': bannerSize === 'portrait',
          ' aspect-video ': bannerSize === 'landscape',
          ' aspect-square ': bannerSize === 'square',
        },
      ])}
    >
      <div className="flex flex-col items-center">
        <div className="animate-pulse flex space-x-4 mb-4">
          <div className="h-12 w-12 rounded-full bg-gradient-to-r from-pink-500 to-purple-500"></div>
        </div>
        <p className="text-white font-medium text-center ">
          Generating your banner...
        </p>
      </div>
    </div>
  );
};

const IsLockedComp = ({
  bannerSize,
  handleClick,
  showZeroCredit = false,
}: {
  bannerSize: string;
  handleClick: () => void;
  showZeroCredit?: boolean;
}) => {
  const src = `/images/ai-locked/${bannerSize[0]}l.png`;
  return (
    <div
      className={classNames([
        'bg-purple-900 bg-opacity-20 rounded-lg w-full relative cursor-pointer ',
        {
          ' aspect-[9/16] ': bannerSize === 'portrait',
          ' aspect-video ': bannerSize === 'landscape',
          ' aspect-square ': bannerSize === 'square',
        },
      ])}
      onClick={() => {
        if (!showZeroCredit) handleClick();
      }}
    >
      <Image
        height={400}
        width={400}
        src={src}
        alt="locked banners"
        className=" w-full h-full "
      />
      <div className=" absolute inset-0 grid place-items-center ">
        <div className=" flex items-center gap-3 bg-blue-1/80 border-2 border-blue-1/85 p-2 rounded-xl ">
          <FaLock size={20} className=" max-sm:w-4 " />
          <p className="text-white font-medium text-center select-none ">
            {showZeroCredit ? '0 credit left for today' : 'Upgrade plan...'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AiBanners;
