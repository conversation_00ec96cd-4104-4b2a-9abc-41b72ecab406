'use client';

import React from 'react';
import Logo from '@/images/logo/logo-white.svg';
import Title from '@/images/logo/title.svg';
import Image from 'next/image';
import { useUser } from '../context/UserProvider';
import { useRouter } from 'next/navigation';
import TextV2 from '../lib/typography/TextV2';

const Header = () => {
  const { user } = useUser();
  const router = useRouter();

  return (
    <div className=" flex justify-between items-center ">
      <div className=" flex gap-2 md:gap-4 items-center ">
        <Logo className=" text-[#673DE6] h-[20px] md:h-[24px] lg:h-[30px] " />
        <Title className=" text-white  h-7 md:h-10 lg:h-12 " />
      </div>
      <div className=" flex-shrink-0 ">
        {user?.photoUrl ? (
          <Image
            src={user.photoUrl}
            width={48}
            height={48}
            alt="user profile image"
            className=" h-10 w-10 md:w-12 md:h-12 rounded-full cursor-pointer  "
            onClick={() => router.push('/profile')}
          />
        ) : (
          <TextV2
            className=" bg-white h-10 w-10 md:w-12 md:h-12 font-semibold flex items-center justify-center leading-none self-center rounded-full text-black cursor-pointer "
            onClick={() => router.push('/profile')}
          >
            {user?.displayName?.[0] ?? 'B'}
          </TextV2>
        )}
      </div>
    </div>
  );
};

export default Header;
