import React from 'react';
import Link from 'next/link';
import {
  FOOTER_NAVLINKS,
  // LEAD_GEN_SEO_CITIES_LIST,
  // LEAD_GEN_SEO_INDUSTRIES_LIST,
} from '@/constants/seo';

// import BLOGS_CATEGORIES_DATA from '@/constants/seo/data/blogs-categories';

interface IFooterNavlinksFlat {
  navlinkClassName?: string;
  className?: string;
}

const FooterNavlinksFlat = (props: IFooterNavlinksFlat) => {
  const { navlinkClassName = '', className = '' } = props;

  /*const blogsCategoriesResponse = useQuery(
    'getBlogsCategories',
    () => {
      return getBlogsCategories({
        headers: {},
        queryParams: {},
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'FooterNavlinksFlat.blogsCategoriesResponse',
        );
      },
    },
  );*/

  return (
    <div
      className={`flex flex-row justify-center items-center flex-wrap ${className}`}
    >
      {FOOTER_NAVLINKS.map((item, index) => {
        return (
          <Link
            href={item.href}
            className={`underline mx-[10px] my-2 text-xs font-medium whitespace-nowrap ${navlinkClassName}`}
            key={index}
            target="_blank"
          >
            {item.label}
          </Link>
        );
      })}
      {/* <div className="h-0.5 w-full my-8 bg-gray-light" />
      {LEAD_GEN_SEO_CITIES_LIST.map((item, index) => {
        return (
          <Link
            href={`/lead-generation/cities/${item.city.toLowerCase()}`}
            className={`underline mx-[10px] my-2 text-xs font-medium whitespace-nowrap ${navlinkClassName}`}
            key={index}
          >
            Lead Generation in {item.city}
          </Link>
        );
      })}
      <div className="h-0.5 w-full my-8 bg-gray-light" />
      {LEAD_GEN_SEO_INDUSTRIES_LIST.map((item, index) => {
        return (
          <Link
            href={`/lead-generation/industry/${item.slug}`}
            className={`underline mx-[10px] my-2 text-xs font-medium whitespace-nowrap ${navlinkClassName}`}
            key={index}
          >
            {item.industry} Lead Generation
          </Link>
        );
      })}
      {BLOGS_CATEGORIES_DATA?.length ? (
        <>
          {BLOGS_CATEGORIES_DATA.map((item, index) => {
            return (
              <Link
                href={`/blogs/${item.category_url}`}
                className={`underline mx-[10px] my-2 text-xs font-medium whitespace-nowrap ${navlinkClassName}`}
                key={index}
                target="_blank"
              >
                {item.category_name}
              </Link>
            );
          })}
        </>
      ) : null} */}
    </div>
  );
};

export default FooterNavlinksFlat;
