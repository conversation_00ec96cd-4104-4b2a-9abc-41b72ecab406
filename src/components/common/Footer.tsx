import React from 'react';
import LandingPageContainer from '../LandingPageContainer';
import FooterNavlinksFlat from './FooterNavlinksFlat';

const Footer = ({ className }: { className?: string }) => {
  return (
    <LandingPageContainer
      className={` pb-9 sm:pb-[66px] lg:pb-[100px] bg-gray-50 p-6 md:p-8 ${className} `}
    >
      <footer>
        <FooterNavlinksFlat />
        <div className="h-0.5 w-full my-10 bg-[#767676] bg-opacity-40 " />
        <p className="text-xs text-center">
          &copy; 2024 AUTOTME SOFTWARE PRIVATE LIMITED. All rights reserved.
        </p>
      </footer>
    </LandingPageContainer>
  );
};

export default Footer;
