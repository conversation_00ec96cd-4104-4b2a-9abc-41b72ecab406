import React from 'react';
import Heading from '../lib/typography/Heading';
import { HiCheck } from 'react-icons/hi';
import Text from '../lib/typography/Text';
import Link from 'next/link';

// Pricing data object to store all plan information
const PRICING_DATA = {
  starter: {
    name: 'Free',
    currentPrice: 'Free',
    buttonVariant: 'outline',
    featuresTitle: 'Include:',
    featuresHighlight: 'Essential',
    features: [
      'Unlimited templatized banners',
      '1 AI banners per day',
      '1 templatized videos per day',
    ],
  },
  pro: {
    name: 'Paid',
    originalPrice: '199',
    currentPrice: '49',
    buttonVariant: 'filled',
    featuresTitle: 'Include:',
    featuresHighlight: 'Essential',
    features: [
      'Unlimited templatized banners',
      '25 AI banners per day',
      '25 templatized videos per day',
    ],
  },
};

const PriceTag = ({
  originalPrice,
  currentPrice,
}: {
  originalPrice?: string;
  currentPrice: string;
}) => (
  <div className=" space-y-2 ">
    {originalPrice && (
      <Heading level={6}>
        <s className="text-gray-1">$ {originalPrice}</s>
      </Heading>
    )}
    <Heading level={3}>
      {currentPrice.toLowerCase() !== 'free' && <span>$</span>}
      <span className=" font-semibold">
        {currentPrice}
        {currentPrice.toLowerCase() !== 'free' && '.00'}
      </span>
      {currentPrice.toLowerCase() !== 'free' && <span>/mo</span>}
    </Heading>
  </div>
);

// Component for displaying features
const PlanFeatures = ({
  title,
  highlight,
  features,
}: {
  title: string;
  highlight: string;
  features: Array<string>;
}) => (
  <div className=" space-y-4 ">
    <Heading level={6} className="uppercase">
      <span className="text-blue-2">{highlight} </span>
      {title}
    </Heading>
    <div className=" space-y-2 ">
      {features.map((feature, index) => (
        <div key={index} className="flex items-center gap-4">
          <HiCheck size={24} className=" text-green-1" />
          {feature === 'Human Designer Add-On' ? (
            <Text className=" text-lg ">
              <span className="text-blue-2 font-medium ">Human Designer </span>
              Add-On
            </Text>
          ) : (
            <Text>{feature}</Text>
          )}
        </div>
      ))}
    </div>
  </div>
);

interface IPricingCardProps {
  name: string;
  originalPrice?: string;
  currentPrice: string;
  buttonVariant: string;
  featuresTitle: string;
  featuresHighlight: string;
  features: Array<string>;
}

const PricingCard = (props: IPricingCardProps) => {
  const {
    name,
    originalPrice,
    currentPrice,
    buttonVariant,
    featuresTitle,
    featuresHighlight,
    features,
  } = props;
  return (
    <div className=" p-6 sm:p-8 border-2 md:border-4 border-[#673de6] rounded-2xl space-y-6 sm:space-y-8 flex flex-col justify-between ">
      <div className=" space-y-12 lg:space-y-14 ">
        <Heading level={4} className="font-semibold">
          {name}
        </Heading>

        <PriceTag originalPrice={originalPrice} currentPrice={currentPrice} />

        <Link href={'/subscription'}>
          <button
            className={` w-full border-2 text-center border-[#673de6] px-10 py-4 rounded-xl cursor-pointer text-xl font-medium transition-all duration-300  ${
              buttonVariant === 'filled'
                ? '!bg-[#673de6] !text-white'
                : '!bg-transparent !text-[#673de6]'
            } `}
          >
            Choose Plan
          </button>
        </Link>
        {/* <Button
          className={`w-full mt-6 sm:mt-11 lg:mt-16 border-4 border-blue-2 
        ${
          buttonVariant === 'filled'
            ? '!bg-blue-2 !text-white'
            : '!bg-transparent !text-blue-2'
        }`}
        >
        </Button> */}
      </div>

      <hr className="border-[#673de6]" />

      <PlanFeatures
        title={featuresTitle}
        highlight={featuresHighlight}
        features={features}
      />
    </div>
  );
};

const PlanPricing = () => {
  return (
    <div className=" space-y-12 sm:space-y-16 lg:space-y-20 max-w-5xl mx-auto ">
      <Heading level={3} className="text-center">
        Plans & Pricing
      </Heading>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 ">
        <PricingCard {...PRICING_DATA.starter} />
        <PricingCard {...PRICING_DATA.pro} />
      </div>
    </div>
  );
};

export default PlanPricing;
