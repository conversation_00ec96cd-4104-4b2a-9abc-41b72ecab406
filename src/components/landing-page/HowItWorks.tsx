import React from 'react';
import LandingPageContainer from '../LandingPageContainer';
import { Montser<PERSON> } from 'next/font/google';
import Heading from '../lib/typography/Heading';
import Text from '../lib/typography/Text';
import CurvedArrowIcon from '@/images/icons/curved-arrow.svg';
import Link from 'next/link';

const montserrat = Montserrat({
  weight: ['700'],
  subsets: ['latin'],
});

const HOW_IT_WORKS = [
  {
    heading: 'Input Your Campaign Goals',
    text: 'Provide a few details like target audience, platform, and campaign type.',
  },
  {
    heading: 'AI Creates Stunning Ads Instantly',
    text: 'Get visually optimized banners, videos, and carousel ads in seconds.',
  },
  {
    heading: 'Need Something Extra?',
    text: 'Tap into our human design team for unique, custom-made creatives.',
  },
  {
    heading: 'Download or Deploy Directly',
    text: 'Export your designs or integrate them into your campaigns seamlessly.',
  },
];

const HowItWorks = () => {
  return (
    <LandingPageContainer
      rounded
      className=" relative z-[1] px-4 py-12 sm:p-16 sm:px-10 lg:p-20 rounded-t-[70px] sm:rounded-t-[144px] lg:rounded-t-[200px] bg-blue-1 "
      innerClassName=" max-w-[1440px] "
    >
      <div className=" relative z-10 space-y-12 sm:space-y-16 lg:space-y-20 ">
        <Heading level={3} className=" text-center ">
          How It Works
        </Heading>

        <div className=" flex flex-col gap-8 max-lg:items-center space-y-8 ">
          {HOW_IT_WORKS.map((item, index) => (
            <Card
              key={index}
              index={index + 1}
              {...item}
              last={index === HOW_IT_WORKS.length - 1}
            />
          ))}
        </div>

        <div className=" flex sm:justify-center ">
          <Link href="/login" className=" max-sm:w-full ">
            <div className=" bg-red-2 px-10 py-4 sm:py-5 rounded-xl cursor-pointer max-sm:text-center sm:w-fit text-xl font-medium hover:bg-red-1 transition-all duration-300 ">
              Get Started for Free
            </div>
          </Link>
        </div>
      </div>

      {/* <BackgroundCircles className=" !absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 " />
      <BackgroundCircles className=" !absolute bottom-0 left-0 -translate-x-1/2 translate-y-1/2 " /> */}
      <div className=" absolute inset-0 bg-blue-1 z-[1] top-1/2 h-full  " />
    </LandingPageContainer>
  );
};

interface ICard {
  index: number;
  heading: string;
  text: string;
  last?: boolean;
}

const Card = (props: ICard) => {
  const { index, heading, text, last } = props;

  return (
    <div className={` flex w-fit ${index % 2 === 0 ? ' lg:ml-auto ' : ''} `}>
      {!last && index % 2 === 0 && (
        <div
          className=" max-lg:hidden w-fit origin-center self-end "
          style={{
            transform: 'rotateY(180deg)',
          }}
        >
          <CurvedArrowIcon className=" text-white w-[200px] " />
        </div>
      )}

      <div className=" text-center max-w-lg xl:max-w-2xl ">
        <p
          className={` ${montserrat} relative text-[134px] leading-[120%] text-blue-2 font-bold drop-shadow-[6px_6px_0px_#fff] `}
        >
          {index}
        </p>
        <div className=" space-y-2 ">
          <Heading level={6}>{heading}</Heading>
          <Text>{text}</Text>
        </div>
      </div>

      {!last && index % 2 === 1 && (
        <div className=" max-lg:hidden self-end ">
          <CurvedArrowIcon className=" text-white w-[200px] " />
        </div>
      )}
    </div>
  );
};

export default HowItWorks;
