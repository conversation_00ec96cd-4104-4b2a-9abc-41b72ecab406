import React from 'react';
import LandingPageContainer from '../LandingPageContainer';
import Heading from '../lib/typography/Heading';
import Text from '../lib/typography/Text';
// import BackgroundCircles from './BackgroundCircles';
import Link from 'next/link';

const StartCreatingAds = () => {
  return (
    <LandingPageContainer
      rounded
      className=" relative z-[1] rounded-t-[70px] sm:rounded-t-[144px] lg:rounded-t-[200px] px-4 py-12 sm:p-16 sm:px-10 lg:p-20 bg-blue-1 text-white overflow-hidden "
      innerClassName=" space-y-12 sm:space-y-16 lg:space-y-20 "
    >
      <div className=" relative z-[1] space-y-4 ">
        <Heading level={3} className=" text-center ">
          Start Creating Ads Like Never Before
        </Heading>
        <Text className=" text-center  ">
          Join thousands of marketers saving time and boosting ad performance
          with Bannerbot.
        </Text>
      </div>
      <div className=" flex items-center justify-center ">
        <Link href="/login" className=" max-sm:w-full ">
          <div className=" bg-red-2 px-10 py-4 sm:py-5 rounded-xl cursor-pointer max-sm:text-center sm:w-fit text-xl font-medium hover:bg-red-1 transition-all duration-300 ">
            Get Started for Free
          </div>
        </Link>
      </div>
      {/* <BackgroundCircles className=" !absolute top-0 left-0 -translate-x-1/2 -translate-y-1/2 " /> */}
    </LandingPageContainer>
  );
};

export default StartCreatingAds;
