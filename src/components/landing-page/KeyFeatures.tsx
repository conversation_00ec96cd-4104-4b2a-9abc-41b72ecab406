import React from 'react';
import LandingPageContainer from '../LandingPageContainer';
import Heading from '../lib/typography/Heading';
import Text from '../lib/typography/Text';
import ImageIcon from '@/images/icons/image.svg';
import { FaPencilRuler } from 'react-icons/fa';
import PaintCircleRectangleIcon from '@/images/icons/paint-circle-rectangle.svg';
import { BiBarChart } from 'react-icons/bi';

const KEY_FEATURES_DATA = [
  {
    Icon: ImageIcon as React.ComponentType<{ className?: string }>,
    heading: 'AI-Powered Ad Creation',
    body: 'Generate stunning ad banners, video banners, and carousels in all major platform sizes with minimal input.',
  },
  {
    Icon: FaPencilRuler,
    heading: 'Unlimited Designs',
    body: 'One subscription, unlimited creative possibilities every month.',
  },
  {
    Icon: PaintCircleRectangleIcon,
    heading: 'Human Design Expertise',
    body: 'For complex requirements, outsource to our expert human designers - seamlessly within the same plan.',
  },
  {
    Icon: BiBarChart,
    heading: 'Platform Optimization',
    body: 'Tailor-made designs for Facebook, Google, TikTok, and YouTube to maximize performance and engagement.',
  },
];

const KeyFeatures = () => {
  return (
    <LandingPageContainer
      rounded
      className=" relative bg-off-white text-black rounded-t-[70px] sm:rounded-t-[144px] lg:rounded-t-[200px] px-4 py-12 sm:p-16 sm:px-10 lg:p-20 "
    >
      <div className=" relative z-[2]">
        <div className=" flex flex-col gap-12 sm:gap-16 lg:gap-20 ">
          <Heading level={3} className=" text-center ">
            Key Features
          </Heading>

          <div className=" flex flex-wrap justify-center gap-6 sm:gap-8 ">
            {KEY_FEATURES_DATA.map((item, index) => (
              <Card {...item} key={index} />
            ))}
          </div>
        </div>
      </div>
      <div className=" absolute inset-0 bg-off-white z-[1] top-1/2 h-full  " />
    </LandingPageContainer>
  );
};

interface ICard {
  Icon: React.ComponentType<{ className?: string }>;
  heading: string;
  body: string;
}

const Card = (props: ICard) => {
  const { Icon, heading, body } = props;
  return (
    <div className=" p-6 sm:p-8 flex-1 min-w-[280px] flex-shrink max-w-[440px] rounded-2xl border-2 border-[#673de6] bg-[#e3d9fc] space-y-6 sm:space-y-8 ">
      <div className=" w-[48px] h-[48px] bg-blue-2 rounded-lg flex justify-center items-center ">
        <Icon className=" w-7 h-7 text-white " />
      </div>
      <div className=" space-y-2 ">
        <Heading level={6}>{heading}</Heading>
        <Text>{body} </Text>
      </div>
    </div>
  );
};

export default KeyFeatures;
