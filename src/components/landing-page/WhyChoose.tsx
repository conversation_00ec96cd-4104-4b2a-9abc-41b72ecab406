import React from 'react';
import LandingPageContainer from '../LandingPageContainer';
import Heading from '../lib/typography/Heading';
import Text from '../lib/typography/Text';
import { HiCheck, HiOutlineX } from 'react-icons/hi';
import TrustedBy from './TrustedBy';
import PlanPricing from './PlanPricing';
import Faqs from './Faqs';

const WhyChoose = () => {
  return (
    <LandingPageContainer
      rounded
      className=" relative z-[1] rounded-t-[70px] sm:rounded-t-[144px] lg:rounded-t-[200px] bg-off-white text-black px-4 py-12 sm:p-16 sm:px-10 lg:p-20 "
    >
      <div className=" relative z-[2] space-y-12 sm:space-y-16 lg:space-y-20 ">
        <div className=" space-y-12 md:space-y-16 lg:space-y-20 ">
          <Heading level={3} className=" text-center ">
            Why Choose DesignEasy?
          </Heading>
          <div className="grid grid-cols-[1fr_auto_auto] justify-center items-center justify-items-center rounded-2xl overflow-hidden border-2 sm:border-4 border-blue-2 bg-[#f4f5ff]">
            {/* Header Row */}
            <Heading
              level={6}
              className="bg-blue-2 max-sm:text-sm self-stretch justify-self-stretch text-white py-6 md:py-8 pl-4 md:pl-8"
            >
              Features
            </Heading>
            <Heading
              level={6}
              className="bg-blue-2 max-sm:text-sm self-stretch justify-self-stretch text-center text-white py-6 md:py-8 px-2 md:px-8"
            >
              DesignEasy
            </Heading>
            <Heading
              level={6}
              className="bg-blue-2 max-sm:text-sm self-stretch justify-self-stretch text-center text-white py-6 md:py-8 pr-4 md:pr-8"
            >
              Competitors
            </Heading>

            {/* Feature Rows */}
            <>
              <Text className="justify-self-start pl-2 md:pl-8 pt-6 md:pt-8">
                Ai-Powered Design
              </Text>
              <HiCheck className="text-[24px] text-blue-2" />
              <HiCheck className="text-[24px] text-blue-2" />
            </>
            <>
              <Text className="justify-self-start pl-2 md:pl-8 pt-6">
                Unlimited Designs
              </Text>
              <HiCheck className="text-[24px] text-blue-2" />
              <div className="flex flex-col items-center gap-1.5">
                <HiOutlineX className="text-[24px] text-red-1" />
                <Text level={2}>(Limited Credits)</Text>
              </div>
            </>
            <>
              <Text className="justify-self-start pl-2 md:pl-8 pt-6">
                Human Expertiese Add-On
              </Text>
              <HiCheck className="text-[24px] text-blue-2" />
              <HiOutlineX className="text-[24px] text-red-1" />
            </>
            <>
              <Text className="justify-self-start pl-2 md:pl-8 pt-6 pb-6 md:pb-8">
                All-In-One Subscription
              </Text>
              <HiCheck className="text-[24px] text-blue-2" />
              <div className="flex flex-col items-center gap-1.5">
                <HiOutlineX className="text-[24px] text-red-1" />
                <Text level={2}>(Multiple Plans)</Text>
              </div>
            </>
          </div>
        </div>

        <TrustedBy />

        <PlanPricing />

        <Faqs />
      </div>

      <div className=" absolute inset-0 bg-off-white z-[1] top-full h-[200px]  " />
    </LandingPageContainer>
  );
};

export default WhyChoose;
