import Image from 'next/image';
import React from 'react';
import { IoIosStar } from 'react-icons/io';
import Heading from '../lib/typography/Heading';
import Text from '../lib/typography/Text';

const REVIEW_CARD_DATA = [
  {
    imageUrl: '/images/testimonials-people/akshat.jpg',
    body: 'Designeasy has completely transformed our ad creation process! As a startup, we struggled with designing high-quality banners and videos. Now, in just a few clicks, we get AI-generated creatives that look professional and convert well. Highly recommend it!',
    name: '<PERSON><PERSON><PERSON>',
    position: 'Founder, Pagaar.ai',
    starCount: 5,
  },
  {
    imageUrl: '/images/testimonials-people/dhruvin.jpg',
    body: "We run multiple ad campaigns every month, and Designeasy has saved us so much time and effort. The AI-powered designs are spot-on, and the customization options make it even better. No more waiting for designers – it's a game-changer!",
    name: '<PERSON><PERSON><PERSON><PERSON>',
    position: 'Sr Manager, Sprinklr',
    starCount: 5,
  },
];

const TrustedBy = () => {
  return (
    <div className=" space-y-12 sm:space-y-16 lg:space-y-20 ">
      <Heading level={3} className=" text-center ">
        Trusted by Marketers Across the Global
      </Heading>
      <div className=" grid grid-cols-1 md:grid-cols-2 gap-8 ">
        {REVIEW_CARD_DATA.map((item, index) => (
          <ReviewCard {...item} key={index} />
        ))}
      </div>
    </div>
  );
};

interface IReviewCardProps {
  imageUrl: string;
  body: string;
  name: string;
  position: string;
  starCount: number;
}

function ReviewCard(props: IReviewCardProps) {
  const { imageUrl, body, name, position, starCount } = props;
  return (
    <div className=" border-2 border-[#673de6] rounded-2xl overflow-hidden ">
      <div className=" flex items-end gap-4 bg-[#e3d9fc] p-6 sm:p-8 pb-4 sm:pb-6 ">
        <Image
          src={imageUrl}
          width={64}
          height={64}
          alt={name}
          className=" rounded-full"
        />
        <div>
          <Heading level={6} className=" !font-semibold ">
            {name}
          </Heading>
          <Text>{position}</Text>
        </div>
      </div>
      <div className=" p-6 sm:p-8 pt-4 sm:pt-6 space-y-4 ">
        <div className=" flex gap-3 ">
          {Array(starCount)
            .fill(0)
            .map((_, index) => (
              <IoIosStar size={24} className=" text-blue-2" key={index} />
            ))}
        </div>
        <Text>{body}</Text>
      </div>
    </div>
  );
}

export default TrustedBy;
