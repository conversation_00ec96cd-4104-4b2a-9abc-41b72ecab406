import React from 'react';

interface IBackgroundCircles {
  className?: string;
  colorClassName?: string;
  size?: number;
}

const BackgroundCircles = (props: IBackgroundCircles) => {
  const { size = 969, className, colorClassName = 'bg-blue-2/10' } = props;
  return (
    <div
      className={`absolute ${className}`}
      style={{
        width: size + 'px',
        height: size + 'px',
      }}
    >
      <div
        className={` z-[4] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${colorClassName} rounded-full  `}
        style={{
          width: 0.6 * size + 'px',
          height: 0.6 * size + 'px',
        }}
      />
      <div
        className={` z-[3] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${colorClassName} rounded-full  `}
        style={{
          width: 0.74 * size + 'px',
          height: 0.74 * size + 'px',
        }}
      />
      <div
        className={` z-[2] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${colorClassName} rounded-full  `}
        style={{
          width: 0.87 * size + 'px',
          height: 0.87 * size + 'px',
        }}
      />
      <div
        className={` z-[1] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${colorClassName} rounded-full  `}
        style={{
          width: 1 * size + 'px',
          height: 1 * size + 'px',
        }}
      />
    </div>
  );
};

export default BackgroundCircles;
