'use client';

import React, { useState } from 'react';
import Heading from '../lib/typography/Heading';
import Text from '../lib/typography/Text';

const data = [
  {
    heading: 'What is Designeasy?',
    body: 'Designeasy is an AI-powered ad creation tool that helps businesses design stunning banners, videos, and carousels effortlessly. It combines AI automation with human expertise to generate high-performing ad creatives.',
  },
  {
    heading: 'How does Designeasy work?',
    body: 'Simply provide your brand details, choose your ad format (banner, video, or carousel), and let Designeasy generate multiple creative options. You can then customize or download them instantly.',
  },
  {
    heading: 'Is Designeasy free to use?',
    body: 'Yes! You can get started for free and create a few ads. For unlimited designs and premium features, you can upgrade to a paid plan.',
  },
  {
    heading: 'What types of ads can I create with Designeasy?',
    body: 'You can create banners (static & animated display ads), videos (short ad creatives for social media), and carousels (engaging multi-image ads for platforms like Facebook & Instagram).',
  },
  {
    heading: 'Can I customize the generated ads?',
    body: 'Absolutely! You can tweak colors, text, images, and branding elements to match your brand identity.',
  },
  {
    heading: 'Does Designeasy support different ad formats and sizes?',
    body: 'Yes, Designeasy supports multiple ad formats and platform-specific dimensions, including Google Display Ads, Facebook Ads, Instagram, LinkedIn, and more.',
  },
  {
    heading: 'Can I download my ads in different file formats?',
    body: 'Yes, you can download ads in PNG, JPG, and MP4 (for videos).',
  },
  {
    heading: 'How is Designeasy different from other ad design tools?',
    body: 'Unlike traditional design tools, Designeasy automates the creative process using AI, providing unlimited design variations every month without needing a designer.',
  },
  {
    heading: 'Do I need design skills to use Designeasy?',
    body: 'No! Designeasy is built for marketers, business owners, and agencies—no design experience is required. Just enter your details, and AI will do the rest.',
  },
  {
    heading: 'Can I use Designeasy for multiple brands or clients?',
    body: 'Yes! Designeasy is perfect for agencies and freelancers who need to create high-quality ads for multiple brands.',
  },
  {
    heading: 'How do I get started?',
    body: 'Simply sign up for free and start creating ads instantly. No credit card is required to explore the platform.',
  },
  {
    heading: 'How do I contact support?',
    body: 'If you need help, you can reach out to our support <NAME_EMAIL> or visit our help center.',
  },
];

const Faqs = () => {
  return (
    <div className=" space-y-12 sm:space-y-16 lg:space-y-20 ">
      <Heading level={3} className="text-center">
        FAQs
      </Heading>

      <div className=" space-y-6 sm:space-y-8  ">
        {data.map((d, index) => (
          <Card heading={d.heading} body={d.body} key={index} />
        ))}
      </div>
    </div>
  );
};

const Card = ({ heading, body }: { heading: string; body: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div
      className={` group flex flex-col py-6 px-6 cursor-pointer backdrop-blur-sm bg-[#e3d9fc] rounded-lg transition-all duration-500 overflow-hidden `}
      onClick={() => setIsOpen(!isOpen)}
    >
      <div className="flex justify-between items-center gap-4 ">
        <Heading
          level={6}
          className={` !font-medium select-none transition-all duration-500 `}
        >
          {heading}
        </Heading>

        <div className=" relative w-4 h-4 flex-shrink-0 ">
          <div className=" absolute top-1/2 translate-y-1/2 left-0 w-full border-b-2 border-black " />
          <div
            className={`  absolute top-1/2 translate-y-1/2 left-0 w-full border-b-2 border-black transition-all duration-500 ${
              isOpen ? 'rotate-0' : 'rotate-90'
            }`}
          />
        </div>
      </div>
      <div
        className={`${
          isOpen ? 'max-h-[500px] ' : 'max-h-0  '
        } transition-all duration-500 `}
      >
        <div className=" h-5 " />
        <Text>{body}</Text>
      </div>
    </div>
  );
};

export default Faqs;
