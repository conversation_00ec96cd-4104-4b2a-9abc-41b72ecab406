'use client';

import React, { useEffect } from 'react';

const ShowHideCTA = ({ children }: Readonly<{ children: React.ReactNode }>) => {
  useEffect(() => {
    const element = document.getElementById(
      'landing-page-main-button',
    ) as HTMLDivElement | null;

    if (!element) return;

    // setup intersection observer
    const observer = new IntersectionObserver(
      ([entry]) => {
        const cssVariableValue = entry.isIntersecting ? '100%' : '0';
        document.documentElement.style.setProperty(
          '--translate-y',
          cssVariableValue,
        );
      },
      { threshold: 0 },
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, []);
  return <>{children}</>;
};

export default ShowHideCTA;
