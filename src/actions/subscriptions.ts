import { ISubscriptionPlan, IUserSubscription } from '@/types/subscription';
import { API_ENDPOINTS, fetchPostRequest, fetchRequest } from '.';

export const getAllSubscriptionPlans = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
}): Promise<{
  data: ISubscriptionPlan[];
}> => {
  const url = new URL(API_ENDPOINTS.GET_SUBSCRIPTION_PLAN);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: ISubscriptionPlan[];
  }>;
  return response;
};

export const createNewSubscriptionPlan = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: ISubscriptionPlan;
}): Promise<{
  data: ISubscriptionPlan;
}> => {
  const url = new URL(API_ENDPOINTS.UPDATE_SUBSCRIPTION_PLAN);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: ISubscriptionPlan;
  }>;
  return response;
};

export const updateSubscriptionPlan = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: ISubscriptionPlan;
}): Promise<{
  data: ISubscriptionPlan;
}> => {
  const url = new URL(API_ENDPOINTS.UPDATE_SUBSCRIPTION_PLAN + `/${data.id}`);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: ISubscriptionPlan;
  }>;
  return response;
};

export const createStripeCheckoutSession = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: { price_id: string; success_url: string; cancel_url: string };
}): Promise<{
  data: {
    session_id: string;
    url: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.STRIPE_CHECKOUT_SESSION);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      session_id: string;
      url: string;
    };
  }>;
  return response;
};

export const verifyStripePayment = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: { session_id: string };
}): Promise<{
  data: { message: string };
}> => {
  const url = new URL(API_ENDPOINTS.STRIPE_VERIFY_CHECKOUT);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      message: string;
    };
  }>;
  return response;
};

export const getUserSubscription = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
}): Promise<{
  data: IUserSubscription | undefined;
}> => {
  const url = new URL(API_ENDPOINTS.USER_SUBSCRIPTION);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IUserSubscription | undefined;
  }>;
  return response;
};

export const cancelUserSubscription = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: { subscription_id: string };
}): Promise<{
  data: {
    message: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.CANCEL_USER_SUBSCRIPTION);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      message: string;
    };
  }>;
  return response;
};

export const reactivateUserSubscription = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: { subscription_id: string };
}): Promise<{
  data: {
    message: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.REACTIVATE_USER_SUBSCRIPTION);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      message: string;
    };
  }>;
  return response;
};

export const redeemCouponCode = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: { coupon_code: string };
}): Promise<{
  data: {
    message: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.REDEEM_COUPON_CODE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      message: string;
    };
  }>;
  return response;
};
