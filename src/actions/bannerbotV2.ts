import { IBannerImage, IBannerTemplate } from '@/types/banner_templates';
import {
  API_ENDPOINTS,
  fetchFormPostRequest,
  fetchPostRequest,
  fetchRequest,
} from '.';

export const updateProject = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: Partial<IBannerbotProject>;
}): Promise<{
  data: IBannerbotProject;
}> => {
  const url = new URL(API_ENDPOINTS.PROJECTS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IBannerbotProject;
  }>;
  return response;
};

export const getProjects = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
}): Promise<{
  data: Array<IBannerbotProject>;
}> => {
  const url = new URL(API_ENDPOINTS.PROJECTS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: Array<IBannerbotProject>;
  }>;
  return response;
};

export const getProjectsForAdmin = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
}): Promise<{
  data: {
    data: Array<{
      userDetail: {
        uid: string;
        name: string;
        email: string;
      };
      project: IBannerbotProject;
    }>;

    last_cursor_id: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.ADMIN_PROJECTS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: {
      data: Array<{
        userDetail: {
          uid: string;
          name: string;
          email: string;
        };
        project: IBannerbotProject;
      }>;

      last_cursor_id: string;
    };
  }>;
  return response;
};

export const getAiAdBannersForAdmin = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
}): Promise<{
  data: {
    data: Array<{
      user_details: {
        uid: string;
        name: string;
        email: string;
      };
      ai_banner: IAiAdBanner;
    }>;
    last_cursor_id: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.ADMIN_AI_AD_BANNERS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: {
      data: Array<{
        user_details: {
          uid: string;
          name: string;
          email: string;
        };
        ai_banner: IAiAdBanner;
      }>;
      last_cursor_id: string;
    };
  }>;
  return response;
};

export const getProject = async ({
  headers,
  queryParams, // add a key 'id'
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: Array<IBannerbotProject>;
}> => {
  const url = new URL(
    API_ENDPOINTS.PROJECT.replace(':project_id', queryParams['id']),
  );
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: Array<IBannerbotProject>;
  }>;
  return response;
};

export const uploadFileAndGetS3Url = async ({
  headers = {},
  queryParams = {},
  data,
}: {
  headers?: Record<string, string>;
  queryParams?: Record<string, string>;
  data: {
    file: File;
  };
}): Promise<{
  data: {
    s3_url: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.S3_ASSET_URL);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const formData = new FormData();
  formData.append('file', data.file);
  const response = (await fetchFormPostRequest({
    url: url.toString(),
    headers,
    body: formData,
  })) as Promise<{
    data: {
      s3_url: string;
    };
  }>;
  return response;
};

export const getAiKeyBenefits = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: {
    id: string;
    details: { business_details: IBannerbotBusinessDetails };
  };
}): Promise<{
  data: { key_benefits: string[] };
}> => {
  const url = new URL(API_ENDPOINTS.GET_AI_KEY_BENEFITS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: { key_benefits: string[] };
  }>;
  return response;
};

export const getBannersV2 = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: Partial<IBannerbotProject>;
}): Promise<{
  data: {
    templates: IBannerTemplate[];
    images: IBannerImage[];
  };
}> => {
  const url = new URL(API_ENDPOINTS.BANNERSV2);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      templates: IBannerTemplate[];
      images: IBannerImage[];
    };
  }>;
  return response;
};

export const getBusinessDetailsFromWebsite = async ({
  headers,
  queryParams, // add a key 'website'
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    business_name: string;
    business_square_logo_url: string;
    business_logo_url: string;
    business_description: string;
    business_usp: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.BUSINESS_DETAILS_FROM_WEBSITE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: {
      business_name: string;
      business_square_logo_url: string;
      business_logo_url: string;
      business_description: string;
      business_usp: string;
    };
  }>;
  return response;
};

export const sendWelcomeEmail = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
}): Promise<{
  data: object;
}> => {
  const url = new URL(API_ENDPOINTS.SEND_WELCOME_EMAIL);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: object;
  }>;
  return response;
};

export const createOrUpdateUserProfile = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: Partial<IUserProfile>;
}): Promise<{
  data: IUserProfile;
}> => {
  const url = new URL(API_ENDPOINTS.USERS_PROFILE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IUserProfile;
  }>;
  return response;
};

export const generateAiAdBanner = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams?: Record<string, string>;
  data: { project_id: string; size: string };
}): Promise<{
  data: { height: number; s3_url: string; width: number };
}> => {
  const url = new URL(API_ENDPOINTS.GENERATE_AI_AD_BANNER);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: { height: number; s3_url: string; width: number };
  }>;
  return response;
};
