import FetchError from './FetchError';

const BASE_URL = 'https://groweasy.ai/api/bannerbot';
// const BASE_URL = 'http://localhost:4000/api/bannerbot';

export const API_ENDPOINTS = {
  BANNERS:
    'https://zxlfebn5bmjewfy2dp5cihqs7m0azmlb.lambda-url.ap-south-1.on.aws/',
  UNSPLASH_IMAGE_DOWNLOAD:
    'https://r4bk4wz2luj7kn46mxlidwab640vuygf.lambda-url.ap-south-1.on.aws/',
  VIDEO_DATA: `${BASE_URL}/video-data`,
  PROJECTS: `${BASE_URL}/v2/projects`,
  ADMIN_PROJECTS: `${BASE_URL}/v2/admin/projects`,
  PROJECT: `${BASE_URL}/v2/projects/:project_id`,
  S3_ASSET_URL: `${BASE_URL}/v2/s3-asset-url`,
  GET_AI_KEY_BENEFITS: `${BASE_URL}/v2/populate-key-benefits`,
  BANNERSV2: `${BASE_URL}/v2/ad-banners`,
  BUSINESS_DETAILS_FROM_WEBSITE: `${BASE_URL}/v2/business-details-from-website`,
  SEND_WELCOME_EMAIL: BASE_URL + '/v2/send-welcome-email',
  USERS_PROFILE: BASE_URL + '/v2/profile',
  FP_VIDEO_DATA: BASE_URL + '/v2/video-data',
  SEARCH_VIDEOS: BASE_URL + '/v2/search/videos',
  SEARCH_IMAGES: BASE_URL + '/v2/search/images',
  GENERATE_FP_VIDEO: BASE_URL + '/v2/fp-video',
  GET_SUBSCRIPTION_PLAN: BASE_URL + '/v2/subscription-plan',
  UPDATE_SUBSCRIPTION_PLAN: BASE_URL + '/v2/admin/subscription-plan',
  STRIPE_CHECKOUT_SESSION: BASE_URL + '/v2/create-checkout-session',
  STRIPE_VERIFY_CHECKOUT: BASE_URL + '/v2/verify-checkout',
  USER_SUBSCRIPTION: BASE_URL + '/v2/user-subscription',
  CANCEL_USER_SUBSCRIPTION: BASE_URL + '/v2/cancel-user-subscription',
  REACTIVATE_USER_SUBSCRIPTION: BASE_URL + '/v2/reactivate-user-subscription',
  GENERATE_AI_AD_BANNER: BASE_URL + '/v2/generate-ai-ad-banner',
  ADMIN_AI_AD_BANNERS: `${BASE_URL}/v2/admin/ai-ad-banners`,
  REDEEM_COUPON_CODE: BASE_URL + '/v2/coupons/redeem',
};

export const getCommonHeaders = (user?: IBannerbotUser) => {
  return {
    Authorization: `Bearer ${user?.authToken}`,
    'Content-Type': 'application/json',
  };
};

export const fetchRequest = async ({
  url,
  headers,
}: {
  url: string;
  headers: Record<string, string>;
}) => {
  const response = await fetch(url, {
    headers,
  });
  let data: object | null = null;
  try {
    data = (await response.json()) as Promise<object>;
  } catch {
    // ignore
  }
  if (response.ok) {
    return data;
  } else {
    throw new FetchError(response.status, data as Record<string, unknown>);
  }
};

export const fetchPostRequest = async ({
  url,
  headers,
  body,
}: {
  url: string;
  headers: Record<string, string>;
  body: object;
}) => {
  const response = await fetch(url, {
    headers,
    body: JSON.stringify(body),
    method: 'POST',
  });
  let data: object | null = null;
  try {
    data = (await response.json()) as Promise<object>;
  } catch {
    // ignore
  }
  if (response.ok) {
    return data;
  } else {
    throw new FetchError(response.status, data as Record<string, unknown>);
  }
};

export const fetchFormPostRequest = async ({
  url,
  headers,
  body,
}: {
  url: string;
  headers: Record<string, string>;
  body: FormData;
}) => {
  // form data will make it multipart/form-data
  delete headers['Content-Type'];
  const response = await fetch(url, {
    headers,
    body,
    method: 'POST',
  });
  let data: object | null = null;
  try {
    data = (await response.json()) as Promise<object>;
  } catch {
    // ignore
  }
  if (response.ok) {
    return data;
  } else {
    throw new FetchError(response.status, data as Record<string, unknown>);
  }
};
