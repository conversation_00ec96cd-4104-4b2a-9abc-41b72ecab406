class FetchError extends Error {
  statusCode: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  response: Record<string, any>;

  constructor(statusCode: number, response: Record<string, unknown>) {
    super(`Request failed with status code ${statusCode}`); // Call super with the error message
    this.statusCode = statusCode;
    this.name = 'FetchError'; // Set the name property for better identification
    this.response = response;
    Object.setPrototypeOf(this, FetchError.prototype); // Ensure prototype chain is correctly set
  }
}

export default FetchError;
