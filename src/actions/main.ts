import { IVideoData } from '@/types/video';
import { API_ENDPOINTS, fetchPostRequest, fetchRequest } from '.';
import { IBannerImage, IBannerTemplate } from '../types/banner_templates';

export const getBanners = async ({
  headers,
  queryParams = {},
  query,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  query: string;
}): Promise<{
  banners: IBannerTemplate[];
  images: IBannerImage[];
}> => {
  const url = new URL(API_ENDPOINTS.BANNERS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {
      query,
    },
  })) as Promise<{
    banners: IBannerTemplate[];
    images: IBannerImage[];
  }>;
  return response;
};

export const triggerUnsplashImageDownload = async (
  queryParams: Record<string, string> = {},
) => {
  const url = new URL(API_ENDPOINTS.UNSPLASH_IMAGE_DOWNLOAD);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers: {},
  })) as Promise<{
    banners: IBannerTemplate[];
    images: IBannerImage[];
  }>;
  return response;
};

export const getVideoData = async ({
  headers,
  queryParams = {},
  query,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  query: string;
}): Promise<{
  data: IVideoData;
}> => {
  const url = new URL(API_ENDPOINTS.VIDEO_DATA);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {
      query,
    },
  })) as Promise<{
    data: IVideoData;
  }>;
  return response;
};
