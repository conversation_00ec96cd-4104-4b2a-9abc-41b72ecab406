import { IFpVideoData } from '@/remotion/types';
import { API_ENDPOINTS, fetchPostRequest, fetchRequest } from '.';

export const getFpVideoData = async ({
  headers,
  queryParams = {}, // needed project_id and template_id
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IFpVideoData;
}> => {
  const url = new URL(API_ENDPOINTS.FP_VIDEO_DATA);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IFpVideoData;
  }>;
  return response;
};

export const generateFpVideo = async ({
  headers = {},
  queryParams = {},
  data,
}: {
  headers?: Record<string, string>;
  queryParams?: Record<string, string>;
  data: {
    project_id: string;
    template_data: IFpVideoData;
  };
}): Promise<{
  data: {
    id: string;
    url: string;
    width: number;
    height: number;
    template_id: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.GENERATE_FP_VIDEO);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      id: string;
      url: string;
      width: number;
      height: number;
      template_id: string;
    };
  }>;
  return response;
};

export const searchVideos = async ({
  headers,
  queryParams = {}, // needed query
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IPexelsVideoData[];
}> => {
  const url = new URL(API_ENDPOINTS.SEARCH_VIDEOS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IPexelsVideoData[];
  }>;
  return response;
};

export const searchImages = async ({
  headers,
  queryParams = {}, // needed query
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IImageData[];
}> => {
  const url = new URL(API_ENDPOINTS.SEARCH_IMAGES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IImageData[];
  }>;
  return response;
};
