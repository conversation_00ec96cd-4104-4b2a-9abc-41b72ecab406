'use client';

import { getCommonHeaders } from '@/actions';
import {
  createStripeCheckoutSession,
  getAllSubscriptionPlans,
} from '@/actions/subscriptions';
import { useUser } from '@/components/context/UserProvider';
import Button from '@/components/lib/Button';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import HeadingV2 from '@/components/lib/typography/HeadingV2';
import TextV2 from '@/components/lib/typography/TextV2';
import RedeemCouponDialog from '@/components/RedeemCouponDialog';
import { ISubscriptionPlan } from '@/types/subscription';
import { openUrlInNewTab } from '@/utils';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { FaCheck } from 'react-icons/fa6';
import { IoGiftOutline } from 'react-icons/io5';
import { useMutation, useQuery } from 'react-query';

const BASE_URL =
  process.env.NODE_ENV === 'production'
    ? 'https://designeasy.ai'
    : 'http://localhost:3000';

const FREE_PLAN: ISubscriptionPlan = {
  active: true,
  name: 'Free',
  description: 'Gets you started with basic features to explore the platform.',
  line_items: [
    'Unlimited templatized banners',
    '1 AI banners per day',
    '1 templatized videos per day',
  ],
  id: 'free',
  period: 'month',
  price_in_usd: 0,
  stripe_price_id: '1',
  features: {},
  stripe_product_id: '1',
};

const SubscriptionPage = () => {
  const { user } = useUser();
  const [showRedeemDialog, setShowRedeemDialog] = useState(false);

  const router = useRouter();

  const getAllSubscriptionsQuery = useQuery(['getAllSubscriptionPlans'], () => {
    return getAllSubscriptionPlans({
      headers: getCommonHeaders(user),
      queryParams: {
        active: 'true',
      },
    });
  });

  const createStripeCheckoutSessionMutation = useMutation(
    createStripeCheckoutSession,
  );

  const handleClick = (priceId: string) => {
    //create checkout session and redirect
    createStripeCheckoutSessionMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        data: {
          price_id: priceId,
          success_url: BASE_URL + '/subscription/success',
          cancel_url: BASE_URL + '/subscription',
        },
      })
      .then((data) => {
        const sessionUrl = data.data.url;
        openUrlInNewTab(sessionUrl);
      });
  };

  const { data, isLoading } = getAllSubscriptionsQuery;

  return (
    <div className=" bg-blue-1 max-w-5xl mx-auto ">
      <div className=" space-y-2 text-center mt-8 max-w-3xl mx-auto ">
        <HeadingV2 level={1} className=" capitalize !font-semibold ">
          create more, limit less
        </HeadingV2>
        <TextV2 level={3} className=" text-gray-300 ">
          The Free plan gets you started. The Pro plan takes your creativity to
          the next level — with more features, fewer limits, and full support.
        </TextV2>
      </div>

      <main className=" grid grid-cols-1 sm:grid-cols-2 gap-8 mt-10 md:mt-12 ">
        <FreePlanCard
          {...FREE_PLAN}
          handleClick={() => router.push('/dashboard')}
        />
        {data?.data?.map((plan) => (
          <PlanCard
            {...plan}
            key={plan.id}
            handleClick={() => handleClick(plan.stripe_price_id)}
          />
        ))}
      </main>

      <div className=" mt-6 flex justify-center ">
        <Button
          variant={'outline'}
          className=" max-sm:w-full hover:!bg-blue-300/60 "
          onClick={() => setShowRedeemDialog(true)}
        >
          <IoGiftOutline /> Redeem Coupon
        </Button>
      </div>

      <RedeemCouponDialog
        isOpen={showRedeemDialog}
        setIsOpen={setShowRedeemDialog}
      />
      {isLoading && <FullScreenLoader />}
    </div>
  );
};

const FreePlanCard = (
  props: ISubscriptionPlan & { handleClick: () => void },
) => {
  const { name, line_items, description, handleClick } = props;

  return (
    <div className=" bg-black/5 bg-opacity-40 backdrop-blur-sm border border-gray-800 hover:border-gray-700 flex flex-col justify-between p-6 md:p-8 rounded-xl transition-all duration-300 space-y-4 ">
      <div className=" space-y-1 ">
        <TextV2 level={2} className=" font-semibold capitalize ">
          {name}
        </TextV2>
        <TextV2 level={4} className=" text-gray-300 ">
          {description}
        </TextV2>
      </div>
      <p className=" text-4xl font-bold tracking-wide ">Free</p>
      <div className=" flex flex-col gap-1 mt-6 ">
        {line_items.map((item) => (
          <div className=" flex gap-3 items-center " key={item}>
            <FaCheck size={20} className=" text-green-1 " />
            <p>{item}</p>
          </div>
        ))}
      </div>
      <Button
        className=" w-full mt-6 bg-gray-800/90 border border-gray-500 hover:bg-gray-700 "
        variant={'custom'}
        onClick={handleClick}
      >
        Choose Plan
      </Button>
    </div>
  );
};

const PlanCard = (props: ISubscriptionPlan & { handleClick: () => void }) => {
  const { name, price_in_usd, line_items, period, description, handleClick } =
    props;
  return (
    <div className=" bg-gradient-to-br from-purple-800 to-purple-900/80 border border-purple-700 shadow-lg transform hover:-translate-y-1 flex flex-col p-6 md:p-8 rounded-xl transition-all duration-300 space-y-4 ">
      <div className=" space-y-1 ">
        <TextV2 level={2} className=" font-semibold capitalize ">
          {name}
        </TextV2>
        <TextV2 level={4} className=" text-gray-300 ">
          {description}
        </TextV2>
      </div>
      <p className=" text-4xl font-bold tracking-wide ">
        ${price_in_usd}
        <span className="text-gray-400 text-base font-normal"> /{period}</span>
      </p>
      <div className=" flex flex-col gap-1 mt-6 ">
        {line_items.map((item) => (
          <div className=" flex gap-3 items-center " key={item}>
            <FaCheck size={20} className=" text-green-1 " />
            <p>{item}</p>
          </div>
        ))}
      </div>
      <Button
        className=" w-full mt-6 "
        variant={name === 'starter' ? 'secondary' : 'default'}
        onClick={handleClick}
      >
        Choose Plan
      </Button>
    </div>
  );
};

export default SubscriptionPage;
