'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { FaRegCheckCircle } from 'react-icons/fa';
import { FiXCircle } from 'react-icons/fi';
import Button from '@/components/lib/Button';
import Link from 'next/link';
import { verifyStripePayment } from '@/actions/subscriptions';
import { getCommonHeaders } from '@/actions';
import { useUser } from '@/components/context/UserProvider';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { showToastMessage } from '@/modules/toast';

const Page = () => {
  const { user } = useUser();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [verificationStatus, setVerificationStatus] = useState('loading');
  const router = useRouter();
  const [redirectTime, setRedirectTime] = useState(3);

  useEffect(() => {
    if (!sessionId) {
      router.replace('/dashboard');
    } else {
      // verify the payment
      verifyStripePayment({
        headers: getCommonHeaders(user),
        data: { session_id: sessionId },
      })
        .then((data) => {
          setVerificationStatus('complete');
          showToastMessage(data.data.message, 'success');
          const intervalId = setInterval(() => {
            setRedirectTime((prev) => {
              if (prev === 1) {
                clearInterval(intervalId);
                router.push('/dashboard');
              }
              return prev - 1;
            });
          }, 1000);
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error,
            'success.verifyStripePaymentMutation',
          );
          setVerificationStatus('error');
        });
    }
  }, [sessionId, router, user]);

  return (
    <div className=" flex-1 bg-blue-1 text-gray-50 flex flex-col items-center justify-center ">
      <div className="max-w-md w-full">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center animate-fade-in">
          {verificationStatus === 'loading' && (
            <div className="space-y-4 flex flex-col items-center">
              <SpinnerLoader className=" !border-t-blue-400 " />
              <h2 className="text-2xl font-bold text-white">
                Verifying Payment
              </h2>
              <p className="text-gray-300">
                Please wait while we confirm your payment...
              </p>
            </div>
          )}

          {verificationStatus === 'complete' && (
            <div className="space-y-4 animate-slide-up">
              <FaRegCheckCircle className="w-16 h-16 text-green-400 mx-auto animate-bounce-slow" />
              <h2 className="text-2xl font-bold text-white">
                Payment Successful!
              </h2>
              <p className="text-gray-300">
                Thank you for your purchase. Your payment has been confirmed.
              </p>
              <p className=" text-xs text-gray-300 ">
                Redirecting in {redirectTime}
              </p>
              <Link href={'/'}>
                <Button className=" mt-2 ">Return Home</Button>
              </Link>
            </div>
          )}

          {verificationStatus === 'error' && (
            <div className="space-y-4 animate-slide-up">
              <FiXCircle className="w-16 h-16 text-red-400 mx-auto" />
              <h2 className="text-2xl font-bold text-white">
                Verification Failed
              </h2>
              <p className="text-gray-300">
                We couldn&apos;t verify your payment. Please contact support.
              </p>
              <Link href={'/'}>
                <Button className=" mt-2 ">Return Home</Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Page;
