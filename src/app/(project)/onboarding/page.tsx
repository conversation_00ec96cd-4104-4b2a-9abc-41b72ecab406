'use client';

import { useOnboarding } from '@/components/context/OnboardingProvider';
import AddProductDetails from '@/components/onboarding/AddProductDetails';
import ChooseVideoTemplate from '@/components/onboarding/ChooseVideoTemplate';
import DesignProjects from '@/components/onboarding/DesignProjects';
import EditVideoData from '@/components/onboarding/EditVideoData';
import EnterBrandDetails from '@/components/onboarding/EnterBrandDetails';
import SelectKeyBenefits from '@/components/onboarding/SelectKeyBenefits';
import SelectProjectType from '@/components/onboarding/SelectProjectTypeComp';
import SelectTypeOfAd from '@/components/onboarding/SelectTypeOfAd';
import { OnboardingStepIds } from '@/types/onBoarding';
import React from 'react';

const Onboarding = () => {
  const { stepId } = useOnboarding();

  switch (stepId) {
    case OnboardingStepIds.SELECT_PROJECT_TYPE:
      return <SelectProjectType />;
    case OnboardingStepIds.ADD_PRODUCT_DETAILS:
      return <AddProductDetails />;
    case OnboardingStepIds.SELECT_KEY_BENEFITS:
      return <SelectKeyBenefits />;
    case OnboardingStepIds.ENTER_BRAND_DETAILS:
      return <EnterBrandDetails />;
    case OnboardingStepIds.SELECT_TYPE_OF_AD:
      return <SelectTypeOfAd />;
    case OnboardingStepIds.CHOOSE_VIDEO_TEMPLATE:
      return <ChooseVideoTemplate />;
    case OnboardingStepIds.EDIT_VIDEO_DATA:
      return <EditVideoData />;
  }
  return (
    <div>
      <DesignProjects />
    </div>
  );
};

export default Onboarding;
