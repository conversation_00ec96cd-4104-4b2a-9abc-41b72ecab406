'use client';

import Header from '@/components/common/Header';
import { useRouter } from 'next/navigation';
import React from 'react';
import { FaChevronLeft } from 'react-icons/fa6';

const OnboardingLayout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();

  return (
    <>
      <div className=" max-w-[1440px] mx-auto px-7 md:px-9 pt-7 md:pt-8 ">
        {/* add a back button */}
        <div className=" flex gap-4 md:gap-8 items-center ">
          <FaChevronLeft
            size={24}
            className=" max-md:w-5 max-md:h-5 cursor-pointer "
            onClick={() => router.back()}
          />
          <div className=" flex-1 ">
            <Header />
          </div>
        </div>
      </div>
      <main className=" max-w-[1440px] mx-auto px-4 md:px-6 pt-7 sm:pt-10 pb-4 sm:pb-8  ">
        {children}
      </main>
    </>
  );
};

export default OnboardingLayout;
