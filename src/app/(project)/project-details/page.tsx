'use client';

import ProjectBanners from '@/components/project-details/ProjectBanners';

import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

const Page = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const projectId = searchParams.get('id');

  useEffect(() => {
    if (!projectId) {
      router.replace('/dashboard');
    }
  }, [projectId, router]);

  return (
    <div>
      <ProjectBanners />
    </div>
  );
};

export default Page;
