import VideoMaker from '@/components/main/VideoMaker';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

export const metadata: Metadata = {
  title: 'Free AI Video Maker',
  description:
    'Create social media and Ad videos effortlessly with DesignEasy. Leverage AI to produce high-quality, engaging videos in minutes. No login required and perfect for businesses & content creators.',
  alternates: {
    canonical: 'https://designeasy.ai/video-maker',
  },
};

const VideoMakerComp = () => {
  return (
    <div className="h-screen w-full">
      <section className="w-full min-h-full bg-gradient-to-b from-cyan-500 to-blue-500 p-8 flex flex-col items-center border-1 border-b border-slate-300">
        <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
          <Image
            src="/images/bannerbot-logo.png"
            width="80"
            height="80"
            alt="bannerbot-logo"
            priority
          />
        </div>
        <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
          <p className="text-white text-3xl text-center">DesignEasy </p>
          <h1 className="text-white text-xl mt-4 text-center">
            AI Video Maker
          </h1>
          <VideoMaker />
        </div>
      </section>
      <section className="w-full bg-gradient-to-b from-blue-500 to-violet-500 p-8 flex flex-col items-center border-1 border-b border-slate-300">
        <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
          <h1 className="text-white text-4xl mt-4 text-center">About Us</h1>
          <p className="text-white p-8 text-center">
            We are the same team behind{' '}
            <Link
              href="https://groweasy.ai/"
              target="_blank"
              className="underline"
            >
              GrowEasy
            </Link>
          </p>
          <p className="text-white text-center">
            GrowEasy is an AI-powered lead generation platform that assists
            small businesses in acquiring leads through marketing campaigns on
            Facebook and Instagram.
          </p>
          <p className="text-white text-center mt-8">
            Contact Us: <span className="font-medium"><EMAIL></span>,{' '}
            <span className="font-medium"><EMAIL></span>
          </p>
        </div>
      </section>
      <section className="w-full bg-slate-800 p-8 flex flex-col justify-center items-center">
        <p className="text-white p-8 text-center">
          With DesignEasy, you have the freedom to create your own videos
          effortlessly. Boost your social media presence with captivating videos
          made using our tool.
        </p>
        <p className="text-white p-8 text-center">
          All Rights Reserved | © Copyright 2023 | &nbsp;
          <Link href="/privacy-policy" className="underline">
            Privacy Policy
          </Link>
        </p>
      </section>
    </div>
  );
};

export default VideoMakerComp;
