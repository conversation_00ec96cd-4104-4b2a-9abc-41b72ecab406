@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@theme {
  --color-primary: #294744;
  --color-off-white: #f8f8f8;
  --color-gray-dark: #646464;
  --color-gray-medium: #adadad;
  --color-gray-light: #eaedec;
  --color-hyperlink: #0070cc;
  --color-red-light: #ec45451a;
  --color-green-light: #11d33e1a;
  --color-green-dark: #294730;
  --color-yellow-light: #ffae141a;
  --color-yellow-dark: #ad7a18;
  --color-primary-light: #e2f1f1;

  --color-blue-1: #1f1346;
  --color-blue-2: #673de6;
  --color-red-1: #db2777;
  --color-red-2: #fc5185;
  --color-gray-1: #767676;
  --color-gray-2: #bfbfbf;
  --color-green-1: #54e63d;
}

:root {
  --translate-y: 100%;
}

html {
  -webkit-tap-highlight-color: transparent;
}

.no-scrollbar {
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  -webkit-appearance: none;
}

.new-scrollbar::-webkit-scrollbar {
  width: 5px;
}

.new-scrollbar::-webkit-scrollbar-thumb {
  background-color: #7d7d7d7a;
  border-radius: 15px;
}

@keyframes ModalB2T {
  0% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.animate-ModalB2T {
  -webkit-animation: ModalB2T 0.5s forwards;
  animation: ModalB2T 0.5s forwards;
}

@keyframes preLoader {
  50% {
    background-size: 80%;
  }

  100% {
    background-position: 125% 0;
  }
}

.animate-preLoader {
  -webkit-animation: preLoader 1.2s ease-in-out infinite;
  animation: preLoader 1.2s ease-in-out infinite;
}

.custom-checkbox-container {
  @apply relative pl-12 cursor-pointer select-none;
}

.custom-checkbox-container > input {
  @apply absolute h-0 w-0 opacity-0;
}

.custom-checkbox-container > .custom-checkmark {
  @apply absolute top-0 left-0 w-7 h-7 border-2 border-white bg-transparent rounded-sm;
}

.custom-checkmark::before {
  /* content: ''; */
  @apply absolute left-0 top-0 translate-x-[9.5px] translate-y-[12.5px] rounded-t-full rounded-bl-full -rotate-45 origin-center h-[7px] w-[2px] bg-blue-1;
}

.custom-checkmark::after {
  /* content: ''; */
  @apply absolute left-0 top-0 translate-x-[15px] translate-y-[8px] rounded-t-full rounded-br-full rotate-45 origin-center h-[12px] w-[2px] bg-blue-1;
}

.custom-checkbox-container:hover > .custom-checkmark {
  @apply bg-white/10;
}

.custom-checkbox-container > input:checked ~ .custom-checkmark::after,
.custom-checkbox-container > input:checked ~ .custom-checkmark::before {
  content: '';
}

.custom-checkbox-container > input:checked ~ .custom-checkmark {
  @apply bg-white border-none;
}

@keyframes zoomBounce {
  0% {
    transform: scale(0);
  }
  70% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1);
  }
}
