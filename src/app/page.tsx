import React from 'react';
import LandingPageContainer from '@/components/LandingPageContainer';
import Heading from '@/components/lib/typography/Heading';
import Text from '@/components/lib/typography/Text';
import Image from 'next/image';
import FacebookIcon from '@/images/icons/facebook.svg';
import GoogleIcon from '@/images/icons/google.svg';
import TicktockIcon from '@/images/icons/ticktock.svg';
import YoutubeIcon from '@/images/icons/youtube.svg';
import KeyFeatures from '@/components/landing-page/KeyFeatures';
import HowItWorks from '@/components/landing-page/HowItWorks';
import WhyChoose from '@/components/landing-page/WhyChoose';
import StartCreatingAds from '@/components/landing-page/StartCreatingAds';
import Link from 'next/link';
import Footer from '@/components/common/Footer';
import Logo from '@/images/logo/logo-white.svg';
import Title from '@/images/logo/title.svg';
import ShowHideCTA from '@/components/landing-page/ShowHideCTA';

function Home() {
  return (
    <ShowHideCTA>
      <div className=" bg-blue-1 text-white ">
        <LandingPageContainer className=" p-4 sm:p-6 sm:px-10 lg:px-20 ">
          <header>
            <div>
              <Link href="/">
                <div className=" flex gap-2 md:gap-4 items-center ">
                  <Logo className=" text-[#673DE6] h-[28px] md:h-[30px] lg:h-[40px] " />
                  <Title className=" text-white  h-8 md:h-10 lg:h-14 " />
                </div>
              </Link>
            </div>
          </header>
        </LandingPageContainer>

        <div className="p-4 rounded-t-xl sm:hidden fixed bottom-0 w-full z-10 bg-blue-1 translate-y-[var(--translate-y)] transition-all duration-300">
          <Link href="/login" className=" max-sm:w-full ">
            <div className=" bg-red-2 px-10 py-4 sm:py-5 rounded-xl cursor-pointer max-sm:text-center sm:w-fit text-xl font-medium hover:bg-red-1 transition-all duration-300 ">
              Get Started for Free
            </div>
          </Link>
        </div>

        <LandingPageContainer className=" px-4 py-12 sm:p-16 sm:px-10 lg:p-20 -mt-4 ">
          <div className=" flex flex-col gap-y-12 ">
            <div className=" flex max-lg:flex-col justify-between gap-y-12 sm:gap-y-16  ">
              <div className=" flex flex-col justify-between gap-16 ">
                <div className=" space-y-4 ">
                  <Heading>
                    Create Stunning Ads Effortlessly - Powered by AI and Human
                    Expertise
                  </Heading>
                  <Text className=" lg:w-[70%] ">
                    Simplify your ad creation with DesignEasy: Unlimited designs
                    for banners, videos, and carousels every month.
                  </Text>
                </div>
                <Link
                  href="/login"
                  className=" max-sm:hidden max-lg:self-center "
                >
                  <div className=" bg-red-2 px-10 py-5 rounded-xl cursor-pointer w-fit text-xl font-medium hover:bg-red-1 transition-all duration-300 ">
                    Get Started for Free
                  </div>
                </Link>
              </div>
              <Image
                src="/images/background/homepage.png"
                width={460}
                height={565}
                alt="homepage image"
                className=" max-lg:self-center max-h-[560px] self-center "
                priority
              />
            </div>
            <Link href="/login" className=" sm:hidden ">
              <div
                id="landing-page-main-button"
                className=" bg-red-2 px-10 py-4 rounded-xl cursor-pointer text-center text-xl font-medium hover:bg-red-1 transition-all duration-300 "
              >
                Get Started for Free
              </div>
            </Link>
          </div>
        </LandingPageContainer>

        <LandingPageContainer className=" px-4 py-12 sm:p-16 sm:px-10 lg:p-20 ">
          <div className=" text-center space-y-4 sm:space-y-8 ">
            <Heading level={3}>
              From Concept to Conversion - Smarter Ads Made Simple
            </Heading>
            <Text className=" sm:w-4/5 mx-auto ">
              Save time and scale your marketing campaigns with AI-powered
              designs across <FacebookIcon className=" inline-block w-8 h-8 " />{' '}
              , <GoogleIcon className=" inline-block w-8 h-8 " /> ,{' '}
              <TicktockIcon className=" inline-block w-8 h-8 " /> , and{' '}
              <YoutubeIcon className=" inline-block w-8 h-8 " /> .
            </Text>
          </div>
        </LandingPageContainer>

        <KeyFeatures />

        <HowItWorks />

        <WhyChoose />

        <StartCreatingAds />

        <hr className=" border-gray-400/30 " />
        <div className=" text-black max-sm:pb-24 ">
          <Footer className=" !bg-blue-1 !text-white !px-4 !p-4 sm:!p-16 sm:!px-10 lg:!p-20 " />
        </div>
      </div>
    </ShowHideCTA>
  );
}

export default Home;
