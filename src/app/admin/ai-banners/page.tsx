'use client';

import { getCommonHeaders } from '@/actions';
import { getAiAdBannersForAdmin } from '@/actions/bannerbotV2';
import { useUser } from '@/components/context/UserProvider';
import PreviewMediaDialog from '@/components/onboarding/editVideoTemplate/PreviewMediaDialog';
import { logApiErrorAndShowToastMessage } from '@/utils';
import Image from 'next/image';
import Link from 'next/link';
import React, { useMemo, useState } from 'react';
import { FaArrowRight } from 'react-icons/fa6';
import { IoIosExpand } from 'react-icons/io';
import { useInfiniteQuery } from 'react-query';

const AiBanners = () => {
  const { user } = useUser();
  const [previewMediaDetails, setPreviewMediaDetails] = useState<{
    type: 'image' | 'video' | 'audio';
    url: string;
  }>();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const getAiAdBannersForAdminQuery = useInfiniteQuery(
    ['getAiAdBannersForAdmin'],
    (params) => {
      return getAiAdBannersForAdmin({
        headers: getCommonHeaders(user),
        queryParams: {
          last_cursor_id: params.pageParam ?? '',
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data.last_cursor_id !== ''
          ? lastPage.data.last_cursor_id
          : undefined;
      },
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'ai-banners.getAiAdBannersForAdminQuery',
        );
      },
    },
  );

  const { data, isLoading, isFetchingNextPage } = getAiAdBannersForAdminQuery;

  const allBanners = useMemo(() => {
    return data?.pages.map((item) => item.data.data).flat();
  }, [data]);

  return (
    <div className=" flex-1 text-gray-50 max-w-7xl xl:w-full mx-auto max-xl:mx-4 shadow-sm rounded-xl bg-white/10 p-4 sm:p-6 space-y-4 new-scrollbar ">
      <div className=" flex flex-col gap-4 ">
        {allBanners?.map((item) => (
          <div
            key={item.ai_banner.id}
            className=" bg-blue-1 p-4 md:p-6 rounded-xl flex gap-4 max-md:flex-col overflow-hidden "
          >
            <div className=" relative max-md:self-center ">
              <Image
                src={item.ai_banner.banner_url}
                height={200}
                width={200}
                alt="AI generated banners"
                className=" object-cover "
              />
              <div
                className=" p-1 bg-blue-800 rounded-md absolute top-2 right-2 cursor-pointer "
                onClick={() => {
                  setPreviewMediaDetails({
                    type: 'image',
                    url: item.ai_banner.banner_url,
                  });
                  setIsDialogOpen(true);
                }}
              >
                <IoIosExpand size={16} />
              </div>
            </div>
            <div className=" flex flex-col  text-gray-medium ">
              <p className=" font-semibold ">
                Name:{' '}
                <span className=" font-normal ">
                  {item.user_details.name}
                </span>{' '}
              </p>
              <p className=" font-semibold ">
                Email:{' '}
                <span className=" font-normal ">
                  {item.user_details.email}
                </span>{' '}
              </p>
              <Link href={`/onboarding?id=${item.ai_banner.project_id}`}>
                <div className=" !mt-6 hover:underline flex gap-1 items-center ">
                  <p>Redirect to project</p>{' '}
                  <FaArrowRight
                    size={16}
                    className=" -rotate-45 group-hover:rotate-0 transition-all duration-300 "
                  />
                </div>
              </Link>
            </div>
          </div>
        ))}
      </div>
      {(isLoading || isFetchingNextPage) && (
        <div className=" space-y-4 ">
          <div className=" flex gap-4 animate-pulse ">
            <div className=" w-[200px] h-[200px] bg-gray-600 rounded-xl " />
            <div className=" flex-1 space-y-1 ">
              <div className=" h-5 bg-gray-600 rounded " />
              <div className=" h-5 bg-gray-600 rounded " />
              <div className=" h-5 bg-gray-600 rounded !mt-6 " />
            </div>
          </div>
        </div>
      )}
      {getAiAdBannersForAdminQuery.hasNextPage && !isFetchingNextPage && (
        <div className=" mt-3 ">
          <p
            className=" text-center cursor-pointer underline text-gray-500 "
            onClick={() => getAiAdBannersForAdminQuery.fetchNextPage()}
          >
            load more
          </p>
        </div>
      )}

      <PreviewMediaDialog
        isDialogOpen={isDialogOpen}
        setIsDialogOpen={setIsDialogOpen}
        previewMediaDetails={previewMediaDetails}
        setPreviewMediaDetails={setPreviewMediaDetails}
      />
    </div>
  );
};

export default AiBanners;
