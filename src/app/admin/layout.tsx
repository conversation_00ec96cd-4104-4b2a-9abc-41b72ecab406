'use client';

import React, { useEffect, useState } from 'react';
import {
  FiCheckCircle,
  FiCreditCard,
  FiEdit3,
  FiMenu,
  FiVideo,
} from 'react-icons/fi';
import Logo from '@/images/logo/logo-white.svg';
import Title from '@/images/logo/title.svg';
import TextV2 from '@/components/lib/typography/TextV2';
import { FaRobot } from 'react-icons/fa6';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

const AdminLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const searchParams = useSearchParams();
  const [selectedButton, setSelectedButton] = useState<
    | 'subscription'
    | 'complete-project'
    | 'draft-project'
    | 'ai-banners'
    | 'video-banner'
  >();
  const pathname = usePathname();
  const router = useRouter();

  const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

  useEffect(() => {
    switch (pathname) {
      case '/admin/subscription':
        setSelectedButton('subscription');
        break;
      case '/admin/ai-banners':
        setSelectedButton('ai-banners');
        break;
      case '/admin': {
        if (searchParams.get('tab') === 'draft') {
          setSelectedButton('draft-project');
          break;
        } else {
          setSelectedButton('complete-project');
          break;
        }
      }
    }
  }, [pathname, searchParams]);

  const handleButtonClick = (type: string) => {
    switch (type) {
      case 'subscription':
        router.push('/admin/subscription');
        break;
      case 'ai-banners':
        router.push('/admin/ai-banners');
        break;
      case 'draft-project':
        router.push('/admin?tab=draft');
        break;
      case 'complete-project':
        router.push('/admin?tab=complete');
        break;
    }
    setIsSidebarOpen(false);
  };

  useEffect(() => {
    document.body.style.backgroundColor = '#1f1346';
  }, []);

  return (
    <div className=" relative bg-blue-1 text-gray-50 ">
      {/* SIDEBAR BACKDROP */}
      <div
        className={` ${isSidebarOpen ? ' max-sm:w-full max-sm:h-full ' : ' '} fixed left-0 top-0 z-[1] bg-black/15 `}
        onClick={toggleSidebar}
      />

      {/* SIDEBAR */}
      <div
        className={` ' ${isSidebarOpen ? ' max-sm:translate-x-0 ' : ' max-sm:-translate-x-full '} bg-blue-950 w-[300px] p-4 md:p-6 fixed left-0 top-0 z-[2] sm:z-0 shrink-0 h-screen max-md:fixed max-md:left-0 max-md:top-0 max-md:z-[1] overflow-hidden transition-all duration-300 `}
      >
        <div className={` flex gap-4 items-center justify-between `}>
          <FiMenu
            size={24}
            className=" shrink-0 cursor-pointer "
            onClick={toggleSidebar}
          />
          <div
            className={` ${isSidebarOpen ? ' opacity-100 ' : ' opacity-0 '} `}
          >
            <div className=" flex gap-2 items-center ">
              <Logo className=" text-[#673DE6] h-[18px] md:h-5 " />
              <Title className=" text-white h-8 md:h-10 lg:h-10 mt-1 " />
            </div>
          </div>
        </div>
        <div className=" py-4 md:py-6 space-y-4 ">
          <hr
            className={` ${isSidebarOpen ? ' max-w-[400px] ' : ' sm:max-w-6 '} border-gray-medium transition-all duration-300 `}
          />
          <div
            className={` flex gap-3 text-gray-300 ${isSidebarOpen ? ' px-4 ' : ' sm:-ml-2 sm:w-fit sm:px-2 '} transition-all duration-300 p-2 rounded-lg ring-gray-medium/20 hover:ring hover:bg-gray-medium/15 cursor-pointer data-[selected=true]:bg-gray-medium/10 `}
            data-selected={selectedButton === 'subscription'}
            onClick={() => handleButtonClick('subscription')}
          >
            <FiCreditCard size={24} />
            <p className={` ${isSidebarOpen ? '' : ' sm:hidden '}`}>
              Subscription
            </p>
          </div>
          <div className=" space-y-2 ">
            <TextV2
              level={2}
              className={`  text-gray-medium ${isSidebarOpen ? '' : ' sm:hidden '} `}
            >
              Projects
            </TextV2>
            <hr
              className={` ${isSidebarOpen ? ' max-w-[400px] ' : ' sm:max-w-6 '} border-gray-medium transition-all duration-300 `}
            />
            <div className=" space-y-1 ">
              <div
                className={` flex gap-3 text-gray-300 ${isSidebarOpen ? ' px-4 ' : ' sm:-ml-2 sm:w-fit sm:px-2 '} transition-all duration-300 p-2 rounded-lg ring-gray-medium/20 hover:ring hover:bg-gray-medium/15 cursor-pointer data-[selected=true]:bg-gray-medium/10 `}
                data-selected={selectedButton === 'complete-project'}
                onClick={() => handleButtonClick('complete-project')}
              >
                <FiCheckCircle size={24} />
                <p className={` ${isSidebarOpen ? '' : ' sm:hidden '}`}>
                  Complete
                </p>
              </div>
              <div
                className={` flex gap-3 text-gray-300 ${isSidebarOpen ? ' px-4 ' : ' sm:-ml-2 sm:w-fit sm:px-2 '} transition-all duration-300 p-2 rounded-lg ring-gray-medium/20 hover:ring hover:bg-gray-medium/15 cursor-pointer data-[selected=true]:bg-gray-medium/10 `}
                data-selected={selectedButton === 'draft-project'}
                onClick={() => handleButtonClick('draft-project')}
              >
                <FiEdit3 size={24} />
                <p className={` ${isSidebarOpen ? '' : ' sm:hidden '}`}>
                  Draft
                </p>
              </div>
            </div>
          </div>
          <div className=" space-y-2 ">
            <TextV2
              level={2}
              className={`  text-gray-medium ${isSidebarOpen ? '' : ' sm:hidden '} `}
            >
              Banners
            </TextV2>
            <hr
              className={` ${isSidebarOpen ? ' max-w-[400px] ' : ' sm:max-w-6 '} border-gray-medium transition-all duration-300 `}
            />
            <div className=" space-y-1 ">
              <div
                className={` flex gap-3 text-gray-300 ${isSidebarOpen ? ' px-4 ' : ' sm:-ml-2 sm:w-fit sm:px-2 '} transition-all duration-300 p-2 rounded-lg ring-gray-medium/20 hover:ring hover:bg-gray-medium/15 cursor-pointer data-[selected=true]:bg-gray-medium/10 `}
                data-selected={selectedButton === 'ai-banners'}
                onClick={() => handleButtonClick('ai-banners')}
              >
                <FaRobot size={24} />
                <p className={` ${isSidebarOpen ? '' : ' sm:hidden '}`}>
                  AI Banners
                </p>
              </div>
              <div
                className={` flex gap-3 text-gray-300 ${isSidebarOpen ? ' px-4 ' : ' sm:-ml-2 sm:w-fit sm:px-2 '} transition-all duration-300 p-2 rounded-lg ring-gray-medium/20 hover:ring hover:bg-gray-medium/15 cursor-pointer data-[selected=true]:bg-gray-medium/10 `}
                data-selected={selectedButton === 'video-banner'}
                onClick={() => handleButtonClick('video-banner')}
              >
                <FiVideo size={24} />
                <p className={` ${isSidebarOpen ? '' : ' sm:hidden '}`}>
                  Video Banners
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        className={` ${isSidebarOpen ? ' sm:left-[300px] ' : ' sm:left-15 md:left-17 '} max-sm:left-0 bg-gradient-to-br from-blue-1 to-[#111c44] fixed top-0 bottom-0 right-0 sm:z-10 overflow-x-hidden overflow-auto transition-all duration-300 new-scrollbar mr-1 pr-1 `}
      >
        {/* HEADER */}
        <div className=" p-4 md:p-6 flex gap-6 items-center sticky top-0 z-[1] bg-blue-1 ">
          <FiMenu
            size={24}
            className=" shrink-0 cursor-pointer sm:hidden "
            onClick={toggleSidebar}
          />
          <div
            className={` ${isSidebarOpen ? ' opacity-0 ' : 'opacity-100 '} `}
          >
            <div className=" flex gap-2 items-center ">
              <Logo className=" text-[#673DE6] h-[18px] md:h-5 " />
              <Title className=" text-white h-8 md:h-10 lg:h-10 mt-1 " />
            </div>
          </div>
        </div>
        {/* MAIN CONTENT */}
        <main className=" p-4 max-sm:px-0 md:p-6 max-w-7xl mx-auto ">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
