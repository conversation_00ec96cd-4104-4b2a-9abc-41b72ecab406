'use client';

import { getCommonHeaders } from '@/actions';
import { getProjectsForAdmin } from '@/actions/bannerbotV2';
import ProjectCard from '@/components/admin/ProjectCard';
import ProjectListCard from '@/components/admin/ProjectListCard';
import { useUser } from '@/components/context/UserProvider';
import Text from '@/components/lib/typography/Text';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useInfiniteQuery } from 'react-query';

const Admin = () => {
  const { user } = useUser();

  const [selectedProject, setSelectedProject] = useState<{
    userDetail: {
      uid: string;
      name: string;
      email: string;
    };
    project: IBannerbotProject;
  }>();

  const searchParams = useSearchParams();
  const router = useRouter();

  const selectedTab = useMemo(
    () => searchParams.get('tab') ?? 'complete',
    [searchParams],
  );

  const getProjectsInfiniteQuery = useInfiniteQuery(
    ['getProjects-admin', selectedTab],
    (params) => {
      return getProjectsForAdmin({
        headers: getCommonHeaders(user),
        queryParams: {
          last_cursor_id: params.pageParam ?? '',
          status: selectedTab.toUpperCase(),
          limit: '15',
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data.last_cursor_id !== ''
          ? lastPage.data.last_cursor_id
          : undefined;
      },
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'admin.getProjectsInfiniteQuery');
      },
    },
  );

  const { data, isLoading, isFetchingNextPage } = getProjectsInfiniteQuery;

  const allProjects = useMemo(() => {
    return data?.pages.map((item) => item.data.data).flat();
  }, [data]);

  useEffect(() => {
    if (!searchParams.has('tab')) {
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', 'complete');
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  }, [selectedTab, searchParams, router]);

  return (
    <div className=" text-gray-50 ">
      <div className=" max-w-7xl xl:w-full mx-auto max-xl:mx-4 shadow-sm rounded-xl bg-white/10  ">
        <div className=" flex-1 p-4 sm:p-6 space-y-4 new-scrollbar ">
          {selectedProject ? (
            <ProjectCard
              {...selectedProject}
              handleBack={() => setSelectedProject(undefined)}
              redirectUrl={`/onboarding?id=${selectedProject.project.id}`}
            />
          ) : (
            <div className=" space-y-4 ">
              {allProjects?.map((item) => (
                <ProjectListCard
                  {...item}
                  key={item.project.id}
                  handleClick={() => setSelectedProject(item)}
                />
              ))}
              {getProjectsInfiniteQuery.hasNextPage && !isFetchingNextPage && (
                <div className=" mt-3 ">
                  <Text
                    level={3}
                    className=" text-center cursor-pointer underline text-gray-500 "
                    onClick={() => getProjectsInfiniteQuery.fetchNextPage()}
                  >
                    load more
                  </Text>
                </div>
              )}
            </div>
          )}
          {(isLoading || isFetchingNextPage) && (
            <>
              <div className=" p-6 shadow rounded-xl bg-blue-1 h-[130px] " />
              <div className=" p-6 shadow rounded-xl bg-blue-1 h-[130px] " />
              <div className=" p-6 shadow rounded-xl bg-blue-1 h-[130px] " />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Admin;
