'use client';

import { getCommonHeaders } from '@/actions';
import {
  createNewSubscriptionPlan,
  getAllSubscriptionPlans,
  updateSubscriptionPlan,
} from '@/actions/subscriptions';
import SubscriptionCard from '@/components/admin/subscriptions/SubscriptionCard';
import SubscriptionForm from '@/components/admin/subscriptions/SubscriptionForm';
import { useUser } from '@/components/context/UserProvider';
import Dialog from '@/components/lib/Dialog';
import { ISubscriptionPlan } from '@/types/subscription';
import { logApiErrorAndShowToastMessage } from '@/utils';
import { useState } from 'react';
import { FaPlus, FaX } from 'react-icons/fa6';
import { useMutation, useQuery } from 'react-query';

export default function SubscriptionsPage() {
  const { user } = useUser();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const getAllSubscriptionsQuery = useQuery(['getAllSubscriptionPlans'], () => {
    return getAllSubscriptionPlans({
      headers: getCommonHeaders(user),
    });
  });

  const createSubscriptionPlanMutation = useMutation(createNewSubscriptionPlan);

  const handleCreateSubscription = (subscription: ISubscriptionPlan) => {
    createSubscriptionPlanMutation
      .mutateAsync({
        data: subscription,
        headers: getCommonHeaders(user),
      })
      .then(() => {
        setIsDialogOpen(false);
        getAllSubscriptionsQuery.refetch();
      })
      .catch((error) => {
        logApiErrorAndShowToastMessage(
          error,
          'subscriptions.handleCreateSubscription',
        );
      });
  };

  const updateSubscriptionPlanMutation = useMutation(updateSubscriptionPlan);

  const handleUpdateSubscription = (subscription: ISubscriptionPlan) => {
    updateSubscriptionPlanMutation
      .mutateAsync({
        data: subscription,
        headers: getCommonHeaders(user),
      })
      .then(() => {
        setIsDialogOpen(false);
        getAllSubscriptionsQuery.refetch();
      })
      .catch((error) => {
        logApiErrorAndShowToastMessage(
          error,
          'subscriptions.handleCreateSubscription',
        );
      });
  };

  const { data: subscriptions } = getAllSubscriptionsQuery;

  return (
    <div className="">
      <div className=" mx-auto px-4 space-y-6">
        <div className=" flex justify-end ">
          <button
            onClick={() => setIsDialogOpen(true)}
            className="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center gap-2 transition-all duration-300 transform hover:scale-105"
          >
            <FaPlus className="h-5 w-5" />
            <span>Add New Plan</span>
          </button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {subscriptions?.data.map((subscription) => (
            <SubscriptionCard
              key={subscription.id}
              subscription={subscription}
              onUpdate={handleUpdateSubscription}
            />
          ))}
        </div>
      </div>

      {isDialogOpen && (
        <Dialog isOpen={isDialogOpen} setIsOpen={setIsDialogOpen}>
          <div className="bg-gray-800 rounded-2xl p-6 sm:p-8 w-full max-w-2xl relative max-h-[90vh] overflow-y-auto">
            <button
              onClick={() => setIsDialogOpen(false)}
              className="absolute right-4 top-4 text-gray-400 hover:text-white"
            >
              <FaX className="h-6 w-6" />
            </button>
            <h2 className="text-xl sm:text-2xl font-bold mb-6">
              Create New Subscription Plan
            </h2>
            <SubscriptionForm
              onSubmit={handleCreateSubscription}
              isCreatingNew
            />
          </div>
        </Dialog>
      )}
    </div>
  );
}
