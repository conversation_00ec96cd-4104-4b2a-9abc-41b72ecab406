'use client';

import { getCommonHeaders } from '@/actions';
import {
  cancelUserSubscription,
  getUserSubscription,
  reactivateUserSubscription,
} from '@/actions/subscriptions';
import { useUser } from '@/components/context/UserProvider';
import Button from '@/components/lib/Button';
import Dialog from '@/components/lib/Dialog';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import HeadingV2 from '@/components/lib/typography/HeadingV2';
import TextV2 from '@/components/lib/typography/TextV2';
import RedeemCouponDialog from '@/components/RedeemCouponDialog';
import { showToastMessage } from '@/modules/toast';
import {
  IUserSubscription,
  UserSubscriptionSource,
} from '@/types/subscription';
import { logApiErrorAndShowToastMessage } from '@/utils';
import classNames from 'classnames';
import Link from 'next/link';
import React, { useMemo, useState } from 'react';
import { BsFillLightningChargeFill } from 'react-icons/bs';
import {
  FiCalendar,
  FiClock,
  FiCreditCard,
  FiInfo,
  FiStar,
  FiTag,
} from 'react-icons/fi';
import { IoGiftOutline } from 'react-icons/io5';
import { useMutation, useQuery, UseQueryResult } from 'react-query';

const Profile = () => {
  const { user, handleSignOut } = useUser();
  const [showRedeemDialog, setShowRedeemDialog] = useState(false);

  const getUserSubscriptionQuery = useQuery(
    ['getUserSubscription'],
    () => {
      return getUserSubscription({
        headers: getCommonHeaders(user),
      });
    },
    {
      enabled: !!user,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    },
  );

  const userSubscription = useMemo(() => {
    return getUserSubscriptionQuery.data?.data;
  }, [getUserSubscriptionQuery.data]);

  const isFreeUser = useMemo(
    () => !getUserSubscriptionQuery.isLoading && !userSubscription,
    [getUserSubscriptionQuery.isLoading, userSubscription],
  );

  return (
    <div className=" flex-1 flex flex-col ">
      <div className=" space-y-2 ">
        <HeadingV2 level={2}>My Profile</HeadingV2>
        <p className=" text-sm md:text-base text-gray-medium">
          Manage your account settings and subscription
        </p>
      </div>

      <div className=" mt-8 md:mt-10 flex-1 flex max-md:flex-col gap-6 sm:gap-8 ">
        <div className=" flex-2/5 space-y-6 md:space-y-4 ">
          <div className=" space-y-4 p-4 sm:p-6 bg-white/5 border border-blue-400/30 rounded-xl ">
            <div className=" space-y-1 ">
              <p className=" text-sm text-gray-medium font-medium ">Name</p>
              <TextV2 level={3}>{user?.displayName}</TextV2>
            </div>
            <div className=" space-y-1 ">
              <p className=" text-sm text-gray-medium font-medium ">Email</p>
              <TextV2 level={3}>{user?.email}</TextV2>
            </div>
            {/* <hr className=" !my-3 border-gray-dark " /> */}
          </div>
          <div className=" max-md:mx-auto w-fit ">
            <Button onClick={handleSignOut}>Logout</Button>
          </div>
        </div>

        {userSubscription &&
          (userSubscription.source === UserSubscriptionSource.STRIPE ? (
            <StripeSubscriptionDetails
              userSubscription={userSubscription}
              getUserSubscriptionQuery={getUserSubscriptionQuery}
            />
          ) : (
            <AppSumoSubscriptionDetails userSubscription={userSubscription} />
          ))}

        {isFreeUser && (
          <div className=" flex-3/5 space-y-8 p-4 sm:p-6 bg-white/5 border border-blue-400/30 rounded-xl ">
            <div className=" space-y-2 ">
              <div className=" flex justify-between items-center gap-4">
                <TextV2 className=" capitalize font-medium ">Free Plan</TextV2>
                <div className="shrink-0 self-center px-3 py-0.5 rounded-3xl capitalize bg-blue-400 flex gap-1.5 items-center ">
                  <FiStar size={14} className=" shrink-0 " />
                  Free
                </div>
              </div>
              <p className=" text-sm md:text-base text-gray-300 ">
                You&apos;re on the Free Plan. Upgrade to enjoy premium features!
              </p>
            </div>

            <div className=" flex gap-2 bg-blue-1/30 p-4 rounded-lg border border-amber-300/30 ">
              <FiStar size={20} className=" text-amber-300 shrink-0 mt-0.5 " />
              <div>
                <TextV2 level={3}>You&apos;re on the Free Plan</TextV2>
                <TextV2 level={4} className=" text-gray-300 ">
                  Create eye-catching banners and videos with our core tools.
                  Level up your creativity with even more features when you
                  upgrade.
                </TextV2>
              </div>
            </div>

            <div className=" space-y-2 ">
              <div className=" flex items-center gap-2 ">
                <FiTag size={16} className=" text-pink-700 " />
                <p>Unlimited templatized banners</p>
              </div>
              <div className=" flex items-center gap-2 ">
                <FiTag size={16} className=" text-pink-700 " />
                <p>1 AI banners per day</p>
              </div>
              <div className=" flex items-center gap-2 ">
                <FiTag size={16} className=" text-pink-700 " />
                <p>2 templatized videos per day</p>
              </div>
            </div>
            <div className=" flex flex-col gap-2  ">
              <Link href={'/subscription'}>
                <Button variant={'default'} className=" w-full !bg-green-600 ">
                  Subscribe to Premium
                </Button>
              </Link>
              <Button
                variant={'outline'}
                className=" w-full hover:!bg-blue-300/60 "
                onClick={() => setShowRedeemDialog(true)}
              >
                <IoGiftOutline /> Redeem Coupon
              </Button>
            </div>
          </div>
        )}
      </div>
      <RedeemCouponDialog
        isOpen={showRedeemDialog}
        setIsOpen={setShowRedeemDialog}
        getUserSubscriptionQuery={getUserSubscriptionQuery}
      />
      {getUserSubscriptionQuery.isLoading && <FullScreenLoader />}
    </div>
  );
};

const StripeSubscriptionDetails = (props: {
  userSubscription: IUserSubscription;
  getUserSubscriptionQuery: UseQueryResult<
    {
      data: IUserSubscription | undefined;
    },
    unknown
  >;
}) => {
  const { getUserSubscriptionQuery, userSubscription } = props;
  const { user } = useUser();
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const isCancellingSubscription = useMemo(() => {
    if (
      userSubscription.status === 'active' &&
      userSubscription.cancel_at_period_end
    )
      return true;
    return false;
  }, [userSubscription]);

  const subscriptionEndData = useMemo(() => {
    if (!userSubscription) return undefined;
    return userSubscription.current_period_end
      ? new Date(userSubscription.current_period_end * 1000).toLocaleDateString(
          'en-US',
          {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          },
        )
      : undefined;
  }, [userSubscription]);

  const templatizedVideoLeftToday = useMemo(
    () => userSubscription?.templatized_video_left,
    [userSubscription],
  );

  const maxTemplatizedVideoPerDay = useMemo(
    () => Number(userSubscription?.features.max_templatized_videos_per_day),
    [userSubscription],
  );

  const reactivateUserSubscriptionMutation = useMutation(
    reactivateUserSubscription,
  );

  const reactivateSubscription = () => {
    reactivateUserSubscriptionMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        data: { subscription_id: userSubscription!.stripe_subscription_id },
      })
      .then((data) => {
        showToastMessage(data.data.message, 'success');
        getUserSubscriptionQuery.refetch();
      })
      .catch((error) => {
        logApiErrorAndShowToastMessage(error, 'profile.handleButtonClick');
      });
  };

  const handleButtonClick = () => {
    if (!userSubscription?.stripe_subscription_id) return;

    if (isCancellingSubscription) reactivateSubscription();
    else setShowConfirmationDialog(true);
  };

  const cancelUserSubscriptionMutation = useMutation(cancelUserSubscription);

  const cancelSubscription = () => {
    cancelUserSubscriptionMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        data: { subscription_id: userSubscription!.stripe_subscription_id },
      })
      .then((data) => {
        showToastMessage(data.data.message, 'success');
        getUserSubscriptionQuery.refetch();
      })
      .catch((error) => {
        logApiErrorAndShowToastMessage(error, 'profile.handleButtonClick');
      });
  };

  return (
    <>
      {(reactivateUserSubscriptionMutation.isLoading ||
        cancelUserSubscriptionMutation.isLoading) && <FullScreenLoader />}

      <div className=" flex-3/5 space-y-8 p-4 sm:p-6 bg-white/5 border border-blue-400/30 rounded-xl ">
        <div className=" space-y-2 ">
          <div className=" flex justify-between items-center gap-4 ">
            <TextV2 className=" capitalize font-medium ">
              {userSubscription.name}
            </TextV2>
            <div
              className={classNames([
                'shrink-0 self-center px-3 py-0.5 rounded-3xl capitalize',
                {
                  'bg-lime-600': userSubscription.status === 'active',
                  'bg-red-700': userSubscription.status !== 'active',
                  '!bg-amber-600': isCancellingSubscription,
                },
              ])}
            >
              {isCancellingSubscription
                ? 'cancelling'
                : userSubscription.status}
            </div>
          </div>
          <p className=" text-sm md:text-base text-gray-300 ">
            Manage your subscription details and billing information
          </p>
        </div>
        <div className=" space-y-4 ">
          <div className="mt-2">
            <span className="font-medium text-green-300">
              Templatized Videos left today: {templatizedVideoLeftToday ?? '--'}{' '}
              of {maxTemplatizedVideoPerDay}
            </span>
            <div className="h-2 bg-white/10 rounded mt-2 relative w-full">
              <div
                className="h-2 bg-green-500 rounded absolute top-0 left-0 transition-all"
                style={{
                  width: `${
                    templatizedVideoLeftToday !== undefined &&
                    maxTemplatizedVideoPerDay !== undefined
                      ? ((maxTemplatizedVideoPerDay -
                          templatizedVideoLeftToday) /
                          maxTemplatizedVideoPerDay) *
                        100
                      : 0
                  }%`,
                }}
              />
            </div>
          </div>
          <hr className=" border-blue-400/30 " />
          <div className=" flex justify-between items-center ">
            <div className=" flex gap-2 items-center ">
              <FiCreditCard size={20} className=" text-pink-700 " />
              <TextV2 level={3} className=" text-gray-300 ">
                Billing Amount
              </TextV2>
            </div>
            <TextV2 level={2}>
              ${userSubscription.price_in_usd}/
              <span className=" text-base ">
                {userSubscription.period === 'month' ? 'mo' : 'yr'}
              </span>
            </TextV2>
          </div>
          <hr className=" border-blue-400/30 " />
          <div className=" flex justify-between items-center ">
            <div className=" flex gap-2 items-center ">
              <FiCalendar size={20} className=" text-pink-700 " />
              <TextV2 level={3} className=" text-gray-300 ">
                Billing Period
              </TextV2>
            </div>
            <TextV2 level={3} className=" capitalize ">
              {userSubscription.period === 'month' ? 'monthly' : 'yearly'}
            </TextV2>
          </div>
          <hr className=" border-blue-400/30 " />
          <div className=" flex justify-between items-center ">
            <div className=" flex gap-2 items-center ">
              <FiClock size={20} className=" text-pink-700 " />
              <TextV2 level={3} className=" text-gray-300 ">
                Next Billing Date
              </TextV2>
            </div>
            <TextV2 level={3} className=" capitalize ">
              {subscriptionEndData}
            </TextV2>
          </div>
          <hr className=" border-blue-400/30 " />
        </div>
        {isCancellingSubscription && (
          <div className=" text-amber-300 flex gap-2 bg-blue-1/30 p-4 rounded-lg border border-amber-300/30 ">
            <FiInfo size={20} className=" shrink-0 mt-0.5 " />
            <div>
              <TextV2 level={3}>Subscription Canceling</TextV2>
              <TextV2 level={4} className=" text-amber-300/80 ">
                You won&apos;t be charged after {subscriptionEndData}. Enjoy
                your benefits until then!
              </TextV2>
            </div>
          </div>
        )}
        <div className=" space-y-2 ">
          {userSubscription.line_items.map((item) => (
            <div key={item} className=" flex items-center gap-2 ">
              <FiTag size={16} className=" text-pink-700 " />
              <p>{item}</p>
            </div>
          ))}
        </div>
        <Button
          variant={isCancellingSubscription ? 'default' : 'secondary'}
          className=" w-full "
          onClick={handleButtonClick}
          disabled={userSubscription.status !== 'active'}
        >
          {isCancellingSubscription
            ? 'Reactivate Subscription'
            : 'Cancel Subscription'}
        </Button>
      </div>
      <Dialog
        isOpen={showConfirmationDialog}
        setIsOpen={setShowConfirmationDialog}
        className=" max-h-[80vh] flex flex-col space-y-4 border-2 border-off-white/40 p-6 rounded-xl bg-blue-1 w-full max-w-[700px] overflow-auto mx-4 "
      >
        <TextV2 level={2}>Are your sure?</TextV2>
        <TextV2 level={3} className=" text-gray-300 ">
          Your subscription will remain active until the end of your current
          billing period ({subscriptionEndData}). After that date, you will lose
          access to premium features.{' '}
        </TextV2>
        <div className=" flex max-sm:flex-col gap-4 md:justify-end ">
          <div className=" max-md:flex-1 ">
            <Button
              className=" capitalize max-md:w-full max-md:flex-1 "
              variant={'outline'}
              size={'sm'}
              onClick={() => setShowConfirmationDialog(false)}
            >
              no, keep my subscription
            </Button>
          </div>
          <div className=" max-md:flex-1 ">
            <Button
              className=" capitalize max-md:w-full max-md:flex-1 "
              size={'sm'}
              onClick={() => {
                setShowConfirmationDialog(false);
                cancelSubscription();
              }}
            >
              yes, cancel at period end
            </Button>
          </div>
        </div>
      </Dialog>
    </>
  );
};

const AppSumoSubscriptionDetails = (props: {
  userSubscription: IUserSubscription;
}) => {
  const { userSubscription } = props;

  const templatizedVideoLeftToday = useMemo(
    () => userSubscription?.templatized_video_left,
    [userSubscription],
  );

  const maxTemplatizedVideoPerDay = useMemo(
    () => Number(userSubscription?.features.max_templatized_videos_per_day),
    [userSubscription],
  );

  const aiBannersLeftToday = useMemo(
    () => userSubscription?.ai_ad_banner_left,
    [userSubscription],
  );

  const maxAiBannersPerDay = useMemo(
    () => Number(userSubscription?.features.max_ai_banners_per_day),
    [userSubscription],
  );

  return (
    <div className=" flex-3/5 space-y-8 p-4 sm:p-6 bg-white/5 border border-blue-400/30 rounded-xl ">
      <div className=" space-y-2 ">
        <div className=" flex justify-between items-center gap-4">
          <TextV2 className=" capitalize font-medium ">AppSumo Plan</TextV2>
          <div className="shrink-0 self-center px-3 py-0.5 rounded-3xl capitalize bg-gradient-to-r from-purple-500 to-pink-500 text-white flex gap-1.5 items-center ">
            <BsFillLightningChargeFill size={14} className=" shrink-0 " />
            Unlimited
          </div>
        </div>
        <p className=" text-sm md:text-base text-gray-300 ">
          You have unlimited access with your AppSumo lifetime deal!
        </p>
      </div>

      <div className="p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-pink-900/30 border border-purple-500/30">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
            ⚡
          </div>
          <div>
            <h3 className="font-semibold text-white">AppSumo Lifetime Deal</h3>
            <p className="text-sm text-purple-300">
              Unlimited designs • No expiration
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between  text-purple-200">
          Create eye-catching banners and videos with our core tools.
        </div>
      </div>

      <div className="mt-2">
        <span className="font-medium text-green-300">
          Templatized Videos left today: {templatizedVideoLeftToday ?? '--'} of{' '}
          {maxTemplatizedVideoPerDay}
        </span>
        <div className="h-2 bg-white/10 rounded mt-2 relative w-full">
          <div
            className="h-2 bg-green-500 rounded absolute top-0 left-0 transition-all"
            style={{
              width: `${
                templatizedVideoLeftToday !== undefined &&
                maxTemplatizedVideoPerDay !== undefined
                  ? ((maxTemplatizedVideoPerDay - templatizedVideoLeftToday) /
                      maxTemplatizedVideoPerDay) *
                    100
                  : 0
              }%`,
            }}
          />
        </div>
      </div>
      <div className="mt-2">
        <span className="font-medium text-green-300">
          AI Banners left today: {aiBannersLeftToday ?? '--'} of{' '}
          {maxAiBannersPerDay}
        </span>
        <div className="h-2 bg-white/10 rounded mt-2 relative w-full">
          <div
            className="h-2 bg-green-500 rounded absolute top-0 left-0 transition-all"
            style={{
              width: `${
                aiBannersLeftToday !== undefined &&
                maxAiBannersPerDay !== undefined
                  ? ((maxAiBannersPerDay - aiBannersLeftToday) /
                      maxAiBannersPerDay) *
                    100
                  : 0
              }%`,
            }}
          />
        </div>
      </div>

      <div className=" space-y-2 ">
        {userSubscription.line_items.map((item) => (
          <div key={item} className=" flex items-center gap-2 ">
            <FiTag size={16} className=" text-pink-700 " />
            <p>{item}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Profile;
