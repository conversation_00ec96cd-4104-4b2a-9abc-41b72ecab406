import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  title: 'Privacy Policy',
  alternates: {
    canonical: 'https://designeasy.xyz/privacy-policy',
  },
};

const PrivacyPolicy = () => {
  return (
    <div className="">
      <h1 className="mt-6 text-2xl font-medium">
        Privacy Policy for DesignEasy, a service provided by AUTOTME SOFTWARE
        PRIVATE LIMITED
      </h1>
      <p className="text-sm font-medium mt-3">Effective Date: 4th Feb 2025</p>

      <h2 className="mt-8 text-xl">1. Introduction</h2>
      <p className="text-sm mt-2">
        Welcome to DesignEasy, a service provided by AUTOTME SOFTWARE PRIVATE
        LIMITED (&quot;Company,&quot; &quot;we,&quot; &quot;us,&quot; or
        &quot;our&quot;). Your privacy is important to us, and we are committed
        to protecting the personal information you share with us. This Privacy
        Policy outlines how we collect, use, and safeguard your information when
        you use DesignEasy.
      </p>

      <h2 className="mt-8 text-xl">2. Information We Collect</h2>
      <p className="text-sm mt-2">
        When you use DesignEasy, we may collect the following types of
        information:
      </p>
      <div className="mt-4">
        <h3 className="text-base">a. Personal Information:</h3>
        <p className="text-sm mt-2">
          Name, Email address, Phone number (if provided), Billing information
          (for paid plans)
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">b. Usage Data:</h3>
        <p className="text-sm mt-2">
          Log files, IP address, device type, browser type, Pages visited, time
          spent on the platform, interactions with features
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">c. Uploaded Content:</h3>
        <p className="text-sm mt-2">
          Images, text, and brand assets you upload for ad creation
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">d. Payment Information:</h3>
        <p className="text-sm mt-2">
          We use third-party payment processors to handle transactions securely;
          we do not store payment details on our servers.
        </p>
      </div>

      <h2 className="mt-8 text-xl">3. How We Use Your Information</h2>
      <p className="text-sm mt-2">We use the information collected to:</p>
      <p className="text-sm mt-2">
        Provide, operate, and improve DesignEasy services, Personalize user
        experience and offer customer support, Process transactions and manage
        subscriptions, Enhance security and prevent fraud, Send marketing
        emails, updates, and promotions (you can opt out anytime), Comply with
        legal obligations.
      </p>

      <h2 className="mt-8 text-xl">4. Sharing and Disclosure of Information</h2>
      <p className="text-sm mt-2">
        We do not sell or rent your personal data. However, we may share your
        information with:
      </p>
      <div className="mt-4">
        <h3 className="text-base">Service Providers:</h3>
        <p className="text-sm mt-2">
          Third-party partners that help us deliver our services (e.g., hosting,
          payment processing, analytics)
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">Legal Compliance:</h3>
        <p className="text-sm mt-2">
          Authorities or regulators when required by law
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">Business Transfers:</h3>
        <p className="text-sm mt-2">
          In case of a merger, acquisition, or sale of assets, user information
          may be transferred
        </p>
      </div>

      <h2 className="mt-8 text-xl">5. Data Security</h2>
      <p className="text-sm mt-2">
        We implement industry-standard security measures to protect your data.
        However, no method of transmission over the Internet is completely
        secure, and we cannot guarantee absolute security.
      </p>

      <h2 className="mt-8 text-xl">6. Data Retention</h2>
      <p className="text-sm mt-2">
        We retain personal information for as long as necessary to fulfill the
        purposes outlined in this policy, comply with legal obligations, resolve
        disputes, and enforce our agreements.
      </p>

      <h2 className="mt-8 text-xl">7. Your Rights and Choices</h2>
      <div className="mt-4">
        <h3 className="text-base">Access and Correction:</h3>
        <p className="text-sm mt-2">
          You can access, update, or delete your personal data by contacting us.
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">Opt-Out:</h3>
        <p className="text-sm mt-2">
          You can opt out of marketing communications by following the
          unsubscribe link in emails.
        </p>
      </div>
      <div className="mt-4">
        <h3 className="text-base">Cookies:</h3>
        <p className="text-sm mt-2">
          You can control cookies through your browser settings.
        </p>
      </div>

      <h2 className="mt-8 text-xl">8. Third-Party Links</h2>
      <p className="text-sm mt-2">
        DesignEasy may contain links to third-party websites. We are not
        responsible for their privacy practices, and we encourage you to review
        their policies.
      </p>

      <h2 className="mt-8 text-xl">9. Children&apos;s Privacy</h2>
      <p className="text-sm mt-2">
        DesignEasy is not intended for users under 13 years of age. We do not
        knowingly collect data from children.
      </p>

      <h2 className="mt-8 text-xl">10. Updates to This Policy</h2>
      <p className="text-sm mt-2">
        We may update this Privacy Policy from time to time. Any changes will be
        posted on this page, and your continued use of DesignEasy after changes
        constitute acceptance of the revised policy.
      </p>

      <h2 className="mt-8 text-xl">11. Contact Us</h2>
      <p className="text-sm mt-2">
        If you have any questions about this Privacy Policy, please contact us
        at:
      </p>
      <p className="text-sm mt-2">
        AUTOTME SOFTWARE PRIVATE LIMITED
        <br />
        Email: <EMAIL>
        <br />
        Address: Noida Special Economic Zone, Ground Floor Desk No 93 D, 9 Drom
        Workshala Private Limited, Sector 3 Noida, Gautam Buddha Nagar, Uttar
        Pradesh, India
      </p>

      <p className="mt-8 text-base">
        By using DesignEasy, you agree to the terms outlined in this Privacy
        Policy.
      </p>
    </div>
  );
};

export default PrivacyPolicy;
