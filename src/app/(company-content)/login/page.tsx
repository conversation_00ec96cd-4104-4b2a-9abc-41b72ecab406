'use client';

import Button from '@/lib/Button';
import React, { useCallback, useEffect, useState } from 'react';
import GoogleIcon from '@/images/icons/google.svg';
import { getAdditionalUserInfo, UserCredential } from 'firebase/auth';
import { useUser } from '@/components/context/UserProvider';
import { useRouter } from 'next/navigation';
import { logEvent } from '@/utils/firebase';
import { FirebaseError } from 'firebase/app';
import { EVENT_NAMES } from '@/constants/events';
import {
  createOrUpdateUserProfile,
  sendWelcomeEmail,
} from '@/actions/bannerbotV2';
import { getCommonHeaders } from '@/actions';
import { logApiErrorAndShowToastMessage, openUrlInNewTab } from '@/utils';
import { useMutation } from 'react-query';

const Login = () => {
  const [error, setError] = useState('');
  const { user, setUser } = useUser();

  const router = useRouter();

  const createOrUpdateUserProfileMutation = useMutation(
    createOrUpdateUserProfile,
  );

  const handleNewSignup = (newUser: IBannerbotUser) => {
    void sendWelcomeEmail({
      headers: getCommonHeaders(newUser),
    });

    createOrUpdateUserProfileMutation
      .mutateAsync({
        headers: getCommonHeaders(newUser),
        data: {
          name: newUser.displayName ?? '',
          uid: newUser.uid,
          email: newUser.email ?? '',
          acquisition_source: window?.platform,
        },
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'Login.createOrUpdateUserProfileMutation',
        );
      });
  };

  const onLoginSuccess = useCallback(
    (user: IBannerbotUser) => {
      setUser(user);
    },
    [setUser],
  );

  const onFirebaseAuthSuccess = async (credential: UserCredential) => {
    const idTokenResult = await credential.user.getIdTokenResult(true);

    const user = {
      displayName: credential.user.displayName,
      email: credential.user.email,
      authToken: idTokenResult.token,
      uid: credential.user.uid,
      photoUrl: credential.user.photoURL,
      mobile: credential.user.phoneNumber,
    };

    onLoginSuccess(user);

    // Check if the user is new using `idTokenResult.claims`
    const additionalUserInfo = getAdditionalUserInfo(credential);
    if (additionalUserInfo?.isNewUser) {
      handleNewSignup(user);
    }
  };

  const handleGoogleLogin = async () => {
    if (window.bridge) {
      window.bridge.postMessage('login');
      return;
    }
    const { signInWithPopup, GoogleAuthProvider } = await import(
      'firebase/auth'
    );
    const { getFirebaseAuth } = await import('@/utils/firebase');

    logEvent(EVENT_NAMES.signin_with_google_clicked);
    const provider = new GoogleAuthProvider();
    const auth = getFirebaseAuth();

    if (auth) {
      try {
        const result = await signInWithPopup(auth, provider);
        await onFirebaseAuthSuccess(result);
      } catch (error: unknown) {
        if (error instanceof FirebaseError) {
          const errorCode = error.code;
          const errorMessage = error.message;
          setError(`Code: ${errorCode}, Error: ${errorMessage}`);
        } else {
          setError('An unexpected error occurred');
        }
      }
    }
  };

  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [user, router]);

  useEffect(() => {
    // flutter webview will call these methods
    window.onLoginSuccess = (user?: IBannerbotUser) => {
      if (user && user.authToken && user.email && user.uid) {
        onLoginSuccess(user);
      }
    };
  }, [onLoginSuccess]);

  return (
    <>
      <div className=" flex-1 bg-background flex flex-col">
        <div className=" flex-1 flex items-center justify-center">
          <div className="w-full max-w-[400px] mx-auto">
            <div className="space-y-6">
              <div className="space-y-2 text-center">
                <h1 className="text-3xl font-semibold tracking-tight">
                  Sign in
                </h1>
                <p className=" text-zinc-400 ">
                  Use your Google Account to continue
                </p>
              </div>

              <div className="space-y-4">
                <Button
                  variant="outline"
                  onClick={handleGoogleLogin}
                  className="w-full !bg-white !text-gray-900 hover:!bg-gray-50 border-2 !border-gray-100  "
                >
                  <GoogleIcon className=" w-7 h-7 " />
                  Sign in with Google
                </Button>
                {error && (
                  <p className=" text-center text-sm text-red-400 ">*{error}</p>
                )}
              </div>

              <div className="text-center text-zinc-400">
                <p>
                  By continuing, you agree to our{' '}
                  <div
                    className="underline hover:text-primary transition-colors cursor-pointer "
                    onClick={() =>
                      openUrlInNewTab(
                        'https://designeasy.ai/terms-and-conditions',
                      )
                    }
                  >
                    Terms of Service
                  </div>
                  and{' '}
                  <div
                    className="underline hover:text-primary transition-colors cursor-pointer"
                    onClick={() =>
                      openUrlInNewTab('https://designeasy.ai/privacy-policy')
                    }
                  >
                    Privacy Policy
                  </div>
                  .
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
