import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  title: 'Terms and conditions',
  alternates: {
    canonical: 'https://designeasy.xyz/terms-and-conditions',
  },
};

const terms = [
  {
    title: '1. Definitions',
    content: [
      {
        subtitle: '"Company"',
        text: 'Refers to AUTOTME SOFTWARE PRIVATE LIMITED, the owner and operator of DesignEasy.',
      },
      {
        subtitle: '"Service"',
        text: 'Refers to the AI-powered ad creation platform provided by DesignEasy.',
      },
      {
        subtitle: '"User"',
        text: 'Refers to any individual or entity accessing or using the Service.',
      },
      {
        subtitle: '"Subscription Plan"',
        text: 'Refers to the paid plans available for using premium features of the Service.',
      },
    ],
  },
  {
    title: '2. Eligibility',
    content: [
      {
        text: 'You must be at least 18 years old to use DesignEasy. By using the Service, you represent and warrant that you have the legal capacity to enter into this agreement.',
      },
    ],
  },
  {
    title: '3. Account Registration',
    content: [
      {
        text: 'You must provide accurate information when creating an account.',
      },
      {
        text: 'You are responsible for maintaining the confidentiality of your login credentials.',
      },
      {
        text: 'You agree to notify us immediately of any unauthorized use of your account.',
      },
    ],
  },
  {
    title: '4. Use of Service',
    content: [
      {
        text: 'You may use DesignEasy to create, customize, and download ad creatives.',
      },
      {
        text: 'The Service must not be used for illegal, fraudulent, or infringing activities.',
      },
      {
        text: 'We reserve the right to suspend or terminate your account if you violate these terms.',
      },
    ],
  },
  {
    title: '5. Subscription & Payment',
    content: [
      {
        text: 'Users can access limited features for free and must subscribe to a paid plan for unlimited access.',
      },
      {
        text: 'Subscription fees are billed monthly or annually as per the chosen plan.',
      },
      { text: 'Payments are non-refundable except as required by law.' },
      {
        text: 'The Company may change pricing at any time, with notice to existing subscribers.',
      },
    ],
  },
  {
    title: '6. Intellectual Property',
    content: [
      {
        text: 'DesignEasy retains ownership of all proprietary software, algorithms, and branding.',
      },
      {
        text: 'Users retain ownership of the final designs they create using the Service.',
      },
      {
        text: 'You grant DesignEasy a limited license to use submitted content for improving AI models and marketing purposes unless explicitly opted out.',
      },
    ],
  },
  {
    title: '7. Limitation of Liability',
    content: [
      {
        text: 'DesignEasy is provided "as-is" without warranties of any kind.',
      },
      {
        text: 'We are not liable for any loss or damages arising from the use or inability to use the Service.',
      },
      {
        text: 'Users are responsible for ensuring compliance with advertising platform policies.',
      },
    ],
  },
  {
    title: '8. Termination',
    content: [
      {
        text: 'We may terminate or suspend your access to the Service at our discretion, without liability, if you violate these Terms.',
      },
      {
        text: 'You may cancel your subscription at any time, but no refunds will be issued for the remaining billing cycle.',
      },
    ],
  },
  {
    title: '9. Privacy & Data Protection',
    content: [
      { text: 'Your data is processed in accordance with our Privacy Policy.' },
      {
        text: 'We do not sell or share user data with third parties without consent.',
      },
    ],
  },
  {
    title: '10. Modifications to Terms',
    content: [
      { text: 'We reserve the right to update these Terms at any time.' },
      {
        text: 'Continued use of the Service after changes constitutes acceptance of the updated Terms.',
      },
    ],
  },
  {
    title: '11. Governing Law & Dispute Resolution',
    content: [
      { text: 'These Terms are governed by the laws of India.' },
      {
        text: 'Any disputes shall be resolved through arbitration in Noida, Uttar Pradesh.',
      },
    ],
  },
  {
    title: '12. Contact Information',
    content: [
      { text: 'For any questions or concerns, please contact us at:' },
      { subtitle: 'Email:', text: '<EMAIL>' },
      {
        subtitle: 'Company Address:',
        text: 'AUTOTME SOFTWARE PRIVATE LIMITED, Noida Special Economic Zone, Ground Floor Desk No 93, D9 DROM Workshala Private Limited, Sector 3 Noida, Gautam Buddha Nagar, Uttar Pradesh, India 201301',
      },
    ],
  },
];

const PrivacyPolicy = () => {
  return (
    <div className="">
      <h1 className="mt-6 text-2xl font-medium">Terms and Conditions</h1>
      <p className="text-sm font-medium mt-3">Last Updated: 4th Feb 2025</p>
      <p className="mt-5 text-base">
        Welcome to DesignEasy! These Terms and Conditions govern your use of the
        DesignEasy platform and services. By accessing or using DesignEasy, you
        agree to comply with these terms.
      </p>
      {terms.map((item, index) => (
        <div key={index}>
          <h2 className="mt-8 text-xl">{item.title}</h2>
          {item.content.map((contentItem, innerIndex) => (
            <div key={innerIndex} className="mt-4">
              {contentItem.subtitle ? (
                <h3 className="text-base font-medium">
                  {contentItem.subtitle}
                </h3>
              ) : null}
              <p className="text-sm mt-2">{contentItem.text}</p>
            </div>
          ))}
        </div>
      ))}
      <p className="mt-8 text-base">
        By using DesignEasy, you acknowledge that you have read, understood, and
        agreed to these Terms and Conditions.
      </p>
      {/* <ContactUsComp /> */}
    </div>
  );
};

export default PrivacyPolicy;
