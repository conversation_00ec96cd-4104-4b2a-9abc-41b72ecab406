import React from 'react';
import Logo from '@/images/logo/logo.svg';
import Title from '@/images/logo/title.svg';
import Footer from '@/components/common/Footer';

const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div>
      <div className=" p-4 md:p-8 min-h-screen flex flex-col ">
        <header className=" max-w-[1280px] mx-auto w-full bg-background flex flex-col">
          <div className=" self-start bg-muted rounded-md flex items-center justify-center">
            <div className=" flex gap-2 md:gap-4 items-center ">
              <Logo className=" text-gray-900 h-[20px] md:h-[30px] lg:h-[40px] " />
              <Title className=" text-gray-900 h-7 md:h-10 lg:h-14 " />
            </div>
          </div>
        </header>
        <div className=" mt-4 md:mt-8 flex-1 flex flex-col ">
          <main className=" max-w-[1280px] mx-auto flex-1 flex flex-col ">
            {children}
          </main>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Layout;
