import BannerMaker from '@/components/main/BannerMaker';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const BannerMakerPage = () => {
  return (
    <>
      <div className="h-screen w-full">
        <section className="w-full min-h-full bg-gradient-to-b from-cyan-500 to-blue-500 p-8 flex flex-col items-center border-1 border-b border-slate-300">
          <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
            <Image
              src="/images/bannerbot-logo.png"
              width="80"
              height="80"
              alt="bannerbot-logo"
              priority
            />
          </div>
          <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
            <p className="text-white text-3xl text-center">DesignEasy </p>
            <h1 className="text-white text-xl mt-4 text-center">
              AI Banner Maker
            </h1>
            <BannerMaker />
          </div>
        </section>
        <section className="w-full bg-gradient-to-b from-blue-500 to-violet-500 p-8 flex flex-col items-center border-1 border-b border-slate-300">
          <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
            <h1 className="text-white text-4xl mt-4 text-center">About Us</h1>
            <p className="text-white p-8 text-center">
              We are the same team behind{' '}
              <Link
                href="https://groweasy.ai/"
                target="_blank"
                className="underline"
              >
                GrowEasy
              </Link>
            </p>
            <p className="text-white text-center">
              GrowEasy is an AI-powered lead generation platform that assists
              small businesses in acquiring leads through marketing campaigns on
              Facebook and Instagram.
            </p>
            <p className="text-white text-center mt-8">
              Contact Us: <span className="font-medium"><EMAIL></span>,{' '}
              <span className="font-medium"><EMAIL></span>
            </p>
          </div>
        </section>
        <section className="w-full min-h-full bg-gradient-to-b from-blue-500 to-violet-500 p-8 flex flex-col sm:flex-row justify-center items-center border-1 border-b border-slate-300">
          <div className="sm:w-1/2 flex flex-col items-center justify-center">
            <Image
              src="/images/bannerbot-main.jpg"
              alt="banner-bot"
              width="315"
              height="648"
            />
          </div>
          <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
            <h1 className="text-white text-4xl mt-4 text-center">
              What is DesignEasy?
            </h1>
            <p className="text-white p-8">
              DesignEasy is an instant AI-Powered promotional banner generator.
              With DesignEasy, you can create professional and eye-catching
              banners in just a few clicks.
            </p>
            <p className="text-white px-8">
              Our easy-to-use interface and customizable options allow you to
              create stunning banners that perfectly align with your brand and
              message. Whether you need landscape (16:9), square (1:1), or
              portrait (3:4) banners, we{"'"}ve got you covered.
            </p>
          </div>
        </section>
        <section className="w-full min-h-full bg-gradient-to-b from-violet-500 to-fuchsia-500 p-8 flex flex-col sm:flex-row justify-center items-center border-1 border-b border-slate-300">
          <div className="sm:w-1/2 flex flex-col items-center justify-center">
            <Image
              src="/images/bannerbot-edit-texts.jpg"
              alt="banner-bot"
              width="315"
              height="648"
            />
          </div>
          <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
            <p className="text-white p-8">
              Take control of your promotional efforts by using DesignEasy{"'"}s
              powerful features to create your own banner.
            </p>
            <p className="text-white px-8">
              Our AI-powered tool utilizes advanced algorithms to deliver
              stunning and high-quality banners that meet your specific needs.
              You can customize the texts, colors, gradients, fonts, and images
              to make your banners truly unique and engaging.
            </p>
          </div>
        </section>
        <section className="w-full min-h-full bg-gradient-to-b from-fuchsia-500 to-purple-500 p-8 flex flex-col sm:flex-row justify-center items-center border-1 border-b border-slate-300">
          <div className="sm:w-1/2 flex flex-col items-center justify-center">
            <Image
              src="/images/bannerbot-edit-image.jpg"
              alt="banner-bot"
              width="315"
              height="648"
            />
          </div>
          <div className="w-full sm:w-1/2 flex flex-col items-center justify-center">
            <p className="text-white p-8">
              Our tool is specifically designed to help you craft
              attention-grabbing social media banners that drive engagement and
              generate buzz for your brand or business.
            </p>
            <p className="text-white px-8">
              Choose from our vast collection of templates, carefully crafted by
              our design experts, to kickstart your creative process. Whether
              you{"'"}re promoting a product, an event, or a special offer, our
              templates provide a solid foundation to get you started.
            </p>
            <div className="mt-6">
              <a href="https://play.google.com/store/apps/details?id=xyz.bannerbot&utm_source=website">
                <Image
                  src="/images/google-play.svg"
                  width="160"
                  height="48"
                  alt="google-play"
                />
              </a>
            </div>
          </div>
        </section>
        <section className="w-full bg-slate-800 p-8 flex flex-col justify-center items-center">
          <p className="text-white p-8 text-center">
            With DesignEasy, you have the freedom to design your own banner
            effortlessly. Boost your social media presence with captivating
            banners created using our tool.
          </p>
          <p className="text-white px-8 text-center">
            Elevate your promotional efforts with DesignEasy - the go-to tool
            for designing and creating your own custom banners. Empower your
            brand, captivate your audience, and make a lasting impression with
            stunning banners tailored to your unique vision and requirements.{' '}
          </p>
          <p className="text-white px-8 text-center">
            <a
              href="https://play.google.com/store/apps/details?id=xyz.bannerbot&utm_source=website"
              className="underline"
            >
              Download now
            </a>{' '}
            and experience the convenience, speed, and creativity of
            AI-generated banners.
          </p>
          <p className="text-white p-8 text-center">
            All Rights Reserved | © Copyright 2023 | &nbsp;
            <Link href="/privacy-policy" className="underline">
              Privacy Policy
            </Link>
          </p>
        </section>
      </div>
    </>
  );
};

export default BannerMakerPage;
