/* eslint-disable @next/next/no-img-element */
'use client';

import { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { fitTextInContainer } from '@/utils/fp_video';
import { IFpVideoDataV1 } from '@/remotion/types';

export default function AnimatedPage() {
  // Create a global reference for the timeline
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);

  const [customResetKey, setCustomResetKey] = useState(1);
  const [videoData, setVideoData] = useState<IFpVideoDataV1>({
    video_caption:
      'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
    fps: 60,
    template_id: 'p3',
    size: 'portrait',
    width: 720,
    height: 1280,
    branding: {
      brand_name: 'GrowEasy',
      website: 'www.groweasy.ai',
      logo: {
        url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
        width: 100,
        height: 100,
      },
    },
    base_audio: {
      url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/bg-musics/upbeat-reel-commercial-advertising-music-253432.mp3',
    },
    base_assets: [
      {
        type: 'video',
        url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/17955818-sd_540_960_30fps.mp4',
      },
    ],
    scenes: [
      {
        scene_id: 1,
        assets: [
          {
            type: 'image',
            url: 'https://images.pexels.com/photos/19090/pexels-photo.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          },
        ],
        texts: [
          {
            value: 'Efficient Keyword Planning',
          },
        ],
      },
      {
        scene_id: 2,
        assets: [
          {
            type: 'image',
            url: 'https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          },
        ],
        texts: [
          {
            value: 'Enhance Ad Targeting',
          },
        ],
      },
      {
        scene_id: 3,
        assets: [
          {
            type: 'image',
            url: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          },
        ],
        texts: [
          {
            value: 'Seamless Campaign Retrieval',
          },
        ],
      },
      {
        scene_id: 4,
        assets: [
          {
            type: 'image',
            url: 'https://images.pexels.com/photos/292999/pexels-photo-292999.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          },
        ],
        texts: [
          {
            value: 'Streamlined Management',
          },
        ],
      },
      {
        scene_id: 5,
        assets: [],
        texts: [
          {
            value: 'Start Optimizing Today',
          },
          {
            value: 'Visit: www.groweasy.ai',
          },
        ],
      },
      {
        scene_id: 6,
        assets: [
          {
            type: 'image',
            url: 'https://images.pexels.com/photos/1478442/pexels-photo-1478442.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          },
        ],
        texts: [
          {
            value: 'Access Advanced Tools',
          },
        ],
      },
      {
        scene_id: 7,
        texts: [
          {
            value: 'AI Powered lead generation',
          },
          {
            value: 'https://groweasy.ai',
          },
        ],
      },
    ],
  });

  // Generate duplicate items for columns
  const mainColumnItems = Array(4).fill(null);
  const sideColumnItems = Array(6).fill(null);

  const init = (customVideoData: string) => {
    if (!customVideoData) return;
    setCustomResetKey((prev) => prev + 1);
    setVideoData(JSON.parse(customVideoData));
  };

  useEffect(() => {
    startAnimation();
  }, [videoData]);

  const startAnimation = () => {
    // Kill any existing animations
    if (!pageTimelineRef.current) {
      // Create the timeline only if it doesn't exist
      pageTimelineRef.current = gsap.timeline({ repeat: 0 });
    } else {
      // If the timeline exists, clear it before reusing
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    const finalBrandScene = document.getElementById('final-brand-scene');
    const main_column = document.getElementById('main_column');
    const container = document.getElementById('container');
    const super_container = document.getElementById('super-container');
    const show_text_1 = document.getElementById('show-text-1');
    const show_text_2 = document.getElementById('show-text-2');
    const show_text_3 = document.getElementById('show-text-3');
    const show_text_shadow_1 = document.getElementById('show-text-shadow-1');
    const show_text_shadow_2 = document.getElementById('show-text-shadow-2');
    const show_text_shadow_3 = document.getElementById('show-text-shadow-3');

    const slowAnimationBy = 1;

    // let startTime: number;
    // let endTime: number;

    timeline
      .to(main_column, {
        y: -2347,
        onStart: () => {
          // startTime = performance.now();
        },
        duration: slowAnimationBy * 4,
        ease: 'power2.out',
      })
      .to(container, {
        rotate: 0,
        scale: 2,
        duration: slowAnimationBy * 0.5,
      })
      .to(
        main_column,
        {
          x: 170,
          y: -2530,
          duration: slowAnimationBy * 0.5,
        },
        `-=${slowAnimationBy * 0.5}`,
      )
      .to(main_column, {
        x: -170,
        duration: slowAnimationBy * 0.8,
        ease: 'linear',
        delay: slowAnimationBy * 3,
      })
      .set(show_text_1, {
        scale: 2,
        opacity: 0,
      })
      .to([show_text_1, show_text_shadow_1], {
        opacity: 1,
        scale: 1,
        duration: slowAnimationBy * 0.5,
      })
      .to(main_column, {
        y: -2190,
        duration: slowAnimationBy * 0.5,
        delay: slowAnimationBy * 2,
        ease: 'linear',
      })
      .set(show_text_2, {
        scale: 2,
        opacity: 0,
      })
      .to([show_text_2, show_text_shadow_2], {
        opacity: 1,
        scale: 1,
        duration: slowAnimationBy * 0.5,
      })
      .to(main_column, {
        y: -1850,
        duration: slowAnimationBy * 0.5,
        delay: slowAnimationBy * 2,
        ease: 'linear',
      })
      .set(show_text_3, {
        scale: 2,
        opacity: 0,
      })
      .to([show_text_3, show_text_shadow_3], {
        opacity: 1,
        scale: 1,
        duration: slowAnimationBy * 0.5,
      })
      .set(finalBrandScene, {
        scale: 0.2,
        opacity: 0,
      })
      .to(super_container, {
        x: 350,
        rotate: -35,
        scale: 0.5,
        delay: slowAnimationBy * 2,
        duration: slowAnimationBy * 1,
      })
      .to(
        main_column,
        {
          y: -5000,
          duration: slowAnimationBy * 1.3,
          ease: 'power1.in',
        },
        `-=${slowAnimationBy * 1}`,
      )
      .to(
        super_container,
        {
          opacity: 0,
          duration: slowAnimationBy * 0.5,
        },
        `-=${slowAnimationBy * 0.5}`,
      )
      .to(
        finalBrandScene,
        {
          opacity: 1,
          duration: slowAnimationBy * 0.5,
          scale: 1,
          ease: 'power1.out',
          onComplete: () => {
            // endTime = performance.now();
            // console.log(((endTime - startTime) / 1000).toFixed(2) + ' s');
          },
        },
        `-=${slowAnimationBy * 0.6}`,
      )
      .to(finalBrandScene, {
        duration: 3,
      });

    if (typeof window !== 'undefined') {
      const originalSeek = pageTimelineRef.current.seek;

      pageTimelineRef.current.asyncSeek = async function (
        time: string | number,
        suppressEvents?: boolean,
      ) {
        originalSeek.apply(this, [time, suppressEvents]);
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).pageTimeline = pageTimelineRef.current;
    }
  };

  // Expose functions globally for iframe and puppeteer access
  useEffect(() => {
    // gsap.globalTimeline.pause();
    if (typeof window !== 'undefined') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).init = init;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).pageTimeline = pageTimelineRef.current;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).gsap = gsap;
    }

    // init();

    // Cleanup function
    return () => {
      if (pageTimelineRef.current) {
        pageTimelineRef.current.kill();
        pageTimelineRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    // steps to make a font size variable
    // add a container around the tag ('p', 'h1',...) and give some fixed height to it
    // now add the className 'text-fit' or 'text-and-container-fit' to the tag ('p', 'h1',...) as per use
    // 'text-fit' just make the text change font size
    // 'text-and-container-fit' changes the text size also at last changes the container size maximum being the already set height

    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');

    // console.log('updated font size');
  }, [videoData]);

  // Template item component
  const TemplateItem = () => {
    return (
      <div className="item shrink-0 grid gap-10">
        <div className="grid grid-cols-2 gap-10">
          <img
            src={videoData?.scenes[0]?.assets?.[0]?.url || ''}
            className="product-image-0 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
          <img
            src={videoData?.scenes[1]?.assets?.[0]?.url || ''}
            className="product-image-1 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
          <img
            src={videoData?.scenes[2]?.assets?.[0]?.url || ''}
            className="product-image-2 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
          <img
            src={videoData?.scenes[3]?.assets?.[0]?.url || ''}
            className="product-image-3 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
          <div className="w-[300px] h-[300px] bg-green-400 rounded-3xl p-6 flex flex-col justify-between text-white">
            <div className="flex items-center gap-2">
              {videoData?.branding?.logo?.url && (
                <img
                  src={videoData?.branding?.logo?.url}
                  className="brand-logo shrink-0 rounded-xl w-12 h-12 object-cover"
                  alt="Brand Logo"
                />
              )}
              <div className=" h-[48px] flex items-center ">
                <p className="brand-name text-3xl font-semibold text-white text-fit ">
                  {videoData?.branding?.brand_name}
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className=" h-[120px] flex items-end ">
                <h3 className="ad-text-0 text-5xl font-bold font-mono italic text-fit ">
                  {videoData?.scenes?.[4]?.texts?.[0]?.value || ''}
                </h3>
              </div>
              <div className=" h-[36px] ">
                <h5 className="ad-text-1 text-3xl font-medium text-and-container-fit ">
                  {videoData?.scenes?.[4]?.texts?.[1]?.value || ''}
                </h5>
              </div>
            </div>
          </div>
          <img
            src={videoData?.scenes[5]?.assets?.[0]?.url || ''}
            className="product-image-5 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
        </div>
      </div>
    );
  };

  // Main template item component
  const MainTemplateItem = () => {
    return (
      <div className="item shrink-0 grid gap-10">
        <div className="grid grid-cols-2 gap-10">
          <img
            src={videoData?.scenes[0]?.assets?.[0]?.url || ''}
            className="product-image-0 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
          <div className="relative rounded-3xl overflow-hidden">
            <div
              id="show-text-shadow-3"
              className=" absolute inset-0 bg-gradient-to-b from-black/60 to-50% to-transparent opacity-0 "
            />
            <div className="absolute left-6 top-6">
              <div className=" h-[72px] pr-6 ">
                <h2
                  id="show-text-3"
                  className="scene-text-1 [text-shadow:_0_2px_1px_rgb(0_0_0)] text-white font-bold text-7xl tracking-wider drop-shadow-2xl opacity-0 text-fit "
                >
                  {videoData?.scenes?.[5]?.texts?.[0]?.value || ''}
                </h2>
              </div>
            </div>
            <img
              src={videoData?.scenes[1]?.assets?.[0]?.url || ''}
              className="product-image-1 object-cover w-[300px] h-[300px] shadow-md border-2 border-gray-400/35"
              alt="Product"
            />
          </div>
          <img
            src={videoData?.scenes[2]?.assets?.[0]?.url || ''}
            className="product-image-2 object-cover w-[300px] h-[300px] rounded-3xl shadow-md border-2 border-gray-400/35"
            alt="Product"
          />
          <div className="relative rounded-3xl overflow-hidden ">
            <div
              id="show-text-shadow-2"
              className=" absolute inset-0 bg-gradient-to-b from-black/60 to-50% to-transparent opacity-0 "
            />
            <div className="absolute left-6 top-6">
              <div className=" h-[72px] pr-6 ">
                <h2
                  id="show-text-2"
                  className="scene-text-3 [text-shadow:_0_2px_1px_rgb(0_0_0)] text-white font-bold text-7xl tracking-wider drop-shadow-2xl opacity-0 text-fit"
                >
                  {videoData?.scenes?.[3]?.texts?.[0]?.value || ''}
                </h2>
              </div>
            </div>
            <img
              src={videoData?.scenes[3]?.assets?.[0]?.url || ''}
              className="product-image-3 object-cover w-[300px] h-[300px] shadow-md border-2 border-gray-400/35"
              alt="Product"
            />
          </div>
          <div className="w-[300px] h-[300px] bg-green-400 rounded-3xl p-6 flex flex-col justify-between gap-6 text-white relative">
            <div className="flex items-center gap-2">
              {videoData?.branding?.logo?.url && (
                <img
                  src={videoData?.branding?.logo?.url}
                  className="brand-logo shrink-0 rounded-xl w-12 h-12 object-cover"
                  alt="Brand Logo"
                />
              )}
              <div className=" h-[48px] flex items-center ">
                <p className="brand-name text-3xl font-semibold text-white text-fit ">
                  {videoData?.branding?.brand_name}
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div className=" h-[120px] flex items-end ">
                <h3 className="ad-text-0 text-5xl font-bold font-mono italic text-fit ">
                  {videoData?.scenes?.[4]?.texts?.[0]?.value || ''}
                </h3>
              </div>
              <div className=" h-[36px] ">
                <h5 className="ad-text-1 text-3xl font-medium text-and-container-fit ">
                  {videoData?.scenes?.[4]?.texts?.[1]?.value || ''}
                </h5>
              </div>
            </div>
          </div>
          <div className="relative rounded-3xl overflow-hidden">
            <div
              id="show-text-shadow-1"
              className=" absolute inset-0 bg-gradient-to-b from-black/60 to-50% to-transparent opacity-0 "
            />
            <div className="absolute left-6 top-6 ">
              <div className=" h-[72px] pr-6 ">
                <h2
                  id="show-text-1"
                  className="scene-text-5 [text-shadow:_0_2px_1px_rgb(0_0_0)] text-white font-bold text-7xl tracking-wider drop-shadow-2xl opacity-0 text-fit "
                >
                  {videoData?.scenes?.[1]?.texts?.[0]?.value || ''}
                </h2>
              </div>
            </div>
            <img
              src={videoData?.scenes[5]?.assets?.[0]?.url || ''}
              className="product-image-5 object-cover w-[300px] h-[300px] shadow-md border-2 border-gray-400/35"
              alt="Product"
            />
          </div>
        </div>
      </div>
    );
  };

  // Final brand scene component
  const FinalBrandScene = () => {
    const scene = videoData?.scenes[6];
    return (
      <div
        id="final-brand-scene"
        className="bg-green-400 absolute inset-0 m-10 rounded-3xl flex flex-col items-center justify-center p-10 opacity-0"
      >
        <div className="flex gap-4 items-center">
          {videoData?.branding?.logo?.url && (
            <img
              src={videoData?.branding?.logo?.url}
              className="brand-logo shrink-0 rounded-3xl w-24 h-24 object-cover"
              alt="Brand Logo"
            />
          )}
          <div className=" h-24 flex items-center ">
            <p className="brand-name text-7xl font-semibold text-white text-fit ">
              {videoData?.branding?.brand_name}
            </p>
          </div>
        </div>
        <div className="text-5xl italic text-center mt-28 text-white space-y-2 ">
          <div className=" h-[96px] ">
            <p className="ad-text-0 text-and-container-fit">
              {scene?.texts?.[0]?.value || ''}
            </p>
          </div>
          <div className=" h-[96px] ">
            <p className="ad-text-1 text-and-container-fit">
              {scene?.texts?.[1]?.value || ''}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      key={customResetKey}
      className="bg-white flex flex-col items-center justify-center text-lime-900 relative w-full h-screen overflow-hidden"
    >
      <div id="super-container" className="">
        <div
          id="container"
          className="shrink-0 w-[720px] h-[1280px] flex justify-center relative rotate-12"
        >
          <div
            id="main_column"
            className="absolute top-full -translate-y-full left-0 flex flex-col gap-10 p-10"
          >
            <TemplateItem />
            <MainTemplateItem />

            {/* Duplicate items for main column */}
            {mainColumnItems.map((_, index) => (
              <TemplateItem key={`main-item-${index}`} />
            ))}

            <div
              id="side-column-0"
              className="absolute top-full -translate-y-full left-full w-[720px] -ml-10 flex flex-col gap-10 p-10"
            >
              {/* Side column 0 items */}
              {sideColumnItems.map((_, index) => (
                <TemplateItem key={`side0-item-${index}`} />
              ))}
            </div>

            <div
              id="side-column-1"
              className="absolute top-full -translate-y-full right-full w-[720px] -mr-10 flex flex-col gap-10 p-10"
            >
              {/* Side column 1 items */}
              {sideColumnItems.map((_, index) => (
                <TemplateItem key={`side1-item-${index}`} />
              ))}
            </div>
          </div>
        </div>
      </div>

      <FinalBrandScene />
    </div>
  );
}
