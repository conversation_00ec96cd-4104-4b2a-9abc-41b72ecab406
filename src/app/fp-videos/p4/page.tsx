'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { fitTextInContainer } from '@/utils/fp_video';
import Image from 'next/image';
import { IFpVideoDataV1 } from '@/remotion/types';

export default function AnimatedPage() {
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const [customResetKey, setCustomResetKey] = useState(1);
  const [videoData, setVideoData] = useState<IFpVideoDataV1>({
    video_caption:
      'Get Started with Google Ads API – Transform Your Advertising Strategy with GrowEasy!',
    fps: 60,
    template_id: 'p3',
    size: 'portrait',
    width: 720,
    height: 1280,
    branding: {
      brand_name: 'GrowEasy',
      website: 'www.groweasy.ai',
      logo: {
        url: 'https://groweasy.ai/cdn-cgi/image/width=120,height=120,q=100,fit=pad/https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/1740666973802-apple-touch-icon.png?v=1',
        width: 100,
        height: 100,
      },
    },
    base_audio: {
      url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/video-maker/bg-musics/upbeat-reel-commercial-advertising-music-253432.mp3',
    },
    base_assets: [
      {
        type: 'video',
        url: 'https://bannerbot-public.s3.ap-south-1.amazonaws.com/uploaded-assets/17955818-sd_540_960_30fps.mp4',
      },
    ],
    scenes: [
      {
        scene_id: 1,
        texts: [
          {
            value: 'Looking for the perfect luxury gateway?',
          },
        ],
      },
      {
        scene_id: 2,
        texts: [
          {
            value: 'Premium 4BR Pool Villa Near Mandwa Jetty',
          },
          {
            value:
              'Welcome to Hearthstone by Getaway Stays - an exclusive 4 bedroom luxury villa with a private pool.',
          },
        ],
      },
      {
        scene_id: 3,
        texts: [
          {
            value: 'Ideal for Families & Corporate Retreats!',
          },
          {
            value:
              'Perfect for familty vacation, corporate offistes, weekend gateways, and private pool parties!',
          },
        ],
      },
      {
        scene_id: 4,
        texts: [
          {
            value: 'Book Your Stay Now!',
          },
          {
            value:
              'Enjoy world-class comfort, scenic surroundings, and a peaceful retreat just a short ride from Mumbai.',
          },
        ],
      },
      {
        scene_id: 5,
        texts: [
          {
            value: '+911234567890 / www.getawaystays.co.in',
          },
        ],
      },
    ],
  });

  const init = (customVideoData: string) => {
    if (!customVideoData) return;
    setCustomResetKey((prev) => prev + 1);
    setVideoData(JSON.parse(customVideoData));
  };

  const preloadVideo = (preloadDuration: number) => {
    return new Promise((resolve, reject) => {
      const video = videoRef.current;
      if (!video) {
        return reject(new Error('Video element not found'));
      }

      // Pause the video while preloading
      video.pause();

      // Start loading the video
      video.load();

      // Force preloading by "playing" the video silently
      video.muted = true;
      video.playbackRate = 2.0; // Speed up loading

      // Set up a timeout in case preloading takes too long
      const preloadTimeout = setTimeout(() => {
        reject(new Error('Preloading timed out'));
      }, 60000); // 1 minute timeout

      // Start playing to force buffer loading
      const playPromise = video.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            // We're playing, so the browser is now loading the video
            const checkBuffer = setInterval(() => {
              if (video.buffered.length > 0) {
                const bufferedEnd = video.buffered.end(
                  video.buffered.length - 1,
                );

                if (
                  bufferedEnd >= preloadDuration ||
                  bufferedEnd >= video.duration
                ) {
                  // Successfully preloaded the required duration
                  video.pause();
                  video.currentTime = 0;
                  video.playbackRate = 1.0; // Reset playback rate
                  clearInterval(checkBuffer);
                  clearTimeout(preloadTimeout);
                  resolve(true);
                }
              }
            }, 500);
          })
          .catch((error) => {
            clearTimeout(preloadTimeout);
            reject(error);
          });
      } else {
        clearTimeout(preloadTimeout);
        reject(new Error('Browser prevented video playback'));
      }
    });
  };

  const syncVideoWithTimeline = async (seekTime: number) => {
    return new Promise((resolve, reject) => {
      const main_background_video = document.getElementById(
        'main-background-video',
      );

      if (main_background_video) {
        const videoElement = main_background_video as HTMLVideoElement;

        {
          //**
          // sometimes if the difference between currentTime and seekTime is too low then the event 'seeked' wont be called
          // so need to handle those cases using a timeout
          // */
        }

        const onSeeked = () => {
          videoElement.removeEventListener('seeked', onSeeked);
          clearTimeout(timeoutId);
          resolve('');
        };

        const timeoutId = setTimeout(() => {
          videoElement.removeEventListener('seeked', onSeeked);
          resolve('');
        }, 200);

        videoElement.addEventListener('seeked', onSeeked);
        videoElement.currentTime = seekTime;
        // videoElement.getVideoPlaybackQuality();
      } else {
        reject(new Error('Video element not found'));
      }
    });
  };

  const playVideo = () => {
    const main_background_video = document.getElementById(
      'main-background-video',
    );

    if (main_background_video) {
      (main_background_video as HTMLVideoElement).play();
    }
  };

  const pauseVideo = () => {
    const main_background_video = document.getElementById(
      'main-background-video',
    );

    if (main_background_video) {
      (main_background_video as HTMLVideoElement).pause();
    }
  };

  const startAnimation = useCallback(() => {
    // Kill any existing animations
    if (!pageTimelineRef.current) {
      // Create the timeline only if it doesn't exist
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
        // onUpdate: syncVideoWithTimeline,
      });
    } else {
      // If the timeline exists, clear it before reusing
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    // all the DOM references

    const scene_1 = document.getElementById('scene-1');
    const scene_2 = document.getElementById('scene-2');
    const scene_3 = document.getElementById('scene-3');
    const scene_4 = document.getElementById('scene-4');
    const scene_5 = document.getElementById('scene-5');

    // the timeline animation
    timeline
      .to(scene_1, {
        opacity: 1,
        ease: 'power1.inOut',
        onStart: () => {},
      })
      .set(scene_1, {
        delay: 3,
        display: 'none',
      })
      .to(scene_2, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .set(scene_2, {
        delay: 3,
        display: 'none',
      })
      .to(scene_3, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .set(scene_3, {
        delay: 3,
        display: 'none',
      })
      .to(scene_4, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .set(scene_4, {
        delay: 3,
        display: 'none',
      })
      .to(scene_5, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .to(scene_5, {
        delay: 3,
        opacity: 1,
      });

    if (typeof window !== 'undefined') {
      const originalSeek = pageTimelineRef.current.seek;
      const originalPlay = pageTimelineRef.current.play;
      const originalPause = pageTimelineRef.current.pause;

      pageTimelineRef.current.seek = function (
        time: string | number,
        suppressEvents?: boolean,
      ) {
        syncVideoWithTimeline(Number(time));
        return originalSeek.apply(this, [time, suppressEvents]);
      };

      pageTimelineRef.current.asyncSeek = async function (
        time: string | number,
        suppressEvents?: boolean,
      ) {
        await syncVideoWithTimeline(Number(time));
        originalSeek.apply(this, [time, suppressEvents]);
      };

      pageTimelineRef.current.play = function () {
        playVideo();
        return originalPlay.apply(this);
      };

      pageTimelineRef.current.pause = function () {
        pauseVideo();
        return originalPause.apply(this);
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).pageTimeline = pageTimelineRef.current;
    }
  }, []);

  // Expose functions globally for iframe and puppeteer access
  useEffect(() => {
    // gsap.globalTimeline.pause();
    if (typeof window !== 'undefined') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).init = init;

      // (window as any).pageTimeline = pageTimelineRef.current;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).gsap = gsap;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).preloadVideoToTime = async (duration: number) => {
        try {
          await preloadVideo(duration);
          return { success: true };
        } catch (error) {
          // Return the error details rather than just logging them
          if (error instanceof Error)
            return {
              success: false,
              error: {
                message: error.message,
                name: error.name,
              },
            };
          else
            return {
              success: false,
              error: {
                message: 'something went wrong in preloading the video',
              },
            };
        }
      };
    }

    // init();

    // Cleanup function
    return () => {
      if (pageTimelineRef.current) {
        pageTimelineRef.current.kill();
        pageTimelineRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    /**
     * steps to make a font size variable
     * add a container around the tag ('p', 'h1',...) and give some fixed height to it
     * now add the className 'text-fit' or 'text-and-container-fit' to the tag ('p', 'h1',...) as per use
     * 'text-fit' just make the text change font size
     * 'text-and-container-fit' changes the text size also at last changes the container size maximum being the already set height
     */

    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');
  }, [videoData]);

  useEffect(() => {
    startAnimation();
  }, [videoData, startAnimation]);

  return (
    <div
      key={customResetKey}
      className="bg-white flex flex-col items-center justify-center text-lime-900 relative w-full h-screen overflow-hidden"
      id="container"
    >
      <video
        className=" w-full h-full "
        id="main-background-video"
        preload="auto"
        muted
        ref={videoRef}
      >
        <source src={videoData.base_assets?.[0].url} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {videoData.scenes.slice(0, -1).map((scene, index) => (
        <div
          className={` absolute inset-0 flex items-center justify-center text-white p-8 opacity-0 `}
          id={`scene-${index + 1}`}
          key={`scene-${index + 1}`}
        >
          <div className="bg-black/50 p-6 py-10 rounded-3xl w-full ">
            <div className=" h-[180px] ">
              <p className=" text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit ">
                {scene.texts?.[0].value}
              </p>
            </div>
            {scene.texts?.length == 2 && (
              <div className=" h-[160px]  mt-8 ">
                <p className=" text-4xl text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit ">
                  {scene.texts?.[1].value}
                </p>
              </div>
            )}
          </div>
        </div>
      ))}

      <div
        className={` absolute inset-0 flex items-center justify-center text-white p-8 opacity-0 `}
        id={`scene-5`}
        key={`scene-5`}
      >
        <div className="bg-black/50 p-6 py-10 rounded-3xl w-full flex flex-col gap-8 ">
          {videoData?.branding?.logo && (
            <div className=" flex justify-center ">
              <Image
                src={videoData.branding.logo.url}
                width={120}
                height={120}
                alt="brand logo"
                className=" rounded-[30px] "
              />
            </div>
          )}
          <div className=" h-[180px] ">
            <p className=" text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit ">
              {videoData?.branding?.brand_name}
            </p>
          </div>
          <div className=" h-[160px] ">
            <p className=" text-4xl text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit ">
              {videoData.scenes[4].texts?.[0].value}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
