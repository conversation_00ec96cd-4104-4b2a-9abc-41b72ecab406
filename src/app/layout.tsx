import React from 'react';
import type { Metadata } from 'next';
import { Pop<PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import ReactQueryProvider from '@/components/ReactQueryProvider';
import UserProvider from '@/components/context/UserProvider';
import Toaster from '../modules/toast';

const poppins = Poppins({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800'],
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: {
    default: 'DesignEasy - AI Ad Creator',
    template: '%s | DesignEasy',
  },
  description:
    'Create stunning AI-powered ad banners and videos with DesignEasy. Leverage AI to generate professional, high-converting marketing creatives effortlessly.',
  keywords: [
    'DesignEasy',
    'AI-banner-maker',
    'AI-powered ads',
    'AI-video-generator',
    'GrowEasy',
    'Ad design automation',
  ],
  openGraph: {
    title: 'DesignEasy - AI Ad Creator',
    description:
      'Create stunning AI-powered ad banners and videos with DesignEasy. Leverage AI to generate professional, high-converting marketing creatives effortlessly.',
    url: 'https://designeasy.ai',
    siteName: 'DesignEasy',
    images: [
      {
        url: 'https://designeasy.ai/images/bannerbot-og-image.png',
        width: 1200,
        height: 630,
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DesignEasy - AI Ad Creator',
    description:
      'Create stunning AI-powered ad banners and videos with DesignEasy. Leverage AI to generate professional, high-converting marketing creatives effortlessly.',
    images: ['https://designeasy.ai/images/bannerbot-og-image.png'],
  },
  metadataBase: new URL('https://designeasy.ai'),
  alternates: {
    canonical: 'https://designeasy.ai',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: false,
    },
  },
  authors: [{ name: 'DesignEasy' }],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.className} antialiased`}>
        <ReactQueryProvider>
          <UserProvider>
            {children}

            <Toaster />
          </UserProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
