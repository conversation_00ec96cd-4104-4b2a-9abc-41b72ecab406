import { fitTextInContainer } from '../../../utils/fp_video';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio, Img, useCurrentFrame, useVideoConfig } from 'remotion';
import gsap from 'gsap';
import { IFpVideoDataV1 } from '../../types';

const P8Composition: React.FC<{ videoData: IFpVideoDataV1 }> = ({
  videoData,
}) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const [customResetKey, setCustomResetKey] = useState(1);
  const divRef = useRef<HTMLDivElement>(null);

  const startAnimation = useCallback(() => {
    if (!pageTimelineRef.current) {
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
      });
    } else {
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    timeline.pause();

    const scene_1 = document.getElementById('scene-1');
    const scene_2 = document.getElementById('scene-2');
    const scene_3 = document.getElementById('scene-3');
    const scene_4 = document.getElementById('scene-4');
    const scene_5 = document.getElementById('scene-5');

    const scene_2_bg = document.getElementById('scene-2-bg');
    const scene_3_bg = document.getElementById('scene-3-bg');
    const scene_4_bg = document.getElementById('scene-4-bg');

    const scene_1_text = document.getElementById('scene-1-text');
    const scene_2_text = document.getElementById('scene-2-text');
    const scene_3_text = document.getElementById('scene-3-text');
    const scene_4_text = document.getElementById('scene-4-text');
    const scene_5_text = document.getElementById('scene-5-text');

    if (
      !scene_1 ||
      !scene_2 ||
      !scene_3 ||
      !scene_4 ||
      !scene_5 ||
      !scene_1_text ||
      !scene_2_text ||
      !scene_3_text ||
      !scene_4_text ||
      !scene_5_text ||
      !scene_2_bg ||
      !scene_3_bg ||
      !scene_4_bg
    ) {
      console.warn('One or more elements not found for animation setup.');
      return;
    }

    timeline
      .set([scene_1], {
        opacity: 1,
        x: 0,
      })
      .set([scene_1_text], {
        opacity: 1,
      })
      .set([scene_2, scene_3, scene_4, scene_5], {
        opacity: 0,
        x: 50,
      })
      .set([scene_2_bg, scene_3_bg, scene_4_bg], {
        opacity: 1,
        scale: 1,
      })
      .set([scene_2_text, scene_3_text, scene_4_text, scene_5_text], {
        opacity: 0,
      })
      .to([scene_1, scene_1_text], {
        delay: 2.5,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_2, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(scene_2_bg, {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_2_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_2, scene_2_bg, scene_2_text], {
        delay: 2.5,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_3, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(scene_3_bg, {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_3_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_3, scene_3_bg, scene_3_text], {
        delay: 2.5,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_4, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(scene_4_bg, {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_4_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_4, scene_4_bg, scene_4_text], {
        delay: 2.5,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_5, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_5_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_5, scene_5_text], {
        delay: 2,
        opacity: 1,
      });
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCustomResetKey((prev) => prev + 1);
    }, 100);
    return () => clearTimeout(timeoutId);
  }, [videoData]);

  useEffect(() => {
    startAnimation();

    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    requestAnimationFrame(() => {
      fitTextInContainer(items, 'text-fit');
      fitTextInContainer(containerFitItems, 'text-and-container-fit');
    });
  }, [customResetKey, startAnimation]);

  useEffect(() => {
    const currentDuration = frames / fps;
    if (pageTimelineRef.current) {
      pageTimelineRef.current.seek(currentDuration);
    }
  }, [frames, fps]);

  if (!videoData) {
    return null;
  }

  return (
    <div
      className="relative flex items-center justify-center"
      style={{
        height: `${videoData.height}px`,
        width: `${videoData.width}px`,
      }}
    >
      {videoData.base_audio?.url && <Audio src={videoData.base_audio.url} />}
      <div
        className="flex flex-col items-center justify-center relative overflow-hidden h-full w-full bg-gradient-to-b from-blue-50 to-cyan-50 text-blue-900"
        id="container"
      >
        {videoData.scenes.slice(0, 4).map((scene, index) => (
          <div
            className={`absolute inset-0 flex flex-col items-center justify-center p-8 ${index > 0 ? 'opacity-0' : ''}`}
            id={`scene-${index + 1}`}
            key={`scene-${index + 1}`}
            ref={(r) => {
              if (index === 0) divRef.current = r;
            }}
          >
            <div
              className={`w-full flex flex-col items-center justify-center z-10 relative ${index > 0 ? 'opacity-0' : ''}`}
              id={`scene-${index + 1}-text`}
            >
              {index === 0 && scene.texts?.[0]?.value && (
                <div className="h-[240px] flex items-center" data-height="240">
                  <p
                    className="text-8xl font-semibold text-center text-and-container-fit"
                    data-fontsize="72"
                  >
                    {scene.texts?.[0].value}
                  </p>
                </div>
              )}

              {index > 0 && scene.texts?.[0]?.value && (
                <div
                  className="h-[130px] mb-8 flex items-center"
                  data-height="130"
                >
                  <p
                    className="text-6xl font-semibold text-center text-and-container-fit"
                    data-fontsize="50"
                  >
                    {scene.texts?.[0].value}
                  </p>
                </div>
              )}

              {index > 0 && scene.assets?.[0]?.url && (
                <div
                  className="relative w-full px-4 py-6 flex justify-center"
                  id={`scene-${index + 1}-bg`}
                >
                  <Img
                    src={scene.assets[0].url}
                    alt={`Scene ${index + 1} background`}
                    className="w-full max-h-[600px] rounded-4xl shadow-xl object-cover"
                  />
                </div>
              )}

              {index > 0 &&
                scene.texts?.length == 2 &&
                scene.texts?.[1]?.value && (
                  <div
                    className="h-[120px] mt-12 flex items-center"
                    data-height="120"
                  >
                    <p
                      className="text-4xl text-center text-and-container-fit"
                      data-fontsize="36"
                    >
                      {scene.texts?.[1].value}
                    </p>
                  </div>
                )}
            </div>
          </div>
        ))}

        {videoData.scenes.length > 4 && videoData.scenes[4] && (
          <div
            className="absolute inset-0 flex flex-col items-center justify-center p-8 opacity-0"
            id="scene-5"
            key="scene-5"
          >
            {videoData.scenes[4].assets?.[0]?.url && (
              <div className="absolute inset-0 z-0" id="scene-5-bg"></div>
            )}
            <div
              className="w-full flex flex-col gap-8 items-center justify-center z-10 relative opacity-0"
              id="scene-5-text"
            >
              {videoData.branding?.logo?.url && (
                <div className="flex justify-center">
                  <Img
                    src={videoData.branding.logo.url}
                    width={120}
                    height={120}
                    alt="brand logo"
                    className="rounded-[30px] shadow-lg"
                  />
                </div>
              )}
              {videoData.branding?.brand_name && (
                <div className="h-[120px] flex items-center" data-height="120">
                  <p
                    className="text-6xl font-semibold text-center text-and-container-fit"
                    data-fontsize="50"
                  >
                    {videoData.branding.brand_name}
                  </p>
                </div>
              )}
              {videoData.scenes[4].texts?.[0]?.value && (
                <div className="h-[120px] flex items-center" data-height="120">
                  <p
                    className="text-4xl text-center text-and-container-fit"
                    data-fontsize="36"
                  >
                    {videoData.scenes[4].texts?.[0].value}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default P8Composition;
