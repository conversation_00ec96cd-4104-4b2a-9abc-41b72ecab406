import {
  AbsoluteFill,
  Audio,
  interpolate,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';
import ShowScenes from '../../components/ShowScenes';
import { IFpVideoDataV2 } from '../../../remotion/types';
import ShowSubtitle from '../../components/ShowSubtitle';
import ShowBrandDetails from '../../../remotion/components/ShowBrandDetails';

const BRANDING_SCENE_DURATION = 4;
const FADE_IN_DURATION = 0.3;

const P10Composition: React.FC<{ videoData: IFpVideoDataV2 }> = ({
  videoData,
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  const timeInSec = Math.round((frame / fps) * 10000) / 10000; // precision till 4 decimal points

  const brandingStartFrame = durationInFrames - BRANDING_SCENE_DURATION * fps;
  const fadeInEndFrame = brandingStartFrame + FADE_IN_DURATION * fps;

  const opacity = interpolate(
    frame,
    [brandingStartFrame, fadeInEndFrame],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    },
  );

  return (
    <AbsoluteFill className=" bg-black ">
      {(videoData?.scenes?.length ?? 0) > 0 && (
        <ShowScenes
          scenes={(videoData.scenes as IFpVideoDataV2['scenes']) ?? []}
          timeInSec={timeInSec}
        />
      )}

      {videoData.branding && (
        <Sequence
          from={brandingStartFrame}
          durationInFrames={BRANDING_SCENE_DURATION * fps}
          style={{ opacity, zIndex: 100 }}
        >
          <ShowBrandDetails branding={videoData.branding} />
        </Sequence>
      )}

      <ShowSubtitle
        subtitle={
          (videoData.base_audio as IFpVideoDataV2['base_audio']).subtitle
        }
      />

      <Audio src={videoData.base_audio.url} />
    </AbsoluteFill>
  );
};

export default P10Composition;
