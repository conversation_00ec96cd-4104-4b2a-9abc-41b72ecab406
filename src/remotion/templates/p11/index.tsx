import {
  AbsoluteFill,
  Audio,
  interpolate,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';
import { IFpVideoDataV2 } from '../../../remotion/types';
import ShowSubtitle from '../../components/ShowSubtitle';
import { LoopableOffthreadVideo } from '../../../remotion/utils/LoopableOffthreadVideo';
import ShowVideoHighlights from '../../../remotion/components/ShowVideoHighlights';
import ShowBrandDetails from '../../../remotion/components/ShowBrandDetails';

const BRANDING_SCENE_DURATION = 4;
const FADE_IN_DURATION = 0.3;

const P11Composition: React.FC<{ videoData: IFpVideoDataV2 }> = ({
  videoData,
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  const brandingStartFrame = durationInFrames - BRANDING_SCENE_DURATION * fps;
  const fadeInEndFrame = brandingStartFrame + FADE_IN_DURATION * fps;

  const opacity = interpolate(
    frame,
    [brandingStartFrame, fadeInEndFrame],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    },
  );

  const highlightsEndFrame = durationInFrames - BRANDING_SCENE_DURATION * fps;

  const disappearOpacity = interpolate(
    frame,
    [highlightsEndFrame - 0.5 * fps, highlightsEndFrame],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    },
  );

  return (
    <AbsoluteFill className=" bg-black ">
      {videoData.base_assets?.[0].url && (
        <div className=" w-full h-full flex items-center ">
          <LoopableOffthreadVideo
            className="w-full"
            src={videoData.base_assets?.[0].url}
            startFrom={0}
            muted
            onError={() => {}}
            videoDurationInSec={videoData.base_assets?.[0].video_duration}
            loop
          />
        </div>
      )}

      {videoData?.scenes?.[0] && (
        <Sequence
          from={0}
          durationInFrames={highlightsEndFrame}
          style={{ opacity: disappearOpacity }}
        >
          <ShowVideoHighlights texts={videoData?.scenes?.[0].texts ?? []} />
        </Sequence>
      )}

      {videoData.branding && (
        <Sequence
          from={brandingStartFrame}
          durationInFrames={BRANDING_SCENE_DURATION * fps}
          style={{ opacity }}
        >
          <ShowBrandDetails branding={videoData.branding} />
        </Sequence>
      )}

      <ShowSubtitle
        subtitle={
          (videoData.base_audio as IFpVideoDataV2['base_audio']).subtitle
        }
      />

      <Audio src={videoData.base_audio.url} />
    </AbsoluteFill>
  );
};

export default P11Composition;
