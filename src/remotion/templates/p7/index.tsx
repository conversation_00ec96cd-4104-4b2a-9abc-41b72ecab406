import { fitTextInContainer } from '../../../utils/fp_video';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio, Img, useCurrentFrame, useVideoConfig } from 'remotion';
import gsap from 'gsap';
import { IFpVideoDataV1 } from '@/remotion/types';

const P7Composition: React.FC<{ videoData: IFpVideoDataV1 }> = ({
  videoData,
}) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const [customResetKey, setCustomResetKey] = useState(1);
  const divRef = useRef<HTMLDivElement>(null);

  const startAnimation = useCallback(() => {
    if (!pageTimelineRef.current) {
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
      });
    } else {
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    timeline.pause();

    const scene_1 = document.getElementById('scene-1');
    const scene_2 = document.getElementById('scene-2');
    const scene_3 = document.getElementById('scene-3');
    const scene_4 = document.getElementById('scene-4');
    const scene_5 = document.getElementById('scene-5');

    const scene_1_bg = document.getElementById('scene-1-bg');
    const scene_2_bg = document.getElementById('scene-2-bg');
    const scene_3_bg = document.getElementById('scene-3-bg');
    const scene_4_bg = document.getElementById('scene-4-bg');
    const scene_5_bg = document.getElementById('scene-5-bg');

    const scene_1_text = document.getElementById('scene-1-text');
    const scene_2_text = document.getElementById('scene-2-text');
    const scene_3_text = document.getElementById('scene-3-text');
    const scene_4_text = document.getElementById('scene-4-text');
    const scene_5_text = document.getElementById('scene-5-text');

    if (
      !scene_1 ||
      !scene_2 ||
      !scene_3 ||
      !scene_4 ||
      !scene_5 ||
      !scene_1_bg ||
      !scene_2_bg ||
      !scene_3_bg ||
      !scene_4_bg ||
      !scene_5_bg ||
      !scene_1_text ||
      !scene_2_text ||
      !scene_3_text ||
      !scene_4_text ||
      !scene_5_text
    ) {
      return;
    }

    timeline
      .set([scene_2, scene_3, scene_4, scene_5], {
        opacity: 0,
      })
      .set([scene_2_bg, scene_3_bg, scene_4_bg, scene_5_bg], {
        opacity: 0,
        scale: 1.05,
      })
      .set([scene_2_text, scene_3_text, scene_4_text, scene_5_text], {
        opacity: 0,
      })
      .set(scene_1, {
        opacity: 1,
      })
      .set(scene_1_bg, {
        opacity: 1,
        scale: 1,
      })
      .set(scene_1_text, {
        opacity: 1,
      })
      .to([scene_1, scene_1_bg, scene_1_text], {
        delay: 2.5,
        opacity: 0,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_2, {
        opacity: 1,
        duration: 0.1,
      })
      .to(scene_2_bg, {
        opacity: 1,
        scale: 1,
        duration: 1.2,
        ease: 'power2.out',
      })
      .to(
        scene_2_text,
        {
          opacity: 1,
          duration: 1,
          ease: 'power2.out',
        },
        '-=1.0',
      )
      .to([scene_2, scene_2_bg, scene_2_text], {
        delay: 2.5,
        opacity: 0,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_3, {
        opacity: 1,
        duration: 0.1,
      })
      .to(scene_3_bg, {
        opacity: 1,
        scale: 1,
        duration: 1.2,
        ease: 'power2.out',
      })
      .to(
        scene_3_text,
        {
          opacity: 1,
          duration: 1,
          ease: 'power2.out',
        },
        '-=1.0',
      )
      .to([scene_3, scene_3_bg, scene_3_text], {
        delay: 2.5,
        opacity: 0,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_4, {
        opacity: 1,
        duration: 0.1,
      })
      .to(scene_4_bg, {
        opacity: 1,
        scale: 1,
        duration: 1.2,
        ease: 'power2.out',
      })
      .to(
        scene_4_text,
        {
          opacity: 1,
          duration: 1,
          ease: 'power2.out',
        },
        '-=1.0',
      )
      .to([scene_4, scene_4_bg, scene_4_text], {
        delay: 2.5,
        opacity: 0,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_5, {
        opacity: 1,
        duration: 0.1,
      })
      .to(scene_5_bg, {
        opacity: 1,
        scale: 1,
        duration: 1.2,
        ease: 'power2.out',
      })
      .to(
        scene_5_text,
        {
          opacity: 1,
          duration: 1,
          ease: 'power2.out',
        },
        '-=1.0',
      )
      .to([scene_5, scene_5_bg, scene_5_text], {
        delay: 2,
        opacity: 1,
      });
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCustomResetKey((prev) => prev + 1);
    }, 100);
    return () => clearTimeout(timeoutId);
  }, [videoData]);

  useEffect(() => {
    startAnimation();

    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    requestAnimationFrame(() => {
      fitTextInContainer(items, 'text-fit');
      fitTextInContainer(containerFitItems, 'text-and-container-fit');
    });
  }, [customResetKey, startAnimation]);

  useEffect(() => {
    const currentDuration = frames / fps;
    if (pageTimelineRef.current) {
      pageTimelineRef.current.seek(currentDuration);
    }
  }, [frames, fps]);

  if (!videoData) {
    return null;
  }

  return (
    <div
      className="relative"
      style={{
        height: `${videoData.height}px`,
        width: `${videoData.width}px`,
      }}
    >
      {videoData.base_audio?.url && <Audio src={videoData.base_audio.url} />}
      <div
        className="bg-black flex flex-col items-center justify-center relative overflow-hidden h-full w-full"
        id="container"
      >
        {videoData.scenes.slice(0, 4).map((scene, index) => (
          <div
            className={`absolute inset-0 flex items-end justify-center text-white p-8 ${index > 0 ? 'opacity-0' : ''}`}
            id={`scene-${index + 1}`}
            key={`scene-${index + 1}`}
            ref={(r) => {
              if (index === 0) divRef.current = r;
            }}
          >
            {scene.assets?.[0]?.url && (
              <div
                className={`absolute inset-0 z-0 ${index > 0 ? 'opacity-0' : ''}`}
                id={`scene-${index + 1}-bg`}
              >
                <Img
                  src={scene.assets[0].url}
                  alt={`Scene ${index + 1} background`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/10" />
              </div>
            )}
            <div
              className={`w-full z-10 relative bg-gradient-to-t from-black/50 via-black/30 to-transparent py-10 px-4 rounded-3xl backdrop-blur-xs ${index > 0 ? 'opacity-0' : ''}`}
              id={`scene-${index + 1}-text`}
            >
              {scene.texts?.[0]?.value && (
                <div className="h-[180px]" data-height="180">
                  <p
                    className="text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit"
                    data-fontsize="60"
                  >
                    {scene.texts?.[0].value}
                  </p>
                </div>
              )}
              {scene.texts?.length === 2 && scene.texts?.[1]?.value && (
                <div className="h-[160px] mt-8" data-height="160">
                  <p
                    className="text-4xl text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit"
                    data-fontsize="36"
                  >
                    {scene.texts?.[1].value}
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}

        {videoData.scenes.length > 4 && videoData.scenes[4] && (
          <div
            className="absolute inset-0 flex items-center justify-center text-white p-8 opacity-0"
            id="scene-5"
            key="scene-5"
          >
            {videoData.scenes[4].assets?.[0]?.url && (
              <div className="absolute inset-0 z-0 opacity-0" id="scene-5-bg">
                <Img
                  src={videoData.scenes[4].assets[0].url}
                  alt="Final scene background"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/40" />
              </div>
            )}
            <div
              className="w-full flex flex-col gap-8 z-10 relative opacity-0"
              id="scene-5-text"
            >
              {videoData.branding?.logo?.url && (
                <div className="flex justify-center">
                  <Img
                    src={videoData.branding.logo.url}
                    width={120}
                    height={120}
                    alt="brand logo"
                    className="rounded-[30px] shadow-lg"
                  />
                </div>
              )}
              {videoData.branding?.brand_name && (
                <div className="h-[180px]" data-height="180">
                  <p
                    className="text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit"
                    data-fontsize="60"
                  >
                    {videoData.branding.brand_name}
                  </p>
                </div>
              )}
              {videoData.scenes[4].texts?.[0]?.value && (
                <div className="h-[160px]" data-height="160">
                  <p
                    className="text-4xl text-center text-and-container-fit py-10 px-4 bg-black/20 backdrop-blur rounded-4xl"
                    data-fontsize="36"
                  >
                    {videoData.scenes[4].texts?.[0].value}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default P7Composition;
