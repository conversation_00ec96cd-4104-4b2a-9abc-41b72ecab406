import { fitTextInContainer } from '../../../utils/fp_video';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio, Img, useCurrentFrame, useVideoConfig } from 'remotion';
import gsap from 'gsap';
import { IFpVideoDataV1 } from '@/remotion/types';

const P5Composition: React.FC<{ videoData: IFpVideoDataV1 }> = ({
  videoData,
}) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const [customResetKey, setCustomResetKey] = useState(1);

  const startAnimation = useCallback(() => {
    if (!pageTimelineRef.current) {
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
      });
    } else {
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    timeline.pause();

    const scene_2 = document.getElementById('scene-2');
    const scene_3 = document.getElementById('scene-3');
    const scene_4 = document.getElementById('scene-4');
    const scene_5 = document.getElementById('scene-5');
    const container = document.getElementById('container');

    timeline
      .set(container, {
        x: '25%',
      })
      .set([scene_2, scene_3, scene_4], {
        scale: 0.8,
        x: '-10%',
        opacity: 1,
      })
      .set(scene_5, { x: 0 })
      .to(container, {
        x: '-39.40%',
        duration: 1,
        delay: 2,
      })
      .to(
        scene_2,
        {
          x: 0,
          scale: 1,
          duration: 1,
        },
        '-=1',
      )
      .to(container, {
        x: '-114%',
        duration: 1,
        delay: 2.5,
      })
      .to(
        scene_2,
        {
          x: '10%',
          scale: 0.8,
          duration: 1,
        },
        '-=1',
      )
      .to(
        scene_3,
        {
          x: 0,
          scale: 1,
          duration: 1,
        },
        '-=1',
      )
      .to(container, {
        x: '-188.5%',
        duration: 1,
        delay: 2.5,
      })
      .to(
        scene_3,
        {
          x: '10%',
          scale: 0.8,
          duration: 1,
        },
        '-=1',
      )
      .to(
        scene_4,
        {
          x: 0,
          scale: 1,
          duration: 1,
        },
        '-=1',
      )
      .to(container, {
        x: '-238%',
        duration: 1,
        delay: 2.5,
      })
      .to(
        scene_4,
        {
          x: '10%',
          scale: 0.8,
          duration: 1,
          opacity: 0,
        },
        '-=1',
      )
      .to(
        scene_5,
        {
          x: '-40%',
          duration: 1,
        },
        '-=1',
      );
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setCustomResetKey((prev) => prev + 1);
    }, 100);
  }, [videoData, startAnimation]);

  useEffect(() => {
    startAnimation();

    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );
    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');
  }, [customResetKey, startAnimation]);

  useEffect(() => {
    const currentDuration = frames / fps;
    pageTimelineRef.current?.seek(currentDuration);
  }, [frames, fps]);

  if (!videoData) {
    return null;
  }

  return (
    <div
      className=" relative bg-white "
      style={{
        height: `${videoData.height}px`,
        width: `${videoData.width}px`,
      }}
    >
      <Audio src={videoData.base_audio.url} />
      <div
        className=" absolute z-0 inset-0 py-28 text-gray-900 flex gap-8 translate-x-[25%] "
        id="container"
      >
        <div className=" shrink-0 w-[50%] z-[1] flex gap-8 " id="scene-1">
          <div className=" flex flex-col gap-8 justify-center w-full ">
            <div className=" h-[768px] " data-height="768">
              <p
                className=" text-9xl text-and-container-fit italic "
                data-fontsize="128"
              >
                {videoData?.scenes?.[0]?.texts?.[0].value}
              </p>
            </div>
            <div className=" h-[144px] " data-height="144">
              <p
                className=" text-5xl text-and-container-fit "
                data-fontsize="48"
              >
                {videoData?.scenes?.[0]?.texts?.[1].value}
              </p>
            </div>
          </div>
        </div>

        {videoData.scenes.slice(1, -1).map((scene, index) => (
          <div
            className=" w-[70%] shrink-0 scale-[80%] -translate-x-[10%] "
            id={`scene-${index + 2}`}
            key={`scene-${index + 2}`}
          >
            {scene?.assets?.[0].url && (
              <Img
                src={scene?.assets?.[0].url}
                className=" w-full h-full object-cover rounded-3xl "
              />
            )}
          </div>
        ))}

        <div
          className={` flex w-full items-center justify-center text-gray-900 p-20 shrink-0 `}
          id={`scene-5`}
          key={`scene-5`}
        >
          <div className=" p-6 py-10 rounded-3xl w-full flex flex-col justify-center gap-12 ">
            <div className=" flex items-center gap-8 w-full ">
              {videoData?.branding?.logo && (
                <Img
                  src={videoData.branding.logo.url}
                  width={120}
                  height={120}
                  alt="brand logo"
                  className=" rounded-[30px] shrink-0 "
                />
              )}

              <div className=" h-[130px] max-w-[70%] w-full " data-height="130">
                <p
                  className=" text-6xl font-semibold text-and-container-fit "
                  data-fontsize="60"
                >
                  {videoData?.branding?.brand_name}
                </p>
              </div>
            </div>
            <div className=" h-[300px] " data-height="300">
              <p
                className=" text-7xl italic text-and-container-fit "
                data-fontsize="72"
              >
                {videoData.scenes[4].texts?.[0].value}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default P5Composition;
