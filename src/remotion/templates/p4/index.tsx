import { fitTextInContainer } from '../../../utils/fp_video';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio, Img, useCurrentFrame, useVideoConfig } from 'remotion';
import gsap from 'gsap';
import { IFpVideoDataV1 } from '../../types';
import { LoopableOffthreadVideo } from '../../utils/LoopableOffthreadVideo';

const P4Composition: React.FC<{ videoData: IFpVideoDataV1 }> = ({
  videoData,
}) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Create a global reference for the timeline
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // const scenes = useRef<(HTMLDivElement | null)[]>([...Array(5).fill(null)]);
  const [customResetKey, setCustomResetKey] = useState(1);
  const divRef = useRef<HTMLDivElement>(null);

  const startAnimation = useCallback(() => {
    // Kill any existing animations
    if (!pageTimelineRef.current) {
      // Create the timeline only if it doesn't exist
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
        // onUpdate: syncVideoWithTimeline,
      });
    } else {
      // If the timeline exists, clear it before reusing
      // pageTimelineRef.current.seek(0).pause();
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    timeline.pause();

    // all the DOM references

    // const scene_1 = scenes.current[0];
    // const scene_2 = scenes.current[1];
    // const scene_3 = scenes.current[2];
    // const scene_4 = scenes.current[3];
    // const scene_5 = scenes.current[4];

    const scene_1 = document.getElementById('scene-1');
    const scene_2 = document.getElementById('scene-2');
    const scene_3 = document.getElementById('scene-3');
    const scene_4 = document.getElementById('scene-4');
    const scene_5 = document.getElementById('scene-5');

    // console.log(scene_1);
    // console.log(scene_2);
    // console.log(scene_3);
    // console.log(scene_4);
    // console.log(scene_5);

    // console.log(divRef.current);

    // the timeline animation
    timeline
      .set([scene_1, scene_2, scene_3, scene_4, scene_5], {
        opacity: 0,
      })
      .to(scene_1, {
        opacity: 1,
        ease: 'power1.inOut',
        onStart: () => {},
      })
      .set(scene_1, {
        delay: 3,
        opacity: 0,
      })
      .to(scene_2, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .set(scene_2, {
        delay: 3,
        opacity: 0,
      })
      .to(scene_3, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .set(scene_3, {
        delay: 3,
        opacity: 0,
      })
      .to(scene_4, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .set(scene_4, {
        delay: 3,
        opacity: 0,
      })
      .to(scene_5, {
        opacity: 1,
        ease: 'power1.inOut',
      })
      .to(scene_5, {
        delay: 3,
        opacity: 1,
      });
  }, []);

  useEffect(() => {
    // setCustomResetKey((prev) => prev + 1);
    // scenes.current = [...Array(5).fill(null)];
    setTimeout(() => {
      // startAnimation();
      setCustomResetKey((prev) => prev + 1);
    }, 100);
  }, [videoData, startAnimation]);

  useEffect(() => {
    startAnimation();

    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    /**
     * steps to make a font size variable
     * add a container around the tag ('p', 'h1',...) and give some fixed height to it
     * now add the className 'text-fit' or 'text-and-container-fit' to the tag ('p', 'h1',...) as per use
     * 'text-fit' just make the text change font size
     * 'text-and-container-fit' changes the text size also at last changes the container size maximum being the already set height
     */

    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');
  }, [customResetKey, startAnimation]);

  useEffect(() => {
    // const durationInSeconds = durationInFrames/fps
    const currentDuration = frames / fps;
    pageTimelineRef.current?.seek(currentDuration);
  }, [frames, fps]);

  if (!videoData) {
    return null;
  }

  return (
    <div
      className=" relative "
      style={{
        height: `${videoData.height}px`,
        width: `${videoData.width}px`,
        // scale: (videoData?.containerWidth ?? videoData.width) / videoData.width,
        // transformOrigin: '0 0',
      }}
    >
      <Audio src={videoData.base_audio.url} />
      <div
        className="bg-white h-full flex flex-col items-center justify-center text-lime-900 relative overflow-hidden"
        id="container"
      >
        {videoData.base_assets?.[0].url && (
          <div className=" w-full h-full flex items-center ">
            <LoopableOffthreadVideo
              className="w-full"
              id="main-background-video"
              src={videoData.base_assets?.[0].url}
              startFrom={0}
              muted
              videoDurationInSec={videoData.base_assets?.[0].video_duration}
              loop // for now the loop is not supported the lambda is facing issues
            />
          </div>
        )}

        {videoData.scenes.slice(0, -1).map((scene, index) => (
          <div
            className={` absolute inset-0 flex items-center justify-center text-white p-8 opacity-0 `}
            id={`scene-${index + 1}`}
            key={`scene-${index + 1}`}
            ref={(r) => {
              if (index === 1) divRef.current = r;
            }}
          >
            <div className="bg-black/50 p-6 py-10 rounded-3xl w-full ">
              <div className=" h-[180px] " data-height="180">
                <p
                  className=" text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit "
                  data-fontsize="60"
                >
                  {scene.texts?.[0].value}
                </p>
              </div>
              {scene.texts?.length == 2 && (
                <div className=" h-[160px]  mt-8 " data-height="160">
                  <p
                    className=" text-4xl text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit "
                    data-fontsize="36"
                  >
                    {scene.texts?.[1].value}
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}

        <div
          className={` absolute inset-0 flex items-center justify-center text-white p-8 opacity-0 `}
          id={`scene-5`}
          key={`scene-5`}
          // ref={(r) => {
          //   scenes.current[4] = r;
          // }}
        >
          <div className="bg-black/50 p-6 py-10 rounded-3xl w-full flex flex-col gap-8 ">
            {videoData?.branding?.logo &&
              videoData.branding.logo.url !== '' && (
                <div className=" justify-center hidden " id="logo-parent">
                  <Img
                    src={videoData.branding.logo.url}
                    width={120}
                    height={120}
                    alt="brand logo"
                    className=" rounded-[30px] "
                    onLoad={() => {
                      const parentElement =
                        document.getElementById('logo-parent');
                      if (!parentElement) return;
                      parentElement.style.display = 'flex';
                    }}
                    onError={() => {
                      const parentElement =
                        document.getElementById('logo-parent');
                      if (!parentElement) return;
                      parentElement.style.display = 'none';
                    }}
                  />
                </div>
              )}
            <div className=" h-[180px] " data-height="180">
              <p
                className=" text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit "
                data-fontsize="60"
              >
                {videoData?.branding?.brand_name}
              </p>
            </div>
            <div className=" h-[160px] " data-height="160">
              <p
                className=" text-4xl text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit "
                data-fontsize="36"
              >
                {videoData.scenes[4].texts?.[0].value}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default P4Composition;
