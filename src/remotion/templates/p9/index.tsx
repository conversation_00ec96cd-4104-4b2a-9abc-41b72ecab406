import { fitTextInContainer } from '../../../utils/fp_video';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Audio,
  Img,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';
import gsap from 'gsap';
import { LoopableOffthreadVideo } from '../../utils/LoopableOffthreadVideo';
import { IFpVideoDataV1 } from '@/remotion/types';

const P9Composition: React.FC<{ videoData: IFpVideoDataV1 }> = ({
  videoData,
}) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const [customResetKey, setCustomResetKey] = useState(1);

  const startAnimation = useCallback(() => {
    if (!pageTimelineRef.current) {
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
      });
    } else {
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    timeline.pause();

    const scene_1 = document.getElementById('scene-1');
    const scene_2 = document.getElementById('scene-2');
    const scene_3 = document.getElementById('scene-3');
    const scene_4 = document.getElementById('scene-4');

    const scene_1_text = document.getElementById('scene-1-text');
    const scene_2_text = document.getElementById('scene-2-text');
    const scene_3_text = document.getElementById('scene-3-text');
    const scene_4_text = document.getElementById('scene-4-text');

    timeline
      .set([scene_1, scene_1_text], {
        opacity: 1,
        x: 0,
      })
      .set([scene_2, scene_3, scene_4], {
        opacity: 0,
        x: 50,
      })
      .set([scene_2_text, scene_3_text, scene_4_text], {
        opacity: 0,
      })
      .to([scene_1, scene_1_text], {
        delay: 3,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_2, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_2_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_2, scene_2_text], {
        delay: 5.5,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_3, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_3_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_3, scene_3_text], {
        delay: 5.5,
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.in',
      })
      .to(scene_4, {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: 'power2.out',
      })
      .to(
        scene_4_text,
        {
          opacity: 1,
          duration: 0.5,
          ease: 'power2.out',
        },
        '-=0.5',
      )
      .to([scene_4, scene_4_text], {
        delay: 3,
        opacity: 1,
      });
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setCustomResetKey((prev) => prev + 1);
    }, 100);
  }, [videoData, startAnimation]);

  useEffect(() => {
    startAnimation();

    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');
  }, [customResetKey, startAnimation]);

  useEffect(() => {
    const currentDuration = frames / fps;
    pageTimelineRef.current?.seek(currentDuration);
  }, [frames, fps]);

  if (!videoData) {
    return null;
  }

  return (
    <div
      className="relative flex items-center justify-center"
      style={{
        height: `${videoData.height}px`,
        width: `${videoData.width}px`,
      }}
    >
      <Audio src={videoData.base_audio.url} />
      <div
        className="flex flex-col items-center justify-center relative overflow-hidden h-full w-full bg-gradient-to-b from-blue-50 to-cyan-50 text-blue-900"
        id="container"
      >
        {/* First frame - Text only */}
        <div
          className="absolute inset-0 flex flex-col items-center justify-center p-8"
          id="scene-1"
          key="scene-1"
        >
          <div
            className="w-full flex flex-col items-center justify-center z-10 relative"
            id="scene-1-text"
          >
            {videoData.scenes[0].texts?.[0]?.value && (
              <div className="h-[240px] flex items-center" data-height="240">
                <p
                  className="text-8xl font-semibold text-center text-and-container-fit"
                  data-fontsize="72"
                >
                  {videoData.scenes[0].texts?.[0].value}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Second frame - Video with text */}
        <div
          className="absolute inset-0 flex flex-col items-center justify-center p-8 opacity-0 translate-x-[50px] "
          id="scene-2"
          key="scene-2"
        >
          <div
            className="w-full flex flex-col items-center justify-center z-10 relative opacity-0"
            id="scene-2-text"
          >
            {videoData.scenes[1].texts?.[0]?.value && (
              <div
                className="h-[130px] mb-8 flex items-center"
                data-height="130"
              >
                <p
                  className="text-6xl font-semibold text-center text-and-container-fit"
                  data-fontsize="50"
                >
                  {videoData.scenes[1].texts?.[0].value}
                </p>
              </div>
            )}

            {videoData.scenes[1].assets?.[0]?.url && (
              <div className="relative w-full px-4 py-6 flex justify-center">
                <Sequence
                  from={99}
                  durationInFrames={189}
                  className=" !relative "
                >
                  <LoopableOffthreadVideo
                    src={videoData.scenes[1].assets[0].url}
                    className="w-full max-h-[700px] rounded-4xl shadow-xl object-cover"
                    muted
                    // loop
                  />
                </Sequence>
              </div>
            )}

            {videoData.scenes[1].texts?.length == 2 && (
              <div
                className="h-[120px] mt-12 flex items-center"
                data-height="120"
              >
                <p
                  className="text-4xl text-center text-and-container-fit"
                  data-fontsize="36"
                >
                  {videoData.scenes[1].texts?.[1].value}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Third frame - Video with text */}
        <div
          className="absolute inset-0 flex flex-col items-center justify-center p-8 opacity-0 translate-x-[50px]"
          id="scene-3"
          key="scene-3"
        >
          <div
            className="w-full flex flex-col items-center justify-center z-10 relative opacity-0"
            id="scene-3-text"
          >
            {videoData.scenes[2].texts?.[0]?.value && (
              <div
                className="h-[130px] mb-8 flex items-center"
                data-height="130"
              >
                <p
                  className="text-6xl font-semibold text-center text-and-container-fit"
                  data-fontsize="50"
                >
                  {videoData.scenes[2].texts?.[0].value}
                </p>
              </div>
            )}

            {videoData.scenes[2].assets?.[0]?.url && (
              <div className="relative w-full px-4 py-6 flex justify-center">
                <Sequence
                  from={288}
                  durationInFrames={189}
                  className=" !relative "
                >
                  <LoopableOffthreadVideo
                    src={videoData.scenes[2].assets[0].url}
                    className="w-full max-h-[700px] rounded-4xl shadow-xl object-cover"
                    muted
                    // loop
                  />
                </Sequence>
              </div>
            )}

            {videoData.scenes[2].texts?.length == 2 && (
              <div
                className="h-[120px] mt-12 flex items-center"
                data-height="120"
              >
                <p
                  className="text-4xl text-center text-and-container-fit"
                  data-fontsize="36"
                >
                  {videoData.scenes[2].texts?.[1].value}
                </p>
              </div>
            )}
          </div>
        </div>

        <div
          className="absolute inset-0 flex flex-col items-center justify-center p-8 opacity-0 translate-x-[50px]"
          id="scene-4"
          key="scene-4"
        >
          <div
            className="w-full flex flex-col gap-8 items-center justify-center z-10 relative opacity-0"
            id="scene-4-text"
          >
            {videoData?.branding?.logo && (
              <div className="flex justify-center">
                <Img
                  src={videoData.branding.logo.url}
                  width={120}
                  height={120}
                  alt="brand logo"
                  className="rounded-[30px] shadow-lg"
                />
              </div>
            )}
            <div className="h-[120px] flex items-center" data-height="120">
              <p
                className="text-6xl font-semibold text-center text-and-container-fit"
                data-fontsize="50"
              >
                {videoData?.branding?.brand_name}
              </p>
            </div>
            <div className="h-[120px] flex items-center" data-height="120">
              <p
                className="text-4xl text-center text-and-container-fit"
                data-fontsize="36"
              >
                {videoData.scenes[3]?.texts?.[0].value}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default P9Composition;
