import { fitTextInContainer } from '../../../utils/fp_video';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio, Img, useCurrentFrame, useVideoConfig } from 'remotion';
import gsap from 'gsap';
import { LoopableOffthreadVideo } from '../../utils/LoopableOffthreadVideo';
import { IFpVideoDataV1 } from '@/remotion/types';

const P6Composition: React.FC<{ videoData: IFpVideoDataV1 }> = ({
  videoData,
}) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();
  const pageTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const [customResetKey, setCustomResetKey] = useState(1);

  const startAnimation = useCallback(() => {
    if (!pageTimelineRef.current) {
      pageTimelineRef.current = gsap.timeline({
        repeat: 0,
      });
    } else {
      pageTimelineRef.current.clear();
    }

    const timeline = pageTimelineRef.current;

    timeline.pause();

    const scene_2 = document.getElementById('scene-2');
    const hidden_text = document.getElementsByClassName('hidden-text');
    const hidden_text_2 = document.getElementsByClassName('hidden-text-2');

    timeline
      .set([hidden_text, hidden_text_2], {
        y: '100%',
        opacity: 0,
      })
      .set(scene_2, {
        opacity: 0,
      })
      .to(hidden_text, {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: 'power1.out',
      })
      .to(scene_2, {
        opacity: 1,
        delay: 10,
        duration: 0.5,
      })
      .to(
        hidden_text_2,
        {
          y: 0,
          opacity: 1,
          duration: 1,
        },
        '-=0.5',
      );
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setCustomResetKey((prev) => prev + 1);
    }, 100);
  }, [videoData, startAnimation]);

  useEffect(() => {
    startAnimation();

    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );
    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');
  }, [customResetKey, startAnimation]);

  useEffect(() => {
    const currentDuration = frames / fps;
    pageTimelineRef.current?.seek(currentDuration);
  }, [frames, fps]);

  if (!videoData) {
    return null;
  }

  return (
    <div
      className=" relative bg-white "
      style={{
        height: `${videoData.height}px`,
        width: `${videoData.width}px`,
      }}
    >
      <Audio src={videoData.base_audio.url} />
      {videoData.base_assets?.[0].url && (
        <LoopableOffthreadVideo
          className="w-full h-full"
          id="main-background-video"
          src={videoData.base_assets?.[0].url}
          startFrom={0}
          muted
        />
      )}
      <div
        className=" absolute z-0 inset-0 py-28 text-gray-900 flex gap-8 "
        id="container"
      >
        <div
          className=" absolute inset-0 flex flex-col justify-end gap-8 p-8 "
          id="scene-1"
        >
          <div className=" relative overflow-hidden ">
            <div className=" p-4 bg-gray-900 text-gray-50 w-fit max-w-[60%] opacity-0 ">
              <div className=" h-[48px] font-medium " data-height="48">
                <p
                  className=" text-5xl text-and-container-fit "
                  data-fontsize="48"
                >
                  {videoData?.scenes?.[0].texts?.[0].value}
                </p>
              </div>
            </div>
            <div className=" p-4 bg-gray-900 text-gray-50 w-fit max-w-[60%] absolute top-0 left-0 translate-y-full opacity-0 hidden-text ">
              <div className=" h-[48px] font-medium " data-height="48">
                <p
                  className=" text-5xl text-and-container-fit "
                  data-fontsize="48"
                >
                  {videoData?.scenes?.[0].texts?.[0].value}
                </p>
              </div>
            </div>
          </div>

          <div className=" flex flex-col gap-4 ">
            {[1, 2].map((index) => (
              <div
                className={` relative overflow-hidden ${
                  index === 1 ? 'max-w-[75%]' : 'max-w-[90%]'
                } `}
                key={`hidden-text-${index}`}
              >
                <div className={` p-4 text-gray-900 bg-gray-50 opacity-0`}>
                  <div className="h-[96px] font-medium" data-height="96">
                    <p
                      className="text-5xl text-and-container-fit"
                      data-fontsize="48"
                    >
                      {videoData?.scenes?.[0].texts?.[index].value}
                    </p>
                  </div>
                </div>
                <div
                  className={`p-4 text-gray-900 bg-gray-50 absolute top-0 left-0 w-full h-full translate-y-full opacity-0 hidden-text `}
                >
                  <div className="h-[96px] font-medium" data-height="96">
                    <p
                      className="text-5xl text-and-container-fit"
                      data-fontsize="48"
                    >
                      {videoData?.scenes?.[0].texts?.[index].value}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div
          id="scene-2"
          className=" bg-white absolute inset-0 z-10 p-12 py-20 opacity-0 "
        >
          <div className=" flex flex-col gap-8 ">
            <div className=" flex items-center gap-4 ">
              {videoData?.branding?.logo && (
                <Img
                  src={videoData.branding.logo.url}
                  width={120}
                  height={120}
                  alt="brand logo"
                  className=" rounded-[30px] "
                />
              )}
              <div className=" h-[130px] max-w-[80%] w-full " data-height="130">
                <p
                  className=" text-6xl font-bold text-and-container-fit "
                  data-fontsize="60"
                >
                  {videoData?.branding?.brand_name}
                </p>
              </div>
            </div>
            <div className=" relative overflow-hidden py-4 ">
              <div className=" h-[144px] w-full opacity-0 " data-height="144">
                <p
                  className=" text-5xl text-and-container-fit "
                  data-fontsize="48"
                >
                  {videoData.scenes?.[1]?.texts?.[0]?.value}
                </p>
              </div>
              <div
                className=" h-[144px] absolute top-4 left-0 w-full translate-y-full opacity-0 hidden-text-2 "
                data-height="144"
              >
                <p
                  className=" text-5xl text-and-container-fit "
                  data-fontsize="48"
                >
                  {videoData.scenes?.[1]?.texts?.[0]?.value}
                </p>
              </div>
            </div>
          </div>

          <div className=" mt-8 flex flex-col gap-4 ">
            <div className=" flex gap-4 items-center ">
              <div className=" w-12 ">
                <svg
                  stroke="currentColor"
                  fill="none"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </div>
              <div className=" flex-1 relative overflow-hidden flex items-center py-2 ">
                <div className=" h-[42px] opacity-0 w-full " data-height="42">
                  <p
                    className=" text-4xl text-and-container-fit "
                    data-fontsize="36"
                  >
                    {videoData.scenes?.[1]?.texts?.[1]?.value}
                  </p>
                </div>
                <div
                  className=" h-[42px] absolute top-2 left-0 w-full translate-y-full opacity-0 hidden-text-2 "
                  data-height="42"
                >
                  <p
                    className=" text-4xl text-and-container-fit "
                    data-fontsize="36"
                  >
                    {videoData.scenes?.[1]?.texts?.[1]?.value}
                  </p>
                </div>
              </div>
            </div>
            <div className=" flex gap-4 items-center ">
              <div className=" w-12 ">
                <svg
                  stroke="currentColor"
                  fill="none"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="12" r="4"></circle>
                  <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94"></path>
                </svg>
              </div>
              <div className=" flex-1 relative overflow-hidden flex items-center py-2 ">
                <div className=" h-[42px] opacity-0 w-full " data-height="42">
                  <p
                    className=" text-4xl text-and-container-fit "
                    data-fontsize="36"
                  >
                    {videoData.scenes?.[1]?.texts?.[2]?.value}
                  </p>
                </div>
                <div
                  className=" h-[42px] absolute top-2 left-0 w-full translate-y-full opacity-0 hidden-text-2 "
                  data-height="42"
                >
                  <p
                    className=" text-4xl text-and-container-fit "
                    data-fontsize="36"
                  >
                    {videoData.scenes?.[1]?.texts?.[2]?.value}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className=" absolute bottom-0 left-0 w-full flex-1 ">
            <div className=" w-[110%] pt-[110%] relative z-10 rounded-full overflow-hidden -translate-x-[20%] translate-y-[20%] ">
              hey
              {videoData.scenes?.[1]?.assets?.[0].url && (
                <Img
                  src={videoData.scenes?.[1]?.assets?.[0].url}
                  className=" absolute inset-0 w-full h-full object-cover "
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default P6Composition;
