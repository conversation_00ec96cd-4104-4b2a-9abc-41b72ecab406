import React from 'react';
import { Composition } from 'remotion';
import P4Composition from './templates/p4';
import { IFpVideoDataSchema, IFpVideoDataV2Schema } from './types';
import {
  P7_TEMPLATE_EXAMPLE_VALUE,
  P3_TEMPLATE_EXAMPLE_VALUE,
  P4_TEMPLATE_EXAMPLE_VALUE,
  P5_TEMPLATE_EXAMPLE_VALUE,
  P6_TEMPLATE_EXAMPLE_VALUE,
  TEMPLATES_CONFIG,
  P8_TEMPLATE_EXAMPLE_VALUE,
  P9_TEMPLATE_EXAMPLE_VALUE,
  P10_TEMPLATE_EXAMPLE_VALUE,
  P11_TEMPLATE_EXAMPLE_VALUE,
} from '../constants/fp-video';
import P3Composition from './templates/p3';
import P0Composition from './templates/p0';
import P5Composition from './templates/p5';
import P6Composition from './templates/p6';
import P7Composition from './templates/p7';
import P8Composition from './templates/p8';
import P9Composition from './templates/p9';
import P10Composition from './templates/p10';
import { z } from 'zod';
import P11Composition from './templates/p11';

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="p9"
        component={P9Composition}
        durationInFrames={
          TEMPLATES_CONFIG['p9'].durationInSec * TEMPLATES_CONFIG['p9'].fps
        }
        fps={TEMPLATES_CONFIG['p9'].fps}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P9_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p8"
        component={P8Composition}
        durationInFrames={
          TEMPLATES_CONFIG['p8'].durationInSec * TEMPLATES_CONFIG['p8'].fps
        }
        fps={TEMPLATES_CONFIG['p8'].fps}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P8_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p7"
        component={P7Composition}
        durationInFrames={
          TEMPLATES_CONFIG['p7'].durationInSec * TEMPLATES_CONFIG['p7'].fps
        }
        fps={TEMPLATES_CONFIG['p7'].fps}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P7_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p6"
        component={P6Composition}
        durationInFrames={
          TEMPLATES_CONFIG['p6'].durationInSec * TEMPLATES_CONFIG['p6'].fps
        }
        fps={TEMPLATES_CONFIG['p6'].fps}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P6_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p5"
        component={P5Composition}
        durationInFrames={
          TEMPLATES_CONFIG['p5'].durationInSec * TEMPLATES_CONFIG['p5'].fps
        }
        fps={TEMPLATES_CONFIG['p5'].fps}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P5_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p4"
        component={P4Composition}
        durationInFrames={
          TEMPLATES_CONFIG['p4'].durationInSec * TEMPLATES_CONFIG['p4'].fps
        }
        fps={TEMPLATES_CONFIG['p4'].fps}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P4_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p3"
        component={P3Composition}
        durationInFrames={TEMPLATES_CONFIG['p3'].durationInSec * 60}
        fps={60}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataSchema })}
        defaultProps={{ videoData: P3_TEMPLATE_EXAMPLE_VALUE }}
      />
      <Composition
        id="p0"
        component={P0Composition}
        durationInFrames={60}
        fps={60}
        width={720}
        height={1280}
      />
      <Composition
        id="p10"
        component={P10Composition}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataV2Schema })}
        defaultProps={{ videoData: P10_TEMPLATE_EXAMPLE_VALUE }}
        calculateMetadata={({ props, defaultProps }) => {
          /**
           * since we don't know the video duration start we extract that from the subitle timing
           * also we have the fps in the props
           */
          const fps = props.videoData.fps ?? defaultProps.videoData.fps;
          const durationInSec = props.videoData.duration_in_sec;
          const subtitle =
            props.videoData.base_audio.subtitle ??
            defaultProps.videoData.base_audio.subtitle;
          const lastSubtitleEndTime = subtitle[subtitle.length - 1];
          return {
            durationInFrames: durationInSec
              ? Math.ceil(durationInSec * fps)
              : Math.ceil((lastSubtitleEndTime.end + 1) * fps), // giving 1sec extra after subtitle ends
            fps,
          };
        }}
      />
      <Composition
        id="p11"
        component={P11Composition}
        width={720}
        height={1280}
        schema={z.object({ videoData: IFpVideoDataV2Schema })}
        defaultProps={{ videoData: P11_TEMPLATE_EXAMPLE_VALUE }}
        calculateMetadata={({ props, defaultProps }) => {
          const fps = props.videoData.fps ?? defaultProps.videoData.fps;
          const durationInSec = props.videoData.duration_in_sec;
          const subtitle =
            props.videoData.base_audio.subtitle ??
            defaultProps.videoData.base_audio.subtitle;
          const lastSubtitleEndTime = subtitle[subtitle.length - 1];
          return {
            durationInFrames: durationInSec
              ? Math.ceil(durationInSec * fps)
              : Math.ceil((lastSubtitleEndTime.end + 1) * fps),
            fps,
          };
        }}
      />
    </>
  );
};
