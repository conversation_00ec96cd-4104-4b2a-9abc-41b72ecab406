import { fitTextInContainer } from '../../utils/fp_video';
import React, { useEffect, useRef } from 'react';
import { AbsoluteFill } from 'remotion';

interface IShowVideoHighlightsProps {
  texts: Array<{ value: string }>;
}

const ShowVideoHighlights = (props: IShowVideoHighlightsProps) => {
  const { texts } = props;
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');

    if (containerRef.current) containerRef.current.style.width = 'fit-content';
  }, [texts]);

  if (texts.length == 0) return null;

  return (
    <AbsoluteFill className=" ">
      <div className=" m-4 mr-4 mt-8 font-semibold ">
        <div
          className=" h-[280px] max-w-[500px] ml-auto "
          ref={containerRef}
          data-height="280"
        >
          <div
            className="text-white text-4xl text-and-container-fit"
            style={{ lineHeight: '120%' }}
            data-fontsize="36"
          >
            {texts.length === 1
              ? texts.map(({ value }, index) => (
                  <p className=" relative px-2 w-fit text-nowrap " key={index}>
                    <div className=" absolute z-[1] top-1/2 left-0 w-full h-[140%] -translate-y-1/2 bg-blue-500 rounded  " />
                    <span className=" relative z-10 ">{value}</span>
                  </p>
                ))
              : texts.map(({ value }, index) => (
                  <p className=" relative px-2 w-fit text-nowrap " key={index}>
                    <div
                      className={` absolute z-[1] top-1/2 left-0 w-full -translate-y-1/2 bg-blue-500 rounded-r ${index === 0 ? ' h-[140%] rounded-tl ' : index === texts.length - 1 ? ' h-[140%] rounded-bl ' : ' h-[110%] '} `}
                    />
                    <span className=" relative z-10 ">{value}</span>
                  </p>
                ))}
            {/* <p className=" relative px-2 w-fit ">
                    <div className=" absolute z-[1] top-1/2 left-0 w-full h-[140%] -translate-y-1/2 bg-blue-500 rounded-r rounded-tl  " />
                    <span className=" relative z-10 ">3N 4D Manali Trip</span>
                  </p>
                  <p className=" relative px-2 w-fit ">
                    <div className=" absolute z-[1] top-1/2 left-0 w-full h-[110%] -translate-y-1/2 bg-blue-500 rounded-r  " />
                    <span className=" relative z-10 ">Rs 16999</span>
                  </p>
                  <p className=" relative px-2 w-fit ">
                    <div className=" absolute z-[1] top-1/2 left-0 w-full h-[110%] -translate-y-1/2 bg-blue-500 rounded-r  " />
                    <span className=" relative z-10 ">other package</span>
                  </p>
                  <p className=" relative px-2 w-fit ">
                    <div className=" absolute z-[1] top-1/2 left-0 w-full h-[140%] -translate-y-1/2 bg-blue-500 rounded-r rounded-bl  " />
                    <span className=" relative z-10 ">Somewhere around manali</span>
                  </p> */}
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default ShowVideoHighlights;
