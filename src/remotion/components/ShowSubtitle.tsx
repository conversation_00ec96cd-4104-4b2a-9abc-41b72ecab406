import { AbsoluteFill, useCurrentFrame, useVideoConfig } from 'remotion';
import {
  getCurrSubtitleGroup,
  getSubtitlesGroup,
} from '../utils/video-v2/subtitle';
import { ISubtitle } from '@/types/video_v2';
import { useEffect } from 'react';
import { fitTextInContainer } from '../../utils/fp_video';
// import Style1 from './subtitle/Style1';
// import Style2 from './subtitle/Style2';
// import Style3 from './subtitle/Style3';
import Style4 from './subtitle/Style4';

const ShowSubtitle = ({ subtitle }: { subtitle: ISubtitle }) => {
  const frames = useCurrentFrame();
  const { fps } = useVideoConfig();

  const timeInSec = Math.round((frames / fps) * 10000) / 10000; // precision till 4 decimal points

  const subtitleGroup = getSubtitlesGroup(subtitle);
  const currSubGroup = getCurrSubtitleGroup(subtitleGroup, timeInSec);

  useEffect(() => {
    const items = document.getElementsByClassName('text-fit');
    const containerFitItems = document.getElementsByClassName(
      'text-and-container-fit',
    );

    fitTextInContainer(items, 'text-fit');
    fitTextInContainer(containerFitItems, 'text-and-container-fit');
  }, [currSubGroup]);

  return (
    <AbsoluteFill>
      {currSubGroup && <Style4 currSubGroup={currSubGroup} />}
    </AbsoluteFill>
  );
};

export default ShowSubtitle;
