import React, { useRef } from 'react';
import { loadFont } from '@remotion/google-fonts/Rubik';
import { ISegment, IWord } from '@/types/video_v2';

interface IStyle3Props {
  currSubGroup: {
    segment: ISegment;
    word: IWord | undefined;
  };
}

const Style3 = (props: IStyle3Props) => {
  const { currSubGroup } = props;
  const previousIndex = useRef(-1);

  const { fontFamily } = loadFont();

  const currWordIndex = currSubGroup.segment.words.findIndex(
    (item) => item.text === currSubGroup.word?.text,
  );

  if (currWordIndex !== -1 && previousIndex.current !== currWordIndex) {
    previousIndex.current = currWordIndex;
  }

  return (
    <div className="fixed z-[999] top-[80%] w-full flex justify-center">
      <div className=" px-12 font-semibold text-center  ">
        <p
          className={`text-white text-5xl flex gap-5 flex-wrap justify-center `}
          style={{ fontFamily }}
        >
          {currSubGroup.segment.words.map((word, index) => (
            <span key={index} className=" relative ">
              {(index <= previousIndex.current ||
                previousIndex.current === -1) && (
                <svg
                  key={index}
                  xmlns="http://www.w3.org/2000/svg"
                  className=" absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 "
                  width={300}
                  height={200}
                  viewBox="0 0 300 200"
                >
                  <text
                    x="50%"
                    y="50%"
                    className="text-center [text-anchor:middle]"
                    dominant-baseline="middle"
                    stroke="#000000"
                    stroke-width="12"
                    stroke-linejoin="round"
                    fill="none"
                  >
                    {word.text}
                  </text>

                  <text
                    x="50%"
                    y="50%"
                    className="text-center [text-anchor:middle]"
                    dominant-baseline="middle"
                    fill="#fefbe5"
                  >
                    {word.text}
                  </text>
                </svg>
              )}

              <span className=" opacity-0 ">{word.text}</span>
            </span>
          ))}
        </p>
      </div>
    </div>
  );
};

export default Style3;
