import React from 'react';
import { loadFont } from '@remotion/google-fonts/Rubik';
import { ISegment, IWord } from '@/types/video_v2';

interface IStyle1Props {
  currSubGroup: {
    segment: ISegment;
    word: IWord | undefined;
  };
}

const Style1 = (props: IStyle1Props) => {
  const { currSubGroup } = props;

  const { fontFamily } = loadFont();
  return (
    <div className="fixed z-[999] top-[80%] w-full flex justify-center">
      <div className=" bg-gray-600/90 py-6 max-w-full mx-14 rounded-xl ">
        <div className=" px-12 font-semibold text-center  ">
          <div className=" h-[180px] " data-height="180">
            <p
              className={`text-white text-5xl flex gap-x-4 gap-y-2 flex-wrap justify-center text-and-container-fit  `}
              style={{ lineHeight: '120%', fontFamily }}
              data-fontsize="48"
            >
              {currSubGroup.segment.words.map((word, index) => (
                <span
                  key={index}
                  className={
                    word.text === currSubGroup.word?.text
                      ? 'text-yellow-500'
                      : ''
                  }
                >
                  {word.text}
                </span>
              ))}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Style1;
