import React from 'react';
import { loadFont } from '@remotion/google-fonts/Rubik';
import { ISegment, IWord } from '@/types/video_v2';

interface IStyle4Props {
  currSubGroup: {
    segment: ISegment;
    word: IWord | undefined;
  };
}

const Style4 = (props: IStyle4Props) => {
  const { currSubGroup } = props;

  const { fontFamily } = loadFont();

  return (
    <div className="fixed z-[999] top-[80%] w-full flex justify-center">
      <div className=" px-12 font-semibold text-center  ">
        <p
          className={`text-white text-5xl flex gap-5 flex-wrap justify-center `}
          style={{ fontFamily }}
        >
          {currSubGroup.segment.words.map((word, index) => (
            <span key={index} className=" relative ">
              <span className=" absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ">
                <svg
                  key={index}
                  xmlns="http://www.w3.org/2000/svg"
                  width={720}
                  height={200}
                  viewBox="0 0 720 200"
                >
                  <text
                    x="50%"
                    y="50%"
                    className="text-center [text-anchor:middle]"
                    dominant-baseline="middle"
                    stroke="#000000"
                    stroke-width="12"
                    stroke-linejoin="round"
                    fill="none"
                  >
                    {word.text}
                  </text>

                  <text
                    x="50%"
                    y="50%"
                    className="text-center [text-anchor:middle]"
                    dominant-baseline="middle"
                    fill={
                      word.text === currSubGroup.word?.text
                        ? '#fbf400'
                        : '#fefbe5'
                    }
                  >
                    {word.text}
                  </text>
                </svg>
              </span>

              <span className=" opacity-0 ">{word.text}</span>
            </span>
          ))}
        </p>
      </div>
    </div>
  );
};

export default Style4;
