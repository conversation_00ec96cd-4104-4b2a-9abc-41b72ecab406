import React from 'react';
import { loadFont } from '@remotion/google-fonts/Rubik';
import { ISegment, IWord } from '@/types/video_v2';

interface IStyle2Props {
  currSubGroup: {
    segment: ISegment;
    word: IWord | undefined;
  };
}

const Style2 = (props: IStyle2Props) => {
  const { currSubGroup } = props;

  const { fontFamily } = loadFont();
  return (
    <div className="fixed z-[999] top-[80%] w-full flex justify-center">
      <div className=" px-12 font-semibold text-center  ">
        <p
          className={`text-white text-6xl flex gap-x-4 gap-y-2 flex-wrap justify-center [animation:_zoomBounce_0.1s_ease-out_forwards] `}
          style={{ fontFamily }}
          key={currSubGroup.word?.text}
        >
          <svg
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            width="624px"
            height="200px"
            viewBox="0 0 624 200"
          >
            <text
              x="50%"
              y="50%"
              className="text-center [text-anchor:middle]"
              dominant-baseline="middle"
              stroke="#000000"
              stroke-width="12"
              stroke-linejoin="round"
              fill="none"
            >
              {currSubGroup.word?.text}
            </text>

            <text
              x="50%"
              y="50%"
              className="text-center [text-anchor:middle]"
              dominant-baseline="middle"
              fill="#fefbe5"
            >
              {currSubGroup.word?.text}
            </text>
          </svg>
        </p>
      </div>
    </div>
  );
};

export default Style2;
