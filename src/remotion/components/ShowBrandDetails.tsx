import React from 'react';
import { IFpVideoDataV2 } from '../types';
import { Img } from 'remotion';

interface IShowBrandDetailsProps {
  branding: NonNullable<IFpVideoDataV2['branding']>;
}

const ShowBrandDetails = (props: IShowBrandDetailsProps) => {
  const { branding } = props;
  return (
    <div
      className={` absolute inset-0 flex items-center justify-center text-white p-8 `}
    >
      <div className="bg-black/50 p-6 py-10 rounded-3xl w-full flex flex-col gap-8 ">
        {branding?.logo && branding.logo.url !== '' && (
          <div className=" justify-center hidden " id="logo-parent">
            <Img
              src={branding.logo.url}
              width={120}
              height={120}
              alt="brand logo"
              className=" rounded-[30px] "
              onLoad={() => {
                const parentElement = document.getElementById('logo-parent');
                if (!parentElement) return;
                parentElement.style.display = 'flex';
              }}
              onError={() => {
                const parentElement = document.getElementById('logo-parent');
                if (!parentElement) return;
                parentElement.style.display = 'none';
              }}
            />
          </div>
        )}
        <div className=" h-[180px] " data-height="180">
          <p
            className=" text-6xl font-semibold text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit "
            data-fontsize="60"
          >
            {branding?.brand_name}
          </p>
        </div>
        <div className=" h-[160px] " data-height="160">
          <p
            className=" text-4xl text-center [text-shadow:_-1px_2px_1px_rgb(0_0_0)] text-and-container-fit "
            data-fontsize="36"
          >
            {branding?.mobile && branding?.website
              ? branding?.mobile + ' / '
              : (branding?.mobile ?? '')}
            {branding?.website}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ShowBrandDetails;
