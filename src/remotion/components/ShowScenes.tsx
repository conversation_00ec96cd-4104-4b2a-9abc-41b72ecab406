import { ANIMATIONS } from '../../types/video_v2';
import React, { useEffect, useMemo } from 'react';
import { getCurrContent, getNextContent } from '../utils/video-v2/content';
import {
  Freeze,
  Img,
  interpolate,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';
import gsap from 'gsap';
import { addAnimation } from '../utils/video-v2/animation';
import { LoopableOffthreadVideo } from '../utils/LoopableOffthreadVideo';
import { IFpVideoDataV2 } from '../types';

const ANIMATION_DURATIONS_IN_SEC = 0.8;

interface IShowScenesProps {
  scenes: NonNullable<IFpVideoDataV2['scenes']>;
  timeInSec: number;
}

const ShowScenes = (props: IShowScenesProps) => {
  const { scenes, timeInSec } = props;
  const { fps } = useVideoConfig();
  const frame = useCurrentFrame();

  const currContent = useMemo(
    () => getCurrContent(scenes, timeInSec),
    [timeInSec, scenes],
  );
  const nextContent = useMemo(
    () => getNextContent(scenes, timeInSec),
    [timeInSec, scenes],
  );

  useEffect(() => {
    const timeline = gsap.timeline({ repeat: 0, paused: true });

    const currContentDiv = document.getElementById('currContent');
    const nextContentDiv = document.getElementById('nextContent');

    if (currContentDiv) {
      addAnimation({
        animation: ANIMATIONS.SIMPLE_SWIPE,
        animationDuration: ANIMATION_DURATIONS_IN_SEC,
        currContent,
        nextContent,
        currContentDiv,
        nextContentDiv,
        timeline,
      });
    }

    timeline.seek(Math.max(0, timeInSec - currContent?.start));
  }, [timeInSec, currContent, nextContent]);

  const zoomAnimation = interpolate(
    frame,
    [currContent.start * fps, currContent.end * fps],
    [1, 1.2],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    },
  );

  return (
    <div>
      {currContent && (
        <div className="fixed inset-0 z-30" id="currContent">
          {currContent.assets && currContent.assets?.length > 0 ? (
            currContent.assets[0].type === 'video' ? (
              <Sequence
                from={currContent.start * fps}
                durationInFrames={(currContent.end - currContent.start) * fps}
              >
                <LoopableOffthreadVideo
                  key={currContent.assets[0].url}
                  src={currContent.assets[0].url}
                  className="w-full h-full object-contain"
                  startFrom={0}
                  muted
                  onError={() => {}}
                  loop
                />
              </Sequence>
            ) : (
              <Img
                key={currContent.assets[0].url}
                src={currContent.assets[0].url}
                className="w-full h-full object-contain"
                style={{
                  scale: zoomAnimation,
                }}
              />
            )
          ) : null}
        </div>
      )}
      {nextContent && (
        <div className="fixed inset-0 z-10" id="nextContent">
          {nextContent.assets && nextContent.assets?.length > 0 ? (
            nextContent.assets[0].type === 'video' ? (
              <Sequence
                from={(nextContent.start - ANIMATION_DURATIONS_IN_SEC) * fps}
                durationInFrames={ANIMATION_DURATIONS_IN_SEC * fps}
              >
                <Freeze frame={0}>
                  {/* <p className=" text-7xl text-white ">fuck</p> */}
                  <LoopableOffthreadVideo
                    key={nextContent.assets[0].url}
                    src={nextContent.assets[0].url}
                    className="w-full h-full object-contain"
                    startFrom={0}
                    muted
                    endAt={2} // Show only first frame
                    onError={() => {}}
                  />
                </Freeze>
              </Sequence>
            ) : (
              <Img
                key={nextContent.assets[0].url}
                src={nextContent.assets[0].url}
                className="w-full h-full object-contain"
              />
            )
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ShowScenes;
