import React from 'react';
import {
  getRemotionEnvironment,
  Loop,
  OffthreadVideo,
  RemotionOffthreadVideoProps,
  useVideoConfig,
  Video,
} from 'remotion';

const LoopedOffthreadVideo: React.FC<
  RemotionOffthreadVideoProps & { videoDurationInSec: number }
> = (props) => {
  // const [duration, setDuration] = useState<number | null>(null);
  // const [handle] = useState(() => delayRender());
  const { fps } = useVideoConfig();

  // useEffect(() => {
  //   const controller = mediaParserController();

  //   parseMedia({
  //     src: props.src,
  //     acknowledgeRemotionLicense: true,
  //     controller,
  //     fields: {
  //       slowDurationInSeconds: true,
  //     },
  //   })
  //     .then(({ slowDurationInSeconds }) => {
  //       setDuration(slowDurationInSeconds);
  //       continueRender(handle);
  //     })
  //     .catch((err) => {
  //       cancelRender(err);
  //     });

  //   return () => {
  //     continueRender(handle);
  //     controller.abort();
  //   };
  // }, [handle, props.src]);

  return (
    <Loop
      durationInFrames={Math.floor(props.videoDurationInSec * fps)}
      layout="none"
    >
      <OffthreadVideo {...props} />
    </Loop>
  );
};

export const LoopableOffthreadVideo: React.FC<
  RemotionOffthreadVideoProps & {
    loop?: boolean;
    videoDurationInSec?: number;
  }
> = ({ loop, videoDurationInSec = 30, ...props }) => {
  if (getRemotionEnvironment().isRendering) {
    if (loop) {
      return (
        <LoopedOffthreadVideo
          videoDurationInSec={videoDurationInSec}
          {...props}
        />
      );
    }

    return <OffthreadVideo {...props} />;
  }

  return <Video loop={loop} {...props}></Video>;
};
