import { ISegment, ISubtitle, IWord } from '@/types/video_v2';

/**
 * how many characters to be present in a group of subtitle
 */
const MAX_CHAR_COUNT = 30;
/**
 * detect the pause in subtitle and break as per that
 */
const MAX_TIME_DIFFERENCE_IN_SEC = 0.2;

export const getSubtitlesGroup = (words: ISubtitle) => {
  // filter out the spaces from the words

  const group: ISegment[] = [];
  let i = 0;

  while (i < words.length) {
    const groupedWords: IWord[] = [];

    while (
      i < words.length &&
      (groupedWords.length > 0
        ? words[i].start - groupedWords[groupedWords.length - 1].end <=
          MAX_TIME_DIFFERENCE_IN_SEC
        : true) &&
      groupedWords.reduce(
        (prevLength, { text }) => prevLength + text.length,
        0,
      ) +
        words[i].text.length <=
        MAX_CHAR_COUNT
    ) {
      groupedWords.push(words[i]);
      i++;
    }

    if (groupedWords.length === 0) {
      groupedWords.push(words[i]);
      i++;
    }

    group.push({
      text: groupedWords
        .reduce((prev, { text }) => prev + ' ' + text, '')
        .trim(),
      start: groupedWords[0].start,
      end: groupedWords[groupedWords.length - 1]?.end,
      words: groupedWords,
    });
  }

  return group;
};

export const getCurrSubtitleGroup = (
  subtitleGroup: ISegment[],
  timeInSec: number,
) => {
  // fist lets find which segement is the one we need
  const segment = subtitleGroup
    .reverse()
    .find((item) => item.start <= timeInSec); // a subtitle group will still be returned if endtime < timeinsec

  if (!segment) return;

  // finding which block lies
  let word: IWord | undefined;

  segment.words.forEach((item) => {
    if (item.start <= timeInSec && item.end > timeInSec) {
      word = item;
    }
  });

  // if (!word) throw new Error('Something went wrong in getCurrSubtitleGroup');

  return { segment, word };
};
