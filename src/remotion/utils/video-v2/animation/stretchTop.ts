import { IFpVideoDataV2 } from '@/remotion/types';

/**
 * the nextcontent will be at top
 * and it will come down
 */

export const stretchTop = (props: {
  timeline: gsap.core.Timeline;
  currContent: NonNullable<IFpVideoDataV2['scenes']>[number];
  currContentDiv: HTMLElement;
  nextContentDiv: HTMLElement | null;
  animationDuration: number;
}) => {
  const {
    timeline,
    currContent,
    currContentDiv,
    nextContentDiv,
    animationDuration,
  } = props;

  const animationStartAfter =
    currContent.end - animationDuration - currContent.start - 0.05;

  // making everything default
  timeline.set(currContentDiv, {
    x: 0,
    y: 0,
    scaleX: 1,
    scaleY: 1,
    opacity: 1,
    filter: 'blur(0px)',
    transformOrigin: 'center',
  });

  // only do animation when the next content is there
  if (nextContentDiv)
    timeline
      .set(nextContentDiv, {
        x: 0,
        y: '100%',
        scaleX: 1,
        scaleY: 2,
        opacity: 1,
        filter: 'blur(5px)',
        transformOrigin: 'center',
      })
      .to(currContentDiv, {
        x: 0,
        y: '-100%',
        scaleX: 1,
        scaleY: 2,
        opacity: 1,
        filter: 'blur(5px)',
        transformOrigin: 'bottom',
        delay: animationStartAfter,
        duration: animationDuration,
        ease: 'power3.in',
      })
      .to(
        nextContentDiv,
        {
          x: 0,
          y: 0,
          scaleX: 1,
          scaleY: 1,
          opacity: 1,
          filter: 'blur(0px)',
          transformOrigin: 'center',
          duration: animationDuration,
          ease: 'power3.in',
        },
        `-=${animationDuration}`,
      );
};
