import { IFpVideoDataV2 } from '@/remotion/types';
import { ANIMATIONS } from '../../../types/video_v2';
import { slideLeft } from './animation/slideLeft';
import { stretchDown } from './animation/stretchDown';
import { stretchLeft } from './animation/stretchLeft';
import { stretchTop } from './animation/stretchTop';

const indexToFunctionMap = {
  0: slideLeft,
  1: stretchDown,
  2: stretchLeft,
  3: stretchTop,
};

export const addAnimation = (props: {
  animation: ANIMATIONS;
  timeline: gsap.core.Timeline;
  currContent: NonNullable<IFpVideoDataV2['scenes']>[number];
  nextContent?: NonNullable<IFpVideoDataV2['scenes']>[number];
  currContentDiv: HTMLElement;
  nextContentDiv: HTMLElement | null;
  animationDuration: number;
}) => {
  //   CustomEase.create('myEase', 'M0,0 C0.755,0.050 0.855,0.060 1,1');

  // stretchLeft(props);

  indexToFunctionMap[
    Math.floor(props.currContent.end % 4) as keyof typeof indexToFunctionMap
  ](props);
};
