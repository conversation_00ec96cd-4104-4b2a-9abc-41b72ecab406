import { IFpVideoDataV2 } from '@/remotion/types';

export const getCurrContent = (
  contents: NonNullable<IFpVideoDataV2['scenes']>,
  timeInSec: number,
) => {
  const filteredContent = contents.filter(
    (content) => content.start <= timeInSec && content.end > timeInSec,
  );

  if (filteredContent.length > 0) return filteredContent[0];
  else {
    /**
     * for the last content to appear till the video
     * this could be removed if we show branding details at the end
     */
    const c = { ...contents[contents.length - 1] };
    c.end = c.end + 100;
    return c;
  }
};

export const getNextContent = (
  contents: NonNullable<IFpVideoDataV2['scenes']>,
  timeInSec: number,
) => {
  // assuming contents is sorted in ascending order on basis of endTime
  let content: NonNullable<IFpVideoDataV2['scenes']>[number] | undefined;

  [...contents].reverse().forEach((c) => {
    if (c.start > timeInSec) content = c;
  });

  return content;
};
