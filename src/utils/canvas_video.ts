import { showToastMessage } from '@/modules/toast';
import { HTMLMediaElementWithCaptureStream } from '@/types/canvas_video';
import { IVideoData } from '@/types/video';
import { drawTextWithSizeAdjustmentAndReturnHeight } from './banner_image';

interface IVisibleImage {
  image: HTMLImageElement;
  zoomIncrement: number;
  startDuration: number;
  endDuration: number;
}

interface IVisibleScript {
  text: string;
  startDuration: number;
  endDuration: number;
}

// pause if it increases beyond this
const MAX_ZOOM_FACTOR = 1.3;

const globals: {
  requestAnimationFrameReq: number;
  allVideoImages: IVisibleImage[];
  currentlyVisibleImage: IVisibleImage | undefined;
  totalDurationSoFar: number;
  intervalReq: number;
  currentlyVisibleScript: IVisibleScript | undefined;
  allVideoScripts: IVisibleScript[];
} = {
  requestAnimationFrameReq: -1,
  allVideoImages: [],
  currentlyVisibleImage: undefined,
  totalDurationSoFar: 0,
  intervalReq: -1,
  currentlyVisibleScript: undefined,
  allVideoScripts: [],
};

const drawImage = async (
  canvas: HTMLCanvasElement,
  image: HTMLImageElement,
  zoomFactor: number,
) => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Error in initalizing ctx');
    const imageWidth = image.width; //WIDTH * devicePixelRatio;
    const imageHeight = image.height; //HEIGHT * devicePixelRatio;
    // Calculate the scaled width and height based on the zoom factor
    const scaledWidth = imageWidth * zoomFactor;
    const scaledHeight = imageHeight * zoomFactor;
    // Calculate the position to center the scaled image on the canvas
    const offsetX = (canvas.width - scaledWidth) / 2;
    const offsetY = (canvas.height - scaledHeight) / 2;
    // Draw the scaled image on the canvas
    ctx.drawImage(image, offsetX, offsetY, scaledWidth, scaledHeight);
    resolve(true);
  });
};

const drawText = (canvas: HTMLCanvasElement, text: string) => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  const paddingX = 48;
  const marginBottom = 48;

  // Draw slightly dark background rectangle for better readability
  ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
  const backgroundHeight = 140;
  const backgroundY = canvas.height - marginBottom - backgroundHeight;
  const backgroundWidth = canvas.width - 2 * paddingX;
  ctx.fillRect(paddingX, backgroundY, backgroundWidth, backgroundHeight);
  drawTextWithSizeAdjustmentAndReturnHeight(ctx, {
    x: paddingX + 24,
    y: backgroundY + 36,
    width: backgroundWidth - 24 * 2,
    height: backgroundHeight - 36 * 2,
    fontSize: 40,
    fontColor: '#fff',
    fontWeight: 500,
    textAlign: 'center',
    verticalAlign: 'center',
    text,
    value: '',
  });
};

const renderNextVideoFrame = async (canvas: HTMLCanvasElement) => {
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Error in initalizing ctx');
  ctx.clearRect(0, 0, canvas.width, canvas.height); // clear canvas
  if (globals.currentlyVisibleImage) {
    const zoomFactor = 1 + globals.currentlyVisibleImage.zoomIncrement;
    if (zoomFactor > MAX_ZOOM_FACTOR) {
      // pause or do nothing
    } else {
      globals.currentlyVisibleImage.zoomIncrement =
        globals.currentlyVisibleImage.zoomIncrement + 0.0006;
    }
    await drawImage(canvas, globals.currentlyVisibleImage.image, zoomFactor);
  }
  if (globals.currentlyVisibleScript) {
    drawText(canvas, globals.currentlyVisibleScript.text);
  }
  globals.requestAnimationFrameReq = window.requestAnimationFrame(
    () => void renderNextVideoFrame(canvas),
  );
};

const initState = ({
  data,
  videoWidth,
}: //videoHeight
{
  data: IVideoData;
  videoWidth: number;
  videoHeight: number;
}) => {
  globals.allVideoImages = [];
  globals.totalDurationSoFar = 0;
  globals.allVideoScripts = [];
  // create all images upfront instead of creating everytime in new frame
  data.frames.forEach((frame) => {
    // use only first image since this will be most relevant
    // Future: we can create a new video using all 2nd images
    const imageData = frame.images?.[0];
    if (imageData) {
      const imageElement = new Image();
      // either consume blob or use compliant cross-origin
      // https://stackoverflow.com/questions/56970491/why-is-video-from-canvas-capturestream-empty-when-using-drawimage-with-mediareco
      imageElement.crossOrigin = 'anonymous';
      const imageUrl = new URL(imageData.url);
      imageUrl.searchParams.set('w', videoWidth.toString());
      imageUrl.searchParams.set('h', '' /*videoHeight.toString()*/); // do not crop image
      imageElement.src = imageUrl.toString();
      const previousEndDuration = globals.allVideoImages.length
        ? globals.allVideoImages[globals.allVideoImages.length - 1].endDuration
        : 0;
      globals.allVideoImages.push({
        image: imageElement,
        zoomIncrement: 0.0,
        startDuration: previousEndDuration,
        endDuration: previousEndDuration + frame.duration,
      });
    }
    const previousFrameEndDuration = globals.allVideoScripts.length
      ? globals.allVideoScripts[globals.allVideoScripts.length - 1].endDuration
      : 0;
    globals.allVideoScripts.push({
      text: frame.script_text,
      startDuration: previousFrameEndDuration,
      endDuration: previousFrameEndDuration + frame.duration,
    });
  });
  globals.currentlyVisibleImage = globals.allVideoImages[0]; // start with first and keep updating as per timer & [start, end] brackets
  globals.currentlyVisibleScript = globals.allVideoScripts[0];
  // cancel existing animation & interval
  window.cancelAnimationFrame(globals.requestAnimationFrameReq);
  clearInterval(globals.intervalReq);
};

export const renderVideo = ({
  canvas,
  data,
  stopped,
  videoWidth,
  videoHeight,
  resetVideoState,
}: {
  canvas: HTMLCanvasElement;
  data: IVideoData;
  stopped: boolean;
  videoWidth: number;
  videoHeight: number;
  resetVideoState: boolean;
}) => {
  // toggling stopped true/false will also trigger this flow
  // hence relying on totalDurationSoFar to check current state
  if (
    resetVideoState ||
    globals.totalDurationSoFar === 0 ||
    globals.totalDurationSoFar >= data.total_duration
  ) {
    // reset all variables
    initState({
      data,
      videoHeight,
      videoWidth,
    });
  }

  if (stopped) {
    window.cancelAnimationFrame(globals.requestAnimationFrameReq);
    clearInterval(globals.intervalReq);

    // cancelling animation frame will make canvas empty, so manually render current image with existing zoom factor
    if (globals.currentlyVisibleImage) {
      const zoomFactor = 1 + globals.currentlyVisibleImage.zoomIncrement;
      void drawImage(canvas, globals.currentlyVisibleImage.image, zoomFactor);
    }
    drawText(canvas, globals.currentlyVisibleScript!.text);
    return;
  }

  // every 500 ms, check & update image object + script text
  // image & script text rendering will be independent
  const INTERVAL_DURATION = 500;
  globals.intervalReq = window.setInterval(() => {
    globals.currentlyVisibleImage = globals.allVideoImages.find((item) => {
      return (
        globals.totalDurationSoFar >= item.startDuration &&
        globals.totalDurationSoFar <= item.endDuration
      );
    });
    globals.currentlyVisibleScript = globals.allVideoScripts.find((item) => {
      return (
        globals.totalDurationSoFar >= item.startDuration &&
        globals.totalDurationSoFar <= item.endDuration
      );
    });
    globals.totalDurationSoFar += INTERVAL_DURATION;

    if (globals.totalDurationSoFar > data.total_duration) {
      // stop interval and current animation
      clearInterval(globals.intervalReq);
      window.cancelAnimationFrame(globals.requestAnimationFrameReq);
    }
  }, INTERVAL_DURATION);

  globals.requestAnimationFrameReq = window.requestAnimationFrame(
    () => void renderNextVideoFrame(canvas),
  );
};

function recordVideo(
  canvas: HTMLCanvasElement,
  time: number,
  audioStream?: MediaStream,
): Promise<string> {
  return new Promise(function (resolve, reject) {
    const recordedChunks: Blob[] = [];
    const canvasStream = canvas.captureStream(25); // 25 fps stream from canvas
    const videoTrack = canvasStream.getVideoTracks()[0]; // Get video track from canvas stream
    const audioTrack = audioStream!.getAudioTracks()[0]; // Get audio track from provided audio stream

    // Create new MediaStream with video and audio tracks
    const combinedStream = new MediaStream([videoTrack, audioTrack]);
    //const combinedStream = new MediaStream(canvasStream)

    // Specify MIME type for recording (e.g., WebM with VP9 codec)
    const mimeType = 'video/webm;codecs=h264';

    const mediaRecorder = new MediaRecorder(combinedStream, { mimeType });

    // Event listener for dataavailable
    mediaRecorder.ondataavailable = function (event) {
      recordedChunks.push(event.data);
    };

    // Event listener for stop
    mediaRecorder.onstop = function () {
      // before merging blobs change output mime
      // const blob = new Blob(recordedChunks, { type: "video/mp4" });
      const blob = new Blob(recordedChunks, { type: 'video/webm' });
      const url = URL.createObjectURL(blob);
      resolve(url);
    };

    // Event listener for errors
    mediaRecorder.onerror = function () {
      // not standard property? since TS is complaining
      //console.error('MediaRecorder error:', event.error);
      //reject(event.error);
      console.error('MediaRecorder error');
      reject('MediaRecorder error');
    };

    // Start recording immediately
    mediaRecorder.start();

    // Stop recording after specified time
    setTimeout(() => {
      if (mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
      }
    }, time + 1000); // Stop after specified time + some buffer
  });
}

export const recordAndDownloadVideo = async ({
  canvas,
  audio,
  videoDuration,
  videoCaption,
}: {
  canvas: HTMLCanvasElement;
  audio: HTMLMediaElementWithCaptureStream;
  videoDuration: number;
  videoCaption: string;
}) => {
  // not supported in all browsers
  const audioStream = audio.captureStream ? audio.captureStream() : undefined;

  try {
    const videoUrl = await recordVideo(canvas, videoDuration, audioStream);
    const link = document.createElement('a');
    link.setAttribute(
      'download',
      videoCaption.substr(0, 40).replaceAll(' ', '_') + '....webm',
    );
    link.setAttribute('href', videoUrl);
    link.click();
    showToastMessage('Video Downloaded', 'success');
  } catch (error: unknown) {
    if (
      error &&
      typeof (error as { toString: () => string }).toString === 'function'
    ) {
      showToastMessage(
        (error as { toString: () => string }).toString(),
        'error',
      );
    } else {
      showToastMessage('An unknown error occurred', 'error');
    }
  }
};
