import {
  IBannerTemplate,
  IButtonElement,
  IImageElement,
  ITemplateElement,
  ITextArrElement,
  ITextProps,
} from '@/types/banner_templates';
import { logError } from '.';

const TEXT_MARGIN_TOP = 48;
const MIN_FONT_SIZE_SUPPORTED = 10;
const FONT_FAMILY = 'Arial';

function splitTextIntoLines(
  ctx: CanvasRenderingContext2D,
  text: string,
  maxWidth: number,
) {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = words[0];

  for (let i = 1; i < words.length; i++) {
    const testLine = currentLine + ' ' + words[i];
    const testWidth = ctx.measureText(testLine).width;

    if (testWidth <= maxWidth) {
      currentLine = testLine;
    } else {
      lines.push(currentLine);
      currentLine = words[i];
    }
  }
  lines.push(currentLine);

  return lines;
}

export function drawTextWithSizeAdjustmentAndReturnHeight(
  ctx: CanvasRenderingContext2D,
  props: ITextProps & ITemplateElement['container'] & { text: string },
) {
  const {
    x,
    y,
    width,
    height,
    text,
    textAlign,
    fontColor,
    fontWeight,
    italic,
    verticalAlign,
    textDecoration,
    underlineYOffset = 6,
    underlineStrokeWidth = 1,
  } = props;

  let textLines: string[] = [];
  let textFits = false;
  let fontSize = props.fontSize;

  // Try to fit the text within the specified boundary
  while (!textFits && fontSize >= MIN_FONT_SIZE_SUPPORTED) {
    ctx.font = `${
      italic ? 'italic' : ''
    } ${fontWeight} ${fontSize}px ${FONT_FAMILY}`;
    textLines = splitTextIntoLines(ctx, text, width);
    const totalTextHeight = textLines.length * fontSize;

    if (totalTextHeight <= height) {
      textFits = true;
    } else {
      fontSize -= 2; // Decrease font size and try again
    }
  }

  // if (verticalAlign === 'center') {
  //   ctx.textBaseline = 'middle';
  // } else {
  //   ctx.textBaseline = 'top';
  // }

  ctx.textBaseline = 'top';

  ctx.fillStyle = fontColor;
  const singleLineHeight = fontSize;
  // Draw each line of text
  let yPos = y;
  if (verticalAlign === 'center') {
    const totalTextHeight = singleLineHeight * textLines.length;
    yPos = y + height / 2 - totalTextHeight / 2;
  }
  let xPos = x;
  textLines.forEach((line: string) => {
    const textMetrics = ctx.measureText(line);
    const textWidth = textMetrics.width;
    // Align text based on textAlign property
    if (textAlign == 'center') {
      xPos = x + (width - textWidth) / 2;
    }
    if (textAlign == 'right') {
      xPos = x + (width - textWidth);
    }
    ctx.fillText(line, xPos, yPos);

    // Draw a line underneath the text
    if (textDecoration === 'underline') {
      ctx.beginPath();
      ctx.moveTo(
        xPos,
        yPos + textMetrics.actualBoundingBoxDescent + underlineYOffset,
      );
      ctx.lineTo(
        xPos + textWidth,
        yPos + textMetrics.actualBoundingBoxDescent + underlineYOffset,
      );
      ctx.lineWidth = underlineStrokeWidth;
      ctx.strokeStyle = fontColor; // Set the line color
      ctx.stroke();
    }

    yPos += fontSize;
  });

  // return height of drawn text, useful for rendering next text in array
  return yPos - y;
}

const loadImage = async (url: string): Promise<CanvasImageSource | null> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = url;

    // to export images from Javascript (toDataURL / toBlob)
    img.setAttribute('crossorigin', 'anonymous');

    img.onload = () => {
      resolve(img);
    };

    img.onerror = () => {
      logError(
        new Error(`Failed to load image: ${url}`),
        'banner_image.loadImage',
      );
      resolve(null);
    };
  });
};

const drawTexts = (ctx: CanvasRenderingContext2D, element: ITextArrElement) => {
  const container = element.container;
  let yPos = container.y;
  element.texts?.forEach((textNode) => {
    // margin-top: 12
    yPos =
      yPos +
      drawTextWithSizeAdjustmentAndReturnHeight(ctx, {
        x: container.x,
        y: yPos,
        width: container.width,
        height: container.height / element.texts!.length,
        text:
          textNode.textTransform === 'uppercase'
            ? textNode.value?.toUpperCase()
            : textNode.value,
        ...textNode,
      }) +
      TEXT_MARGIN_TOP;
  });
};

const drawButton = (ctx: CanvasRenderingContext2D, element: IButtonElement) => {
  const container = element.container;
  // calculate button width considering text fontSize, button paddingX and container width
  // decrease fontSize if text is too long
  const button = element.buttonProps;
  const text = element.textProps;

  let textValue = text.value;

  if (text.textTransform === 'uppercase') {
    textValue = textValue.toUpperCase();
  }

  // Calculate text size and position
  let fontSize = text.fontSize;
  ctx.font = `${text.fontWeight || 'normal'} ${fontSize}px Arial`;
  let textWidth = ctx.measureText(textValue).width;

  // Adjust font size to fit within the container
  while (textWidth > container.width - 2 * button.paddingX) {
    fontSize--;
    ctx.font = `${text.fontWeight || 'normal'} ${fontSize}px Arial`;
    textWidth = ctx.measureText(textValue).width;
  }

  let buttonX: number = 0;
  const buttonWidth = textWidth + 2 * button.paddingX;
  const buttonY = container.y;
  const buttonHeight = container.height;
  const buttonBorderRadius = button.borderRadius;

  // button position based on buttonProps.align
  if (button.align === 'left') {
    buttonX = container.x;
  } else if (button.align === 'right') {
    buttonX = container.x + container.width - buttonWidth;
  } else if (button.align === 'center') {
    buttonX = container.x + (container.width - buttonWidth) / 2;
  }

  // Draw button
  ctx.fillStyle = button.backgroundColor;
  ctx.beginPath();
  ctx.moveTo(buttonX + buttonBorderRadius, buttonY);
  ctx.lineTo(buttonX + buttonWidth - buttonBorderRadius, buttonY);
  ctx.quadraticCurveTo(
    buttonX + buttonWidth,
    buttonY,
    buttonX + buttonWidth,
    buttonY + buttonBorderRadius,
  );
  ctx.lineTo(
    buttonX + buttonWidth,
    buttonY + buttonHeight - buttonBorderRadius,
  );
  ctx.quadraticCurveTo(
    buttonX + buttonWidth,
    buttonY + buttonHeight,
    buttonX + buttonWidth - buttonBorderRadius,
    buttonY + buttonHeight,
  );
  ctx.lineTo(buttonX + buttonBorderRadius, buttonY + buttonHeight);
  ctx.quadraticCurveTo(
    buttonX,
    buttonY + buttonHeight,
    buttonX,
    buttonY + buttonHeight - buttonBorderRadius,
  );
  ctx.lineTo(buttonX, buttonY + buttonBorderRadius);
  ctx.quadraticCurveTo(buttonX, buttonY, buttonX + buttonBorderRadius, buttonY);
  ctx.closePath();
  ctx.fill();

  // Draw text
  const textX = buttonX + button.paddingX;
  const textY = buttonY + container.height / 2;
  ctx.textBaseline = 'middle';
  ctx.fillStyle = text.fontColor;
  ctx.fillText(textValue, textX, textY);
};

const drawImage = async (
  ctx: CanvasRenderingContext2D,
  element: IImageElement,
) => {
  const container = element.container;
  const image = element.imageProps;

  if (!image.url) {
    return;
  }

  // draw image border
  ctx.strokeStyle = image.borderColor ?? '';
  ctx.lineWidth = image.borderWidth;
  ctx.save();
  ctx.beginPath();

  // Top left corner
  ctx.moveTo(container.x + image.borderRadius, container.y);
  ctx.arcTo(
    container.x + container.width,
    container.y,
    container.x + container.width,
    container.y + container.height,
    image.borderRadius,
  );

  // Bottom right corner
  ctx.arcTo(
    container.x + container.width,
    container.y + container.height,
    container.x,
    container.y + container.height,
    image.borderRadius,
  );
  ctx.arcTo(
    container.x,
    container.y + container.height,
    container.x,
    container.y,
    image.borderRadius,
  );

  // Top right corner
  ctx.arcTo(
    container.x,
    container.y,
    container.x + container.width,
    container.y,
    image.borderRadius,
  );

  ctx.closePath();
  ctx.stroke();
  ctx.clip();
  const img: CanvasImageSource | null = await loadImage(image.url);
  if (img) {
    ctx.drawImage(
      img,
      container.x,
      container.y,
      container.width,
      container.height,
    );
  }
  ctx.restore();
};

export const renderTemplate = async (
  ctx: CanvasRenderingContext2D,
  template: IBannerTemplate,
) => {
  // Loop through each element in the template
  for (const element of template.elements) {
    // Apply zIndex, if provided
    if (element.zIndex !== undefined) {
      ctx.globalCompositeOperation = 'source-over';
    }

    // Handle different element types
    if (element.type === 'textArr') {
      drawTexts(ctx, element);
    } else if (element.type === 'button') {
      drawButton(ctx, element);
    } else if (element.type === 'image') {
      await drawImage(ctx, element);
    }
  }
};
