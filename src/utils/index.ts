import FetchError from '../actions/FetchError';
import { showToastMessage } from '../modules/toast';

export const IS_PROD = process.env.NODE_ENV === 'production';

export const logError = (error: FetchError | Error, source: string) => {
  if (IS_PROD) {
    window?.sentry?.logErrorToSentry(error, source);
  } else {
    console.error(error, source);
  }
};

export const logApiErrorAndShowToastMessage = (
  error: FetchError | Error,
  source: string,
) => {
  let message: string = error.message;

  if (error instanceof FetchError && error.response?.error?.message) {
    message = error.response?.error?.message as string;
  }

  if (IS_PROD) {
    window?.sentry?.logErrorToSentry(error, source);
  } else {
    console.error(error, source);
  }

  // todo events & sentry
  showToastMessage(message, 'error');
};

export const isValidURL = (url: string) => {
  const pattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$',
    'i',
  ); // fragment locator
  return !!pattern.test(url);
};

export const getUpdatedCloudflareImageUrl = (
  url: string,
  params: {
    width: number | string;
    height: number | string;
    q?: number | string;
  },
): string => {
  const { width, height, q = 100 } = params;

  // Check if URL contains cdn-cgi/image
  if (!url.includes('cdn-cgi/image/')) {
    return url; // Return as is if it's not a Cloudflare image URL
  }

  // Extract existing parameters
  const regex = /(cdn-cgi\/image\/)([^/]+)(\/https:\/\/)/;
  const match = url.match(regex);

  let newParams = `width=${width},height=${height}`;

  if (match) {
    const existingParams = match[2];

    // Remove existing width and height
    const updatedParams = existingParams
      .split(',')
      .filter(
        (param) =>
          !param.startsWith('width=') &&
          !param.startsWith('height=') &&
          !param.startsWith('q='),
      )
      .join(',');

    // Append new width & height
    newParams = updatedParams
      ? `${newParams},${updatedParams},q=${q}`
      : `${newParams},q=${q}`;

    return url.replace(regex, `$1${newParams}$3`);
  }

  return url; // Fallback if no match
};

export const getUpdatedUnsplashImageUrl = (
  url: string,
  params: {
    width: number;
    height: number;
  },
): string => {
  const { width, height } = params;
  const urlObj = new URL(url);
  urlObj.searchParams.set('crop', 'edges');
  urlObj.searchParams.set('fit', 'crop');
  urlObj.searchParams.set('w', width.toString());
  urlObj.searchParams.set('h', height.toString());

  return urlObj.toString();
};

export const getUpdatedPexelsImageUrl = (
  url: string,
  params: { width: number; height: number },
): string => {
  const updatedUrl = new URL(url);
  updatedUrl.searchParams.set('w', params.width.toString());
  updatedUrl.searchParams.set('h', params.height.toString());
  updatedUrl.searchParams.set('fit', 'crop');
  updatedUrl.searchParams.set('dpr', '2');
  return updatedUrl.toString();
};

export const getUpdatedUrl = (
  url: string,
  params: {
    width: number;
    height: number;
  },
): string => {
  if (url.includes('base64,')) {
    return url;
  } else if (url.includes('groweasy.ai/cdn-cgi/image')) {
    return getUpdatedCloudflareImageUrl(url, params);
  } else if (url.includes('bannerbot-public.s3.ap-south-1')) {
    return url;
  } else {
    return getUpdatedUnsplashImageUrl(url, params);
  }
};

/**
 * Calculates the maximum possible target width and height for an image while maintaining
 * the given aspect ratio and ensuring dimensions do not exceed the original image size.
 */
export const getImageTransformationParams = ({
  imageWidth,
  imageHeight,
  targetAspectRatio,
}: {
  imageWidth: number;
  imageHeight: number;
  targetAspectRatio: number;
}): { targetWidth: number; targetHeight: number } => {
  let targetWidth = imageWidth;
  let targetHeight = Math.round(targetWidth / targetAspectRatio);

  if (targetHeight > imageHeight) {
    targetHeight = imageHeight;
    targetWidth = Math.round(targetHeight * targetAspectRatio);
  }

  return { targetWidth, targetHeight };
};

export const converNormalUrlToCdnUrl = (
  url: string,
  param?: {
    width?: number;
    height?: number;
    q?: number;
    fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  } & Record<string, string | number>,
) => {
  if (!url) return '';
  // const {
  //   width,
  //   height,
  //   q, // ranges from 1 to 100; by default 85
  //   fit,
  // } = param || {};

  const baseUrl = 'https://groweasy.ai/cdn-cgi/image';

  const paramsArray: string[] = [];
  // if (width) paramsArray.push(`width=${width}`);
  // if (height) paramsArray.push(`height=${height}`);
  // if (q) paramsArray.push(`q=${q}`);
  // if (fit) paramsArray.push(`fit=${fit}`);
  if (param)
    Object.keys(param).forEach((key) => {
      paramsArray.push(`${key}=${param[key as keyof typeof param]}`);
    });

  return `${baseUrl}/${paramsArray.join(',')}/${url}`;
};

/**
 * adds width and height to image, if the url is of s3 it will use the cloudflare
 * to set height and width, else if it already a cloudflare image or upslash url
 * then it just sets the params
 */
export const changeWidthHeightOfImageUrl = (
  url: string,
  params: {
    width: number;
    height: number;
    q?: number;
    fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  } & Record<string, string | number>,
) => {
  if (url.includes('base64,')) {
    // if the url is base64 which is very rare in our codebase then it ignores
    return url;
  } else if (url.includes('groweasy.ai/cdn-cgi/image')) {
    const regex = /(cdn-cgi\/image\/)([^/]+\/)(https.*)/;
    const match = url.match(regex);

    if (!match || !match[3])
      throw new Error('Failed to extract original URL from Cloudflare CDN');

    return converNormalUrlToCdnUrl(match[3], params);
  } else if (url.includes('images.unsplash.com')) {
    return getUpdatedUnsplashImageUrl(url, {
      width: params.width,
      height: params.height,
    });
  } else if (url.includes('images.pexels.com')) {
    return getUpdatedPexelsImageUrl(url, {
      width: params.width,
      height: params.height,
    });
  } else {
    return converNormalUrlToCdnUrl(url, params);
  }
};

export const openUrlInNewTab = (url: string) => {
  const absoluteUrl = url.startsWith('http') ? url : `http://${url}`;
  if (window?.bridge) {
    window.bridge.postMessage(
      JSON.stringify({
        method: 'launch_url',
        args: absoluteUrl,
      }),
    );
  } else {
    window?.open(absoluteUrl, '_blank');
  }
};
