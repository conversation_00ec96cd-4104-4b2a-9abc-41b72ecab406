import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import {
  Analytics,
  getAnalytics,
  logEvent as logFirebaseEvent,
} from 'firebase/analytics';
import { Auth, getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: 'AIzaSyAqLfLUeaWzBDhJlc5oyo-PPXkR3F-ftRg',
  authDomain: 'banner-bot-675a8.firebaseapp.com',
  projectId: 'banner-bot-675a8',
  storageBucket: 'banner-bot-675a8.appspot.com',
  messagingSenderId: '132777643310',
  appId: '1:132777643310:web:e6ad0a55353c7d7033bebf',
  measurementId: 'G-3G2GQS522F',
};

let firebaseApp: FirebaseApp | undefined;
let analytics: Analytics | undefined;
let auth: Auth | undefined;

export function initializeFirebase() {
  if (!getApps().length) {
    firebaseApp = initializeApp(firebaseConfig);
    if (typeof window !== 'undefined') {
      analytics = getAnalytics(firebaseApp);
    }
    auth = getAuth(firebaseApp);
  }
}

export function getFirebaseApp() {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return firebaseApp;
}

export function getFirebaseAuth() {
  if (!auth) {
    initializeFirebase();
  }
  return auth;
}

export function logEvent(eventName: string, eventParams?: object) {
  if (process.env.NODE_ENV !== 'production') {
    console.info('Analytics Event:', eventName, eventParams);
  } else if (analytics) {
    logFirebaseEvent(analytics, eventName, eventParams);
  } else {
    console.warn('Analytics is not initialized');
  }
}
