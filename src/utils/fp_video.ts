export const fitTextInContainer = (
  items: HTMLCollectionOf<Element>,
  classKey: string,
) => {
  const changeContainerSize = classKey === 'text-and-container-fit';
  Array.from(items as HTMLCollectionOf<HTMLParagraphElement>).forEach(
    (item) => {
      // if (item.innerText === 'Collaborate Seamlessly') debugger;
      const maxFontSize = Number(item.dataset.fontsize) || 100;

      let currFontSize = parseFloat(window.getComputedStyle(item).fontSize);
      const lineHeightValue = parseFloat(
        window.getComputedStyle(item).lineHeight,
      );
      const lineHeightRatio = lineHeightValue / currFontSize; // it can be 1.2 or 1 or 1.5

      const containerHeight = Number(item.parentElement?.dataset.height);
      const containerWidth = item.parentElement?.clientWidth;
      if (!containerHeight || !containerWidth) return;

      while (
        (item.scrollHeight > containerHeight ||
          item.scrollWidth > containerWidth) &&
        currFontSize > 14
      ) {
        currFontSize -= 1;
        item.style.fontSize = `${currFontSize}px`;
        item.style.lineHeight = `${currFontSize * lineHeightRatio}px`;
      }

      while (
        item.scrollHeight <= containerHeight &&
        item.scrollWidth <= containerWidth &&
        currFontSize < maxFontSize // Prevent excessively large fonts
      ) {
        currFontSize += 1;
        item.style.fontSize = `${currFontSize}px`;
        item.style.lineHeight = `${currFontSize * lineHeightRatio}px`;

        if (
          item.scrollHeight > containerHeight ||
          item.scrollWidth > containerWidth
        ) {
          currFontSize -= 1;
          item.style.fontSize = `${currFontSize}px`;
          item.style.lineHeight = `${currFontSize * lineHeightRatio}px`;
          break;
        }
      }

      const totalLines = Math.floor(
        item.scrollHeight / (currFontSize * lineHeightRatio),
      );
      // item.style.height = `${totalLines * fontSize * lineHeightRatio}px`;

      if (changeContainerSize)
        item.parentElement.style.height = `${Math.min(totalLines * currFontSize * lineHeightRatio, containerHeight)}px`;
    },
  );
};
