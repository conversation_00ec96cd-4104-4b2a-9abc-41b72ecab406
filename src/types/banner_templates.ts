export interface IBannerImage {
  id: string;
  url: string;
  thumbnail_url: string;
  author_name: string;
  author_profile_url: string;
  likes: number;
  unsplash_url: string;
  download_location: string;
  alt_description: string;
  width: number;
  height: number;
}

export interface ITemplateElement {
  zIndex?: number;
  id?: string;
  container: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface ITextProps {
  value: string;
  fontWeight: number;
  fontSize: number;
  fontColor: string;
  textAlign?: 'left' | 'center' | 'right';
  textTransform?: string;
  verticalAlign?: string;
  italic?: boolean;
  textDecoration?: string;
  underlineStrokeWidth?: number;
  underlineYOffset?: number;
  variableName?: 'CREATIVE_TITLE_TEXT' | 'CALL_OUT_TEXT' | 'CTA_TEXT';
}

export interface ITextArrElement extends ITemplateElement {
  type: 'textArr';
  texts?: ITextProps[];
}

export interface IButtonElement extends ITemplateElement {
  type: 'button';
  buttonProps: {
    borderRadius: number;
    backgroundColor: string;
    paddingX: number;
    align: 'left' | 'center' | 'right';
  };
  textProps: ITextProps;
}

export interface IImageElement extends ITemplateElement {
  type: 'image';
  imageProps: {
    borderRadius: number;
    borderWidth: number;
    url: string;
    borderColor?: string;
    variableName?: string;
  };
}

export enum BannerSize {
  square = 'square',
  landscape = 'landscape',
  portrait = 'portrait',
}

export interface IBannerTemplate {
  id: string;
  minVersionCode: number;
  description?: string;
  category?: string;
  size: string;
  tags?: string[];
  width: number;
  height: number;
  elements: Array<ITextArrElement | IButtonElement | IImageElement>;
}
