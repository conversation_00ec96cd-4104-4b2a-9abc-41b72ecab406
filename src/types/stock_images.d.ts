interface IPexelsVideoData {
  id: number;
  width: number;
  height: number;
  duration: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  full_res: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tags: any[];
  url: string;
  image: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  avg_color: any;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: IPexelsVideoFile[];
  video_pictures: Array<{
    id: number;
    nr: number;
    picture: string;
  }>;
}

interface IPexelsVideoFile {
  id: number;
  quality: string;
  file_type: string;
  width: number;
  height: number;
  fps: number;
  link: string;
  size: number;
}

interface IImageData {
  author_name: string;
  unsplash_url?: string;
  pexels_url?: string;
  download_location: string;
  alt_description: string;
  author_profile_url: string;
  id: string;
  thumbnail_url: string;
  url: string;
  likes: number;
  width: number;
  height: number;
}
