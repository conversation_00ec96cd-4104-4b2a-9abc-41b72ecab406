interface IBannerbotUser {
  displayName: string | null;
  email: string | null;
  photoUrl: string | null;
  authToken: string;
  uid: string;
  mobile?: string | null;
}

interface IBannerbotVideoAdDetails {
  id: string;
  url: string;
  thumbnail_url: string;
  caption: string;
  template_id: string;
  width: number;
  height: number;
  created_at?: number;
  updated_at?: number; // will be updated once s3 URL is ready
}

interface IBannerbotProject {
  id: string;
  title: string;
  status: BannerbotProjectStatus;
  type: 'AI_ONLY' | 'AI_HUMAN';
  created_at: Timestamp;
  updated_at: Timestamp;
  uid: string;
  details: {
    business_details?: IBannerbotBusinessDetails;
    ad_details?: IBannerbotAdDetails;
    ai_key_benefits?: string[];
    saved_assets?: {
      [BannerbotAdType.SINGLE_IMAGE]?: Array<{
        url: string;
        width: number;
        height: number;
        created_at?: number;
      }>;
      [BannerbotAdType.VIDEO]?: IBannerbotVideoAdDetails[];
      [BannerbotAdType.CAROUSEL]?: Array<{
        width: number;
        height: number;
        image_urls: string[];
        created_at?: number;
      }>;
      [BannerbotAdType.AI_BANNER]?: Array<{
        url: string;
        width: number;
        height: number;
        created_at?: number;
      }>;
    };
    generated_banners?: {
      templates: IBannerTemplate[];
      images: IBannerImage[];
    };
  };
}

interface Window extends Window {
  sentry: {
    initializeSentry: () => void;
    addBreadcrumbToSentry: (eventName: string, payload?: object) => void;
    logErrorToSentry: (error: Error, origin: string) => void;
    setUserToSentry: (user: { id?: string; email?: string }) => void;
  };
  bridge: {
    postMessage: (message: string) => void;
  };
  platform?: 'android' | 'ios' | 'web';
  onLoginSuccess: (user: IBannerbotUser) => void;
  onLogout: () => void;
  onInitViaBridge: (user: IBannerbotUser) => void;
}

interface IBannerbotBusinessDetails {
  product_or_service_description?: string;
  product_images?: Array<{
    url: string;
    width: number;
    height: number;
  }>;
  website?: string;
  mobile?: string;
  business_name?: string;
  business_logo?: {
    square: {
      url: string;
      width: number;
      height: number;
    };
  } | null;
  key_benefits?: string[];
}

enum BannerbotAdType {
  SINGLE_IMAGE = 'SINGLE_IMAGE',
  CAROUSEL = 'CAROUSEL',
  VIDEO = 'VIDEO',
  AI_BANNER = 'AI_BANNER',
}

enum BannerbotProjectStatus {
  DRAFT = 'DRAFT',
  ARCHIVED = 'ARCHIVED',
  COMPLETE = 'COMPLETE',
}

interface IBannerbotAdDetails {
  types: BannerbotAdType[];
}

interface IUserProfile {
  name: string;
  uid: string;
  email: string;
  mobile?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
  mobile_dial_code?: string;
  whatsapp_opt_in?: boolean;
  acquisition_source?: 'web' | 'android' | 'ios';
}

interface IAiAdBanner {
  id: string;
  uid: string;
  project_id: string;
  banner_url: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}
