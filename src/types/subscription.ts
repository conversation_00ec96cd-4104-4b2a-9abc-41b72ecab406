export interface ISubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_in_usd: number;
  period: 'month' | 'year';
  line_items: string[];
  features: Record<string, string>;
  stripe_product_id: string;
  stripe_price_id: string;
  active: boolean;
}

export enum UserSubscriptionSource {
  STRIPE = 'STRIPE',
  APP_SUMO = 'APP_SUMO',
  ADMIN = 'ADMIN',
}

export interface IUserSubscription extends ISubscriptionPlan {
  status: string;
  source: UserSubscriptionSource;
  cancel_at_period_end?: boolean;
  current_period_start?: number;
  current_period_end?: number;
  stripe_subscription_id: string;
  templatized_video_left: number;
  ai_ad_banner_left: number;
}
