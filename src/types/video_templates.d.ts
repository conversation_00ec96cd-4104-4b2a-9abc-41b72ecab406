// interface IFpVideoData {
//   video_caption: string;
//   fps: number; // Must be between 1-60
//   template_id: 'p1' | 'p2' | 'p3' | 'p4' | 'p5' | 'p6' | 'p7' | 'p8' | 'p9';
//   size: 'portrait' | 'square' | 'landscape';
//   width: number;
//   height: number;
//   branding: {
//     website?: string;
//     tagline?: string;
//     mobile?: string;
//     brand_name: string;
//     logo?: {
//       url: string;
//       width: number;
//       height: number;
//     };
//   };

//   base_audio: {
//     url: string;
//   };

//   base_assets?: Array<{
//     type: 'image' | 'video';
//     url: string;
//     video_duration?: number;
//   }>;

//   scenes: Array<{
//     scene_id: number;
//     assets?: Array<{
//       type: 'image' | 'video';
//       url: string;
//     }>;
//     texts?: Array<{
//       value: string;
//     }>;
//   }>;
// }

// Base interface with common properties
// interface IFpVideoDataBase {
//   video_caption: string;
//   fps: number; // Must be between 1-60
//   size: 'portrait' | 'square' | 'landscape';
//   width: number;
//   height: number;
//   branding: {
//     website?: string;
//     tagline?: string;
//     mobile?: string;
//     brand_name: string;
//     logo?: {
//       url: string;
//       width: number;
//       height: number;
//     };
//   };
//   base_assets?: Array<{
//     type: 'image' | 'video';
//     url: string;
//     video_duration?: number;
//   }>;
// }

// // V1 interface
// interface IFpVideoData extends IFpVideoDataBase {
//   template_id: 'p1' | 'p2' | 'p3' | 'p4' | 'p5' | 'p6' | 'p7' | 'p8' | 'p9';
//   base_audio: {
//     url: string;
//   };
//   scenes: Array<{
//     scene_id: number;
//     assets?: Array<{
//       type: 'image' | 'video';
//       url: string;
//     }>;
//     texts?: Array<{
//       value: string;
//     }>;
//   }>;
// }

// // V2 interface
// interface IFpVideoDataV2 extends IFpVideoDataBase {
//   template_id: 'p10';
//   base_audio: {
//     url: string;
//     subtitle: ISubtitle;
//   };
//   scenes: Array<{
//     scene_id: number;
//     assets?: Array<{
//       type: 'image' | 'video';
//       url: string;
//     }>;
//     texts?: Array<{
//       value: string;
//     }>;
//     start: number;
//     end: number;
//   }>;
// }
