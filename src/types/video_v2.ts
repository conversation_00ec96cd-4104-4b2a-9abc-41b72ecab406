export type ISubtitle = IWord[];

export interface ISegment {
  text: string;
  start: number;
  end: number;
  words: IWord[];
}

export interface IWord {
  text: string;
  start: number;
  end: number;
}

// more animations to be added
export enum ANIMATIONS {
  SIMPLE_SWIPE = 'SIMPLE_SWIPE',
}

export interface IContent {
  media: {
    url: string;
    type: 'image' | 'video';
  };
  start: number;
  end: number;
  animation?: ANIMATIONS[];
}
