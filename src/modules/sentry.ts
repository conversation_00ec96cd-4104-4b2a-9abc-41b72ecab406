import {
  addBreadcrumb,
  init,
  captureException,
  setUser,
  User,
} from '@sentry/nextjs';

const initializeSentry = () => {
  init({
    dsn: 'https://<EMAIL>/4508789539667968',

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,

    replaysOnErrorSampleRate: 1.0,

    // This sets the sample rate to be 10%. You may want this to be 100% while
    // in development and sample at a lower rate in production
    replaysSessionSampleRate: 0.1,

    // You can remove this option if you're not planning to use the Sentry Session Replay feature:
    integrations: [
      /*new Sentry.Replay({
          // Additional Replay configuration goes in here, for example:
          maskAllText: true,
          blockAllMedia: true,
        }),*/
    ],
  });
};

const addBreadcrumbToSentry = (eventName: string, payload?: object) => {
  addBreadcrumb({
    category: 'event',
    message: eventName,
    data: payload,
  });
};

const logErrorToSentry = (error: Error, origin: string) => {
  captureException(new Error(origin), {
    extra: {
      origin,
      error,
    },
  });
};

const setUserToSentry = (user: User) => {
  setUser(user);
};

const sentry = {
  initializeSentry,
  addBreadcrumbToSentry,
  logErrorToSentry,
  setUserToSentry,
};

export default sentry;
