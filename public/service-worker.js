const CACHE_NAME = 'bannerbot-cache';


// when ever we hit a request which contains these patter we first check if we have cache
// if we have cache we server that else we first fetch -> clone response -> add response in cache -> return response


  const isUrlEligibleForCaching = (url) => {
    let eligible = false;
    // cache all static resources
    if (url.includes('/_next/static/chunks/') || url.includes('/_next/static/css/')) {
      eligible = true;
    }
    // don't cache in local development
    if (url.includes('localhost')) {
      eligible = false;
    }
    return eligible;
  }

  self.addEventListener('install', ()=>{
    console.info("service worker installed");
    
  })

  self.addEventListener('activate', event => {
    event.waitUntil(
      caches.keys().then(async cacheNames => {
        await Promise.all(
          cacheNames.map(existingCacheName => {
            // Delete old caches except the one with the current version
            if (existingCacheName !== CACHE_NAME) {
              return caches.delete(existingCacheName);
            }
          })
        );
        console.info('service worker activated');
      })
    )
  });

  self.addEventListener('fetch', event =>{
    const targetUrl = event.request.url;
    
    if(isUrlEligibleForCaching(targetUrl)) {
        event.respondWith(
            caches.open(CACHE_NAME).then(cache=>

                cache.match(event.request).then(response =>
                    response || fetch(event.request).then(fetchResponse =>{
                        // we dont have the request in cache so put it in cache
                        if(fetchResponse.ok){
                            cache.put(event.request, fetchResponse.clone());
                        }
                        return fetchResponse;
                    })    
                )
            )
        )
    }
  })

