@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'durerregular';
  src: url('/fonts/durer-webfont.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'inter';
  src: url('/fonts/Inter-Regular.ttf') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'inter';
  src: url('/fonts/Inter-Bold.ttf') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

html {
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'inter';
}

.no-scrollbar {
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  -webkit-appearance: none;
}

@keyframes ModalB2T {
  0% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.animate-ModalB2T {
  -webkit-animation: ModalB2T 0.5s forwards;
  animation: ModalB2T 0.5s forwards;
}

@keyframes preLoader {
  50% {
    background-size: 80%;
  }

  100% {
    background-position: 125% 0;
  }
}

.animate-preLoader {
  -webkit-animation: preLoader 1.2s ease-in-out infinite;
  animation: preLoader 1.2s ease-in-out infinite;
}
