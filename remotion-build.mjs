import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';

// Function to run shell command
const runCommand = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        reject(error);
        return;
      }
      if (stderr) {
        console.error(`stderr: ${stderr}`);
      }
      console.log(`stdout: ${stdout}`);
      resolve(stdout);
    });
  });
};

// Main build function
const buildAndCopyRemotionVideos = async () => {
  try {
    // Step 1: Run Remotion bundle command
    console.log('Starting Remotion bundle...');
    await runCommand('npx remotion bundle --config=remotion.config.ts');

    // Step 2: Define paths
    const buildPath = path.resolve('./build');
    const targetPath = path.resolve('./public/fp-videos-v2');
    const junkPaths = [
      './public/fp-videos-v2/public',
      './public/fp-videos-v2/favicon.ico',
      './public/fp-videos-v2/source-map-helper.wasm',
    ];

    // Step 3: Remove existing content in target directory
    console.log(`Removing existing content in ${targetPath}...`);
    if (fs.existsSync(targetPath)) {
      await fs.promises.rm(targetPath, { recursive: true });
    }

    // Step 4: Create target directory if it doesn't exist
    await fs.promises.mkdir(targetPath, { recursive: true });

    // Step 5: Copy build contents to target directory
    console.log(`Copying build contents to ${targetPath}...`);
    await fs.promises.cp(buildPath, targetPath, { recursive: true });

    // Step 6: remove juck files from target directory
    junkPaths.forEach(async (tpath) => {
      if (fs.existsSync(tpath)) {
        await fs.promises.rm(tpath, { recursive: true });
      }
    });

    console.log('Remotion build and copy completed successfully!');
  } catch (error) {
    console.error('Build process failed:', error);
    process.exit(1);
  }
};

// Run the build process
buildAndCopyRemotionVideos();
