{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/utils/*": ["./src/utils/*"], "@/images/*": ["./public/images/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/components/lib/*"], "@/types/*": ["./src/types/*"], "@/actions/*": ["./src/actions/*"], "@/modules/*": ["./src/modules/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "remotion-bundle.ts"], "exclude": ["node_modules"]}