{"name": "banner-gpt-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "remotion": "remotion studio", "render": "remotion render", "remotion-bundle": "node remotion-build.mjs"}, "dependencies": {"@remotion/cli": "4.0.280", "@remotion/google-fonts": "4.0.280", "@remotion/lambda": "4.0.280", "@remotion/media-parser": "4.0.280", "@remotion/player": "4.0.280", "@remotion/tailwind-v4": "4.0.280", "@sentry/nextjs": "^8.54.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "firebase": "^11.1.0", "gsap": "^3.12.7", "lint-staged": "^15.4.3", "next": "15.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-query": "^3.39.3", "remotion": "4.0.280", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.17.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-query": "^1.2.9", "eslint": "^9.20.0", "eslint-config-next": "15.1.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.14.0", "husky": "^9.1.7", "postcss": "^8.5.3", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^4.0.15", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}, "prettier": {"singleQuote": true, "semi": true, "tabWidth": 2, "printWidth": 80}, "lint-staged": {"*.{tsx,jsx,js,ts}": "eslint --fix --cache", "*.{tsx,jsx,ts,js,css,md}": "prettier --write"}}