// See all configuration options: https://remotion.dev/docs/config
// Each option also is available as a CLI flag: https://remotion.dev/docs/cli

// Note: When using the Node.JS APIs, the config file doesn't apply. Instead, pass options directly to the APIs

import { Config } from '@remotion/cli/config';
import { webpackOverride } from './src/remotion/webpack-override.mjs';

Config.setEntryPoint('./src/remotion/index.ts');

// Override webpack configuration
Config.overrideWebpackConfig(webpackOverride);

// since we are hosting in the url http://localhost:3000/fp-videos-v2/index.html
Config.setPublicPath('/fp-videos-v2/');

// Disable copying public folder during build
// Config.disableCopyPublicFolder();
