{"taskDefinitionArn": "arn:aws:ecs:ap-south-1:194722418874:task-definition/bannerbot-fe:1", "containerDefinitions": [{"name": "bannerbot-fe", "image": "194722418874.dkr.ecr.ap-south-1.amazonaws.com/bannerbot-fe:latest", "cpu": 0, "portMappings": [{"name": "bannerbot-fe-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "PORT", "value": "3000"}, {"name": "NODE_ENV", "value": "production"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/bannerbot-fe", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "bannerbot-fe", "taskRoleArn": "arn:aws:iam::194722418874:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::194722418874:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-12-26T16:28:09.192Z", "registeredBy": "arn:aws:iam::194722418874:root", "tags": []}