import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';
import prettier from 'eslint-plugin-prettier';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    plugins: {
      prettier,
    },
    rules: {
      'prettier/prettier': 0, // Make Prettier errors show up as ESLint errors
      'no-console': ['error', { allow: ['warn', 'error', 'info'] }],
    },
  },
];

export default eslintConfig;
