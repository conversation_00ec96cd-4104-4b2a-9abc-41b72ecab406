This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

### ECS Deployment

1. `aws ecr get-login-password --region ap-south-1 --profile groweasy | docker login --username AWS --password-stdin 194722418874.dkr.ecr.ap-south-1.amazonaws.com`
2. `docker build -t bannerbot-fe .`
3. `docker tag bannerbot-fe:latest 194722418874.dkr.ecr.ap-south-1.amazonaws.com/bannerbot-fe:latest`
4. `docker push 194722418874.dkr.ecr.ap-south-1.amazonaws.com/bannerbot-fe:latest`

#### Create Task definition

1. Go to https://ap-south-1.console.aws.amazon.com/ecs/v2/task-definitions?region=ap-south-1
2. Click on "create new task definition"
3. Task definition family - `bannerbot-fe`
4. Choose `0.5vCPU` and `1 GB memory`
5. Container details- Name = `bannerbot-fe`, Image URI = Copy it from ECR image
6. Port Mapping- 4000 (vidur-backend), 3000 (bannerbot-fe)
7. Environment Variables: `PORT=4000/3000`, `NODE_ENV=production`
8. Leave other settings as it is and click on create

Note-

ecsTaskExecutionRolePolicy must have below permissions if using secret manager

```
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
            ],
            "Resource": [
                "arn:aws:secretsmanager:ap-south-1:010438504697:secret:vidur/prod/vidur-backend-BRiJSG"
            ]
        }
    ]
}
```