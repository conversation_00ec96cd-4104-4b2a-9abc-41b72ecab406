import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'bannerbot.xyz',
      },
      {
        protocol: 'https',
        hostname: 'designeasy.ai',
      },
      {
        protocol: 'https',
        hostname: 'bannerbot-public.s3.ap-south-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: '**.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
      },
      {
        protocol: 'https',
        hostname: 'bannerbot-public.s3.ap-south-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'groweasy.ai',
      },
      // Freepik images
      {
        protocol: 'https',
        hostname: 'img.b2bpic.net',
      },
      {
        protocol: 'https',
        hostname: 'img.freepik.com',
      },
      {
        protocol: 'https',
        hostname: 'designeasy-public.s3.amazonaws.com',
      },
    ],
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    return config;
  },
};

export default nextConfig;
